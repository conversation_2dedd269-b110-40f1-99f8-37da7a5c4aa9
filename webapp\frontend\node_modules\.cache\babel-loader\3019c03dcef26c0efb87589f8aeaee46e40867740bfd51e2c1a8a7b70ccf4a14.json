{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\nconst dateFormats = {\n  full: \"EEEE, dd MMMM yyyy\",\n  long: \"dd MMMM yyyy\",\n  medium: \"dd MMM yyyy\",\n  short: \"dd.MM.yyyy\"\n};\nconst timeFormats = {\n  full: \"HH:mm:ss zzzz\",\n  long: \"HH:mm:ss z\",\n  medium: \"HH:mm:ss\",\n  short: \"H:mm\"\n};\nconst dateTimeFormats = {\n  any: \"{{date}} {{time}}\"\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"any\"\n  })\n};", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "any", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/bg/_lib/formatLong.js"], "sourcesContent": ["import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\nconst dateFormats = {\n  full: \"EEEE, dd MMMM yyyy\",\n  long: \"dd MMMM yyyy\",\n  medium: \"dd MMM yyyy\",\n  short: \"dd.MM.yyyy\",\n};\n\nconst timeFormats = {\n  full: \"HH:mm:ss zzzz\",\n  long: \"HH:mm:ss z\",\n  medium: \"HH:mm:ss\",\n  short: \"H:mm\",\n};\n\nconst dateTimeFormats = {\n  any: \"{{date}} {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"any\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iCAAiC;AAEnE,MAAMC,WAAW,GAAG;EAClBC,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAE,aAAa;EACrBC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,WAAW,GAAG;EAClBJ,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE;AACT,CAAC;AAED,MAAME,eAAe,GAAG;EACtBC,GAAG,EAAE;AACP,CAAC;AAED,OAAO,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAEV,iBAAiB,CAAC;IACtBW,OAAO,EAAEV,WAAW;IACpBW,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,IAAI,EAAEb,iBAAiB,CAAC;IACtBW,OAAO,EAAEL,WAAW;IACpBM,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFE,QAAQ,EAAEd,iBAAiB,CAAC;IAC1BW,OAAO,EAAEJ,eAAe;IACxBK,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}