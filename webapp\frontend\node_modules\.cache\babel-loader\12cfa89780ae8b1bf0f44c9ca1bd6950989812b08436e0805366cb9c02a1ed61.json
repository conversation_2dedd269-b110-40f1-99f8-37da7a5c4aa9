{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1621 - #1630\nconst eraValues = {\n  narrow: [\"ઈસપૂ\", \"ઈસ\"],\n  abbreviated: [\"ઈ.સ.પૂર્વે\", \"ઈ.સ.\"],\n  wide: [\"ઈસવીસન પૂર્વે\", \"ઈસવીસન\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1631 - #1654\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1લો ત્રિમાસ\", \"2જો ત્રિમાસ\", \"3જો ત્રિમાસ\", \"4થો ત્રિમાસ\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1655 - #1726\nconst monthValues = {\n  narrow: [\"જા\", \"ફે\", \"મા\", \"એ\", \"મે\", \"જૂ\", \"જુ\", \"ઓ\", \"સ\", \"ઓ\", \"ન\", \"ડિ\"],\n  abbreviated: [\"જાન્યુ\", \"ફેબ્રુ\", \"માર્ચ\", \"એપ્રિલ\", \"મે\", \"જૂન\", \"જુલાઈ\", \"ઑગસ્ટ\", \"સપ્ટે\", \"ઓક્ટો\", \"નવે\", \"ડિસે\"],\n  wide: [\"જાન્યુઆરી\", \"ફેબ્રુઆરી\", \"માર્ચ\", \"એપ્રિલ\", \"મે\", \"જૂન\", \"જુલાઇ\", \"ઓગસ્ટ\", \"સપ્ટેમ્બર\", \"ઓક્ટોબર\", \"નવેમ્બર\", \"ડિસેમ્બર\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1727 - #1768\nconst dayValues = {\n  narrow: [\"ર\", \"સો\", \"મં\", \"બુ\", \"ગુ\", \"શુ\", \"શ\"],\n  short: [\"ર\", \"સો\", \"મં\", \"બુ\", \"ગુ\", \"શુ\", \"શ\"],\n  abbreviated: [\"રવિ\", \"સોમ\", \"મંગળ\", \"બુધ\", \"ગુરુ\", \"શુક્ર\", \"શનિ\"],\n  wide: [\"રવિવાર\" /* Sunday */, \"સોમવાર\" /* Monday */, \"મંગળવાર\" /* Tuesday */, \"બુધવાર\" /* Wednesday */, \"ગુરુવાર\" /* Thursday */, \"શુક્રવાર\" /* Friday */, \"શનિવાર\" /* Saturday */]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1783 - #1824\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"મ.રાત્રિ\",\n    noon: \"બ.\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"​મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"​મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"મ.રાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"​મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/gu/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1621 - #1630\nconst eraValues = {\n  narrow: [\"ઈસપૂ\", \"ઈસ\"],\n  abbreviated: [\"ઈ.સ.પૂર્વે\", \"ઈ.સ.\"],\n  wide: [\"ઈસવીસન પૂર્વે\", \"ઈસવીસન\"],\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1631 - #1654\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1લો ત્રિમાસ\", \"2જો ત્રિમાસ\", \"3જો ત્રિમાસ\", \"4થો ત્રિમાસ\"],\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1655 - #1726\nconst monthValues = {\n  narrow: [\"જા\", \"ફે\", \"મા\", \"એ\", \"મે\", \"જૂ\", \"જુ\", \"ઓ\", \"સ\", \"ઓ\", \"ન\", \"ડિ\"],\n\n  abbreviated: [\n    \"જાન્યુ\",\n    \"ફેબ્રુ\",\n    \"માર્ચ\",\n    \"એપ્રિલ\",\n    \"મે\",\n    \"જૂન\",\n    \"જુલાઈ\",\n    \"ઑગસ્ટ\",\n    \"સપ્ટે\",\n    \"ઓક્ટો\",\n    \"નવે\",\n    \"ડિસે\",\n  ],\n\n  wide: [\n    \"જાન્યુઆરી\",\n    \"ફેબ્રુઆરી\",\n    \"માર્ચ\",\n    \"એપ્રિલ\",\n    \"મે\",\n    \"જૂન\",\n    \"જુલાઇ\",\n    \"ઓગસ્ટ\",\n    \"સપ્ટેમ્બર\",\n    \"ઓક્ટોબર\",\n    \"નવેમ્બર\",\n    \"ડિસેમ્બર\",\n  ],\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1727 - #1768\nconst dayValues = {\n  narrow: [\"ર\", \"સો\", \"મં\", \"બુ\", \"ગુ\", \"શુ\", \"શ\"],\n  short: [\"ર\", \"સો\", \"મં\", \"બુ\", \"ગુ\", \"શુ\", \"શ\"],\n  abbreviated: [\"રવિ\", \"સોમ\", \"મંગળ\", \"બુધ\", \"ગુરુ\", \"શુક્ર\", \"શનિ\"],\n  wide: [\n    \"રવિવાર\" /* Sunday */,\n    \"સોમવાર\" /* Monday */,\n    \"મંગળવાર\" /* Tuesday */,\n    \"બુધવાર\" /* Wednesday */,\n    \"ગુરુવાર\" /* Thursday */,\n    \"શુક્રવાર\" /* Friday */,\n    \"શનિવાર\" /* Saturday */,\n  ],\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1783 - #1824\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"મ.રાત્રિ\",\n    noon: \"બ.\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"​મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\",\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"​મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"મ.રાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\",\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"​મધ્યરાત્રિ\",\n    noon: \"બપોરે\",\n    morning: \"સવારે\",\n    afternoon: \"બપોરે\",\n    evening: \"સાંજે\",\n    night: \"રાત્રે\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;;AAE/D;AACA;AACA,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;EACtBC,WAAW,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;EACnCC,IAAI,EAAE,CAAC,eAAe,EAAE,QAAQ;AAClC,CAAC;;AAED;AACA;AACA,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAE3EC,WAAW,EAAE,CACX,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,KAAK,EACL,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,WAAW,EACX,WAAW,EACX,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,OAAO,EACP,OAAO,EACP,WAAW,EACX,SAAS,EACT,SAAS,EACT,UAAU;AAEd,CAAC;;AAED;AACA;AACA,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAChDM,KAAK,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAC/CL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EAClEC,IAAI,EAAE,CACJ,QAAQ,CAAC,cACT,QAAQ,CAAC,cACT,SAAS,CAAC,eACV,QAAQ,CAAC,iBACT,SAAS,CAAC,gBACV,UAAU,CAAC,cACX,QAAQ,CAAC;AAEb,CAAC;;AAED;AACA;AACA,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBJ,aAAa;EAEbK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}