# PIANO AGGIORNAMENTO SISTEMA CERTIFICAZIONE CAVI
## Conformità CEI 64-8 e Normative Internazionali

### 📊 ANALISI GAP SISTEMA ATTUALE

#### **STRUTTURA ATTUALE**
- ✅ CertificazioniCavi (certificati singoli)
- ✅ StrumentiCertificati (strumenti base)
- ✅ Cavi (anagrafica completa)
- ✅ parco_cavi (inventario bobine)

#### **CAMPI ATTUALI CERTIFICAZIONE**
- ✅ numero_certificato, data_certificazione
- ✅ id_operatore, strumento_utilizzato, id_strumento
- ✅ lunghezza_misurata
- ⚠️ valore_continuita, valore_isolamento, valore_resistenza (troppo semplificati)
- ✅ note, percorso_certificato, percorso_foto

### 🎯 INTEGRAZIONI NECESSARIE

#### **1. NUOVE TABELLE**

##### **A) RapportiGeneraliCollaudo**
```sql
CREATE TABLE RapportiGeneraliCollaudo (
    id_rapporto SERIAL PRIMARY KEY,
    id_cantiere INTEGER NOT NULL,
    numero_rapporto TEXT NOT NULL UNIQUE,
    data_rapporto DATE NOT NULL,
    
    -- Dati Progetto/Commessa
    nome_progetto TEXT,
    codice_progetto TEXT,
    cliente_finale TEXT,
    localita_impianto TEXT,
    societa_installatrice TEXT,
    societa_responsabile_prove TEXT,
    data_inizio_collaudo DATE,
    data_fine_collaudo DATE,
    
    -- Riferimenti Normativi
    normative_applicate TEXT[], -- Array di normative
    documentazione_progetto TEXT[], -- Array documenti
    
    -- Scopo e Ambito
    scopo_rapporto TEXT,
    ambito_collaudo TEXT,
    
    -- Condizioni Ambientali
    temperatura_ambiente REAL,
    umidita_ambiente REAL,
    
    -- Riepilogo
    numero_cavi_totali INTEGER DEFAULT 0,
    numero_cavi_conformi INTEGER DEFAULT 0,
    numero_cavi_non_conformi INTEGER DEFAULT 0,
    
    -- Personale
    responsabile_tecnico TEXT,
    rappresentante_cliente TEXT,
    
    -- Stato e Conclusioni
    stato_rapporto TEXT DEFAULT 'BOZZA', -- BOZZA, COMPLETATO, APPROVATO
    conclusioni TEXT,
    dichiarazione_conformita BOOLEAN DEFAULT FALSE,
    
    -- Metadati
    timestamp_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    timestamp_modifica TIMESTAMP,
    
    FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE
);
```

##### **B) ProveDettagliate**
```sql
CREATE TABLE ProveDettagliate (
    id_prova SERIAL PRIMARY KEY,
    id_certificazione INTEGER NOT NULL,
    tipo_prova TEXT NOT NULL, -- 'ESAME_VISTA', 'CONTINUITA', 'ISOLAMENTO', 'RIGIDITA', 'SEQUENZA_FASI', 'ALTRO'
    
    -- Dati Comuni
    data_prova TIMESTAMP,
    operatore TEXT,
    condizioni_ambientali JSONB, -- {temperatura: 20, umidita: 60}
    
    -- Risultati Specifici per Tipo
    risultati JSONB, -- Struttura flessibile per ogni tipo di prova
    valori_misurati JSONB, -- Valori numerici misurati
    valori_attesi JSONB, -- Valori attesi/limite
    
    -- Esito
    esito TEXT NOT NULL, -- 'CONFORME', 'NON_CONFORME', 'N_A'
    note_prova TEXT,
    
    FOREIGN KEY (id_certificazione) REFERENCES CertificazioniCavi(id_certificazione) ON DELETE CASCADE
);
```

##### **C) NonConformita**
```sql
CREATE TABLE NonConformita (
    id_nc SERIAL PRIMARY KEY,
    id_certificazione INTEGER,
    id_rapporto INTEGER,
    
    -- Identificazione NC
    codice_nc TEXT NOT NULL UNIQUE,
    data_rilevazione DATE NOT NULL,
    tipo_nc TEXT, -- 'CRITICA', 'MAGGIORE', 'MINORE'
    
    -- Descrizione
    descrizione TEXT NOT NULL,
    riferimento_cavo TEXT,
    riferimento_prova TEXT,
    
    -- Azioni Correttive
    azione_correttiva TEXT,
    responsabile_azione TEXT,
    data_scadenza_azione DATE,
    data_completamento_azione DATE,
    
    -- Ri-verifica
    esito_riverifica TEXT, -- 'CONFORME', 'NON_CONFORME', 'PENDING'
    note_riverifica TEXT,
    
    -- Stato
    stato_nc TEXT DEFAULT 'APERTA', -- 'APERTA', 'IN_CORSO', 'CHIUSA'
    
    FOREIGN KEY (id_certificazione) REFERENCES CertificazioniCavi(id_certificazione) ON DELETE SET NULL,
    FOREIGN KEY (id_rapporto) REFERENCES RapportiGeneraliCollaudo(id_rapporto) ON DELETE SET NULL
);
```

##### **D) StrumentiDettagliati (Estensione)**
```sql
-- Aggiungere campi alla tabella esistente StrumentiCertificati
ALTER TABLE StrumentiCertificati ADD COLUMN IF NOT EXISTS tipo_strumento TEXT; -- 'MEGGER', 'MULTIMETRO', 'SEQUENCER', 'MICROHMETRO'
ALTER TABLE StrumentiCertificati ADD COLUMN IF NOT EXISTS ente_certificatore TEXT;
ALTER TABLE StrumentiCertificati ADD COLUMN IF NOT EXISTS range_misura TEXT;
ALTER TABLE StrumentiCertificati ADD COLUMN IF NOT EXISTS precisione TEXT;
ALTER TABLE StrumentiCertificati ADD COLUMN IF NOT EXISTS stato_strumento TEXT DEFAULT 'ATTIVO'; -- 'ATTIVO', 'SCADUTO', 'FUORI_SERVIZIO'
```

#### **2. ESTENSIONI TABELLE ESISTENTI**

##### **A) CertificazioniCavi - Campi Aggiuntivi**
```sql
ALTER TABLE CertificazioniCavi ADD COLUMN IF NOT EXISTS id_rapporto INTEGER;
ALTER TABLE CertificazioniCavi ADD COLUMN IF NOT EXISTS tipo_certificato TEXT DEFAULT 'SINGOLO'; -- 'SINGOLO', 'GRUPPO'
ALTER TABLE CertificazioniCavi ADD COLUMN IF NOT EXISTS stato_certificato TEXT DEFAULT 'BOZZA'; -- 'BOZZA', 'COMPLETATO', 'APPROVATO'
ALTER TABLE CertificazioniCavi ADD COLUMN IF NOT EXISTS designazione_funzionale TEXT;
ALTER TABLE CertificazioniCavi ADD COLUMN IF NOT EXISTS tensione_nominale TEXT;
ALTER TABLE CertificazioniCavi ADD COLUMN IF NOT EXISTS tensione_prova_isolamento INTEGER; -- in Volt
ALTER TABLE CertificazioniCavi ADD COLUMN IF NOT EXISTS durata_prova_isolamento INTEGER; -- in secondi
ALTER TABLE CertificazioniCavi ADD COLUMN IF NOT EXISTS valore_minimo_isolamento REAL; -- in MΩ
ALTER TABLE CertificazioniCavi ADD COLUMN IF NOT EXISTS temperatura_prova REAL;
ALTER TABLE CertificazioniCavi ADD COLUMN IF NOT EXISTS umidita_prova REAL;
ALTER TABLE CertificazioniCavi ADD COLUMN IF NOT EXISTS esito_complessivo TEXT; -- 'CONFORME', 'NON_CONFORME', 'CONFORME_CON_OSSERVAZIONI'

-- Aggiungere foreign key per rapporto
ALTER TABLE CertificazioniCavi ADD CONSTRAINT fk_rapporto 
    FOREIGN KEY (id_rapporto) REFERENCES RapportiGeneraliCollaudo(id_rapporto) ON DELETE SET NULL;
```

### 🚀 PIANO IMPLEMENTAZIONE

#### **FASE 1: Database (Settimana 1)**
1. ✅ Creazione nuove tabelle
2. ✅ Estensione tabelle esistenti  
3. ✅ Migrazione dati esistenti
4. ✅ Test integrità referenziale

#### **FASE 2: Backend Models & API (Settimana 2)**
1. ✅ Nuovi modelli SQLAlchemy
2. ✅ Schemi Pydantic
3. ✅ Endpoint API per rapporti generali
4. ✅ Endpoint API per prove dettagliate
5. ✅ Endpoint API per non conformità

#### **FASE 3: Frontend Interfaces (Settimana 3)**
1. ✅ Interfaccia Rapporto Generale
2. ✅ Wizard Prove Dettagliate
3. ✅ Gestione Non Conformità
4. ✅ Dashboard Avanzamento Collaudi

#### **FASE 4: Generazione PDF (Settimana 4)**
1. ✅ Template PDF Rapporto Generale
2. ✅ Template PDF Certificato Singolo
3. ✅ Integrazione dati normativi
4. ✅ Workflow approvazione digitale

#### **FASE 5: Testing & Deployment (Settimana 5)**
1. ✅ Test funzionali completi
2. ✅ Test generazione documenti
3. ✅ Validazione conformità CEI 64-8
4. ✅ Deploy produzione

#### **3. NUOVI MODELLI PYTHON**

##### **A) Modello RapportoGeneraleCollaudo**
```python
# webapp/backend/models/rapporto_generale_collaudo.py
from sqlalchemy import Column, Integer, String, Date, Text, Boolean, REAL, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from backend.database import Base

class RapportoGeneraleCollaudo(Base):
    __tablename__ = "rapportigeneralicollaudo"

    id_rapporto = Column(Integer, primary_key=True, index=True)
    id_cantiere = Column(Integer, ForeignKey("cantieri.id_cantiere"), nullable=False)
    numero_rapporto = Column(String, nullable=False, unique=True)
    data_rapporto = Column(Date, nullable=False)

    # Dati Progetto
    nome_progetto = Column(String, nullable=True)
    codice_progetto = Column(String, nullable=True)
    cliente_finale = Column(String, nullable=True)
    localita_impianto = Column(String, nullable=True)
    societa_installatrice = Column(String, nullable=True)
    societa_responsabile_prove = Column(String, nullable=True)
    data_inizio_collaudo = Column(Date, nullable=True)
    data_fine_collaudo = Column(Date, nullable=True)

    # Riferimenti Normativi (JSON per flessibilità)
    normative_applicate = Column(Text, nullable=True)  # JSON string
    documentazione_progetto = Column(Text, nullable=True)  # JSON string

    # Scopo e Ambito
    scopo_rapporto = Column(Text, nullable=True)
    ambito_collaudo = Column(Text, nullable=True)

    # Condizioni Ambientali
    temperatura_ambiente = Column(REAL, nullable=True)
    umidita_ambiente = Column(REAL, nullable=True)

    # Riepilogo
    numero_cavi_totali = Column(Integer, default=0)
    numero_cavi_conformi = Column(Integer, default=0)
    numero_cavi_non_conformi = Column(Integer, default=0)

    # Personale
    responsabile_tecnico = Column(String, nullable=True)
    rappresentante_cliente = Column(String, nullable=True)

    # Stato e Conclusioni
    stato_rapporto = Column(String, default='BOZZA')
    conclusioni = Column(Text, nullable=True)
    dichiarazione_conformita = Column(Boolean, default=False)

    # Metadati
    timestamp_creazione = Column(DateTime, default=func.now())
    timestamp_modifica = Column(DateTime, nullable=True)

    # Relazioni
    cantiere = relationship("Cantiere", backref="rapporti_collaudo")
    certificazioni = relationship("CertificazioneCavo", backref="rapporto_generale")
```

##### **B) Modello ProvaDettagliata**
```python
# webapp/backend/models/prova_dettagliata.py
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from backend.database import Base

class ProvaDettagliata(Base):
    __tablename__ = "provedettagliate"

    id_prova = Column(Integer, primary_key=True, index=True)
    id_certificazione = Column(Integer, ForeignKey("certificazionicavi.id_certificazione"), nullable=False)
    tipo_prova = Column(String, nullable=False)  # 'ESAME_VISTA', 'CONTINUITA', 'ISOLAMENTO', etc.

    # Dati Comuni
    data_prova = Column(DateTime, nullable=True)
    operatore = Column(String, nullable=True)
    condizioni_ambientali = Column(JSONB, nullable=True)  # {temperatura: 20, umidita: 60}

    # Risultati Specifici per Tipo
    risultati = Column(JSONB, nullable=True)  # Struttura flessibile per ogni tipo di prova
    valori_misurati = Column(JSONB, nullable=True)  # Valori numerici misurati
    valori_attesi = Column(JSONB, nullable=True)  # Valori attesi/limite

    # Esito
    esito = Column(String, nullable=False)  # 'CONFORME', 'NON_CONFORME', 'N_A'
    note_prova = Column(Text, nullable=True)

    # Relazioni
    certificazione = relationship("CertificazioneCavo", backref="prove_dettagliate")
```

##### **C) Modello NonConformita**
```python
# webapp/backend/models/non_conformita.py
from sqlalchemy import Column, Integer, String, Text, Date, ForeignKey
from sqlalchemy.orm import relationship
from backend.database import Base

class NonConformita(Base):
    __tablename__ = "nonconformita"

    id_nc = Column(Integer, primary_key=True, index=True)
    id_certificazione = Column(Integer, ForeignKey("certificazionicavi.id_certificazione"), nullable=True)
    id_rapporto = Column(Integer, ForeignKey("rapportigeneralicollaudo.id_rapporto"), nullable=True)

    # Identificazione NC
    codice_nc = Column(String, nullable=False, unique=True)
    data_rilevazione = Column(Date, nullable=False)
    tipo_nc = Column(String, nullable=True)  # 'CRITICA', 'MAGGIORE', 'MINORE'

    # Descrizione
    descrizione = Column(Text, nullable=False)
    riferimento_cavo = Column(String, nullable=True)
    riferimento_prova = Column(String, nullable=True)

    # Azioni Correttive
    azione_correttiva = Column(Text, nullable=True)
    responsabile_azione = Column(String, nullable=True)
    data_scadenza_azione = Column(Date, nullable=True)
    data_completamento_azione = Column(Date, nullable=True)

    # Ri-verifica
    esito_riverifica = Column(String, nullable=True)  # 'CONFORME', 'NON_CONFORME', 'PENDING'
    note_riverifica = Column(Text, nullable=True)

    # Stato
    stato_nc = Column(String, default='APERTA')  # 'APERTA', 'IN_CORSO', 'CHIUSA'

    # Relazioni
    certificazione = relationship("CertificazioneCavo", backref="non_conformita")
    rapporto = relationship("RapportoGeneraleCollaudo", backref="non_conformita")
```

#### **4. SCHEMA PROVE DETTAGLIATE**

##### **Struttura JSONB per Risultati Prove**

**ESAME A VISTA:**
```json
{
  "conformita_percorso": "OK|NON_OK|N_A",
  "correttezza_tipo_cavo": "OK|NON_OK|N_A",
  "integrita_guaina": "OK|NON_OK|N_A",
  "correttezza_collegamenti": "OK|NON_OK|N_A",
  "correttezza_siglatura": "OK|NON_OK|N_A",
  "correttezza_supporti": "OK|NON_OK|N_A",
  "note_esame": "testo libero"
}
```

**CONTINUITÀ CONDUTTORI:**
```json
{
  "conduttori": [
    {
      "id_conduttore": "L1",
      "collegamento_atteso": "QE-01/X1/1 - M-101/U",
      "continuita_rilevata": "OK|INTERROTTO",
      "resistenza_ohm": 0.15,
      "resistenza_attesa": 0.20
    },
    {
      "id_conduttore": "L2",
      "collegamento_atteso": "QE-01/X1/2 - M-101/V",
      "continuita_rilevata": "OK|INTERROTTO",
      "resistenza_ohm": 0.16,
      "resistenza_attesa": 0.20
    }
  ]
}
```

**ISOLAMENTO:**
```json
{
  "tensione_prova_v": 1000,
  "durata_prova_sec": 60,
  "valore_minimo_mohm": 1.0,
  "misure": [
    {"tra": "L1-L2", "valore_mohm": 500.0},
    {"tra": "L1-L3", "valore_mohm": 450.0},
    {"tra": "L1-N", "valore_mohm": 480.0},
    {"tra": "L1-PE", "valore_mohm": 520.0}
  ]
}
```

**SEQUENZA FASI:**
```json
{
  "sequenza_attesa": "R-S-T",
  "sequenza_rilevata": "R-S-T",
  "rotazione": "DESTRORSA|SINISTRORSA",
  "tensioni": [
    {"fase": "R-S", "tensione_v": 400},
    {"fase": "S-T", "tensione_v": 400},
    {"fase": "T-R", "tensione_v": 400}
  ]
}
```

### 📋 CHECKLIST CONFORMITÀ CEI 64-8

- [ ] **Rapporto Generale** con tutti i dati richiesti
- [ ] **Certificati Singoli** con prove dettagliate
- [ ] **Riferimenti Normativi** tracciabili
- [ ] **Strumentazione** con taratura certificata
- [ ] **Condizioni Ambientali** registrate
- [ ] **Non Conformità** gestite strutturalmente
- [ ] **Workflow Approvazione** con firme multiple
- [ ] **Generazione PDF** professionale
- [ ] **Tracciabilità** completa delle prove
- [ ] **Storico** modifiche e ri-verifiche

### 🎯 PROSSIMI PASSI

**Vuoi che proceda con:**
1. **Implementazione Database** - Creazione script SQL per nuove tabelle
2. **Modelli Backend** - Creazione modelli Python e API endpoints
3. **Interfacce Frontend** - Nuove pagine per gestione rapporti e prove
4. **Generazione PDF** - Template professionali conformi CEI 64-8

**Quale fase preferisci iniziare per prima?**
