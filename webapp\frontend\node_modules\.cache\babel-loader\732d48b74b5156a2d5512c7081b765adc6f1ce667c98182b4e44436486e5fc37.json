{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\BobineFilterableTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, IconButton, Tooltip } from '@mui/material';\nimport { Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Add as AddIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport { REEL_STATES, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per visualizzare la lista delle bobine con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.bobine - Lista delle bobine da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare una bobina\n * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare una bobina\n * @param {Function} props.onViewHistory - Funzione chiamata quando si vuole visualizzare lo storico di una bobina\n * @param {Function} props.onQuickAdd - Funzione chiamata quando si vuole aggiungere rapidamente cavi a una bobina\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BobineFilterableTable = ({\n  bobine = [],\n  loading = false,\n  onFilteredDataChange = null,\n  onEdit = null,\n  onDelete = null,\n  onViewHistory = null,\n  onQuickAdd = null\n}) => {\n  _s();\n  const [filteredBobine, setFilteredBobine] = useState(bobine);\n\n  // Aggiorna i dati filtrati quando cambiano le bobine\n  useEffect(() => {\n    setFilteredBobine(bobine);\n  }, [bobine]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = data => {\n    setFilteredBobine(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Definizione delle colonne\n  const columns = [{\n    field: 'numero_bobina',\n    headerName: 'ID Bobina',\n    dataType: 'text',\n    width: 120,\n    headerStyle: {\n      fontWeight: 'bold'\n    },\n    renderCell: row => /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      sx: {\n        fontSize: '0.95rem',\n        fontWeight: 600\n      },\n      children: row.numero_bobina\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'utility',\n    headerName: 'Utility',\n    dataType: 'text'\n  }, {\n    field: 'tipologia',\n    headerName: 'Tipologia',\n    dataType: 'text'\n  },\n  // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n  {\n    field: 'sezione',\n    headerName: 'Formazione',\n    dataType: 'text',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    }\n  }, {\n    field: 'metri_totali',\n    headerName: 'Metri Totali',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metri_totali ? row.metri_totali.toFixed(1) : '0'\n  }, {\n    field: 'metri_residui',\n    headerName: 'Metri Residui',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metri_residui ? row.metri_residui.toFixed(1) : '0'\n  }, {\n    field: 'stato_bobina',\n    headerName: 'Stato',\n    dataType: 'text',\n    renderCell: row => {\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.stato_bobina || 'N/D',\n        size: \"small\",\n        color: getReelStateColor(row.stato_bobina),\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    field: 'ubicazione_bobina',\n    headerName: 'Ubicazione',\n    dataType: 'text'\n  }, {\n    field: 'fornitore',\n    headerName: 'Fornitore',\n    dataType: 'text'\n  }, {\n    field: 'n_DDT',\n    headerName: 'N° DDT',\n    dataType: 'text'\n  }, {\n    field: 'data_DDT',\n    headerName: 'Data DDT',\n    dataType: 'text'\n  }, {\n    field: 'actions',\n    headerName: 'Azioni',\n    disableFilter: true,\n    disableSort: true,\n    align: 'center',\n    renderCell: row => /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center'\n      },\n      children: [onEdit && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onEdit(row),\n        title: \"Modifica bobina\",\n        color: \"primary\",\n        children: /*#__PURE__*/_jsxDEV(EditIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 13\n      }, this), onDelete && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onDelete(row),\n        title: \"Elimina bobina\",\n        color: \"error\",\n        disabled: row.stato_bobina !== 'Disponibile' || row.metri_residui !== row.metri_totali,\n        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 13\n      }, this), onViewHistory && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onViewHistory(row),\n        title: \"Visualizza storico utilizzo\",\n        color: \"info\",\n        children: /*#__PURE__*/_jsxDEV(HistoryIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 13\n      }, this), onQuickAdd && row.stato_bobina !== 'Terminata' && row.stato_bobina !== 'Over' && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Aggiungi cavi a questa bobina\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: () => onQuickAdd(row),\n          color: \"success\",\n          children: /*#__PURE__*/_jsxDEV(AddIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_bobina === REEL_STATES.DISPONIBILE) bgColor = 'rgba(76, 175, 80, 0.1)';else if (row.stato_bobina === REEL_STATES.IN_USO) bgColor = 'rgba(255, 152, 0, 0.1)';else if (row.stato_bobina === REEL_STATES.TERMINATA) bgColor = 'rgba(255, 152, 0, 0.2)';else if (row.stato_bobina === REEL_STATES.OVER) bgColor = 'rgba(244, 67, 54, 0.1)';\n    return /*#__PURE__*/_jsxDEV(TableRow, {\n      sx: {\n        backgroundColor: bgColor,\n        '&:hover': {\n          backgroundColor: 'rgba(0, 0, 0, 0.04)'\n        }\n      },\n      children: columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n        align: column.align || 'left',\n        sx: column.cellStyle,\n        children: column.renderCell ? column.renderCell(row) : row[column.field]\n      }, column.field, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this))\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredBobine.length) return null;\n    const totalBobine = filteredBobine.length;\n    const disponibili = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.DISPONIBILE).length;\n    const inUso = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.IN_USO).length;\n    const terminate = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.TERMINATA).length;\n    const over = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.OVER).length;\n    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0);\n    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0);\n    const metriUtilizzati = metriTotali - metriResidui;\n    const percentualeUtilizzo = metriTotali ? Math.round(metriUtilizzati / metriTotali * 100) : 0;\n    return {\n      totalBobine,\n      disponibili,\n      inUso,\n      terminate,\n      over,\n      metriTotali,\n      metriResidui,\n      metriUtilizzati,\n      percentualeUtilizzo\n    };\n  };\n  const stats = calculateStats();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [stats && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        p: 2,\n        bgcolor: 'background.paper',\n        borderRadius: 1,\n        boxShadow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: [\"Statistiche (\", filteredBobine.length, \" bobine visualizzate)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Utilizzo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.percentualeUtilizzo, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Disponibili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"success.main\",\n            children: stats.disponibili\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"In uso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"warning.main\",\n            children: stats.inUso\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Terminate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"warning.main\",\n            children: stats.terminate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Over\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"error.main\",\n            children: stats.over\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriTotali.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri residui\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriResidui.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri utilizzati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriUtilizzati.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n      data: bobine,\n      columns: columns,\n      onFilteredDataChange: handleFilteredDataChange,\n      loading: loading,\n      emptyMessage: \"Nessuna bobina disponibile\",\n      renderRow: renderRow\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n_s(BobineFilterableTable, \"f3TlITpreTFylm3or0YT9wPW5MI=\");\n_c = BobineFilterableTable;\nexport default BobineFilterableTable;\nvar _c;\n$RefreshReg$(_c, \"BobineFilterableTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "TableRow", "TableCell", "IconButton", "<PERSON><PERSON><PERSON>", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Add", "AddIcon", "FilterableTable", "REEL_STATES", "getReelStateColor", "jsxDEV", "_jsxDEV", "BobineFilterableTable", "bobine", "loading", "onFilteredDataChange", "onEdit", "onDelete", "onViewHistory", "onQuickAdd", "_s", "filteredBobine", "setFilteredBobine", "handleFilteredDataChange", "data", "columns", "field", "headerName", "dataType", "width", "headerStyle", "fontWeight", "renderCell", "row", "variant", "sx", "fontSize", "children", "numero_bobina", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "align", "cellStyle", "textAlign", "metri_totali", "toFixed", "metri_residui", "label", "stato_bobina", "size", "color", "disableFilter", "disableSort", "display", "justifyContent", "onClick", "title", "disabled", "renderRow", "index", "bgColor", "DISPONIBILE", "IN_USO", "TERMINATA", "OVER", "backgroundColor", "map", "column", "calculateStats", "length", "totalBobine", "disponibili", "filter", "b", "inUso", "terminate", "over", "metriTotali", "reduce", "sum", "metriResidui", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "percentuale<PERSON><PERSON><PERSON><PERSON>", "Math", "round", "stats", "mb", "p", "bgcolor", "borderRadius", "boxShadow", "gutterBottom", "flexWrap", "gap", "emptyMessage", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/BobineFilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, IconButton, Tooltip } from '@mui/material';\nimport { Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Add as AddIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport { REEL_STATES, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per visualizzare la lista delle bobine con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.bobine - Lista delle bobine da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare una bobina\n * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare una bobina\n * @param {Function} props.onViewHistory - Funzione chiamata quando si vuole visualizzare lo storico di una bobina\n * @param {Function} props.onQuickAdd - Funzione chiamata quando si vuole aggiungere rapidamente cavi a una bobina\n */\nconst BobineFilterableTable = ({\n  bobine = [],\n  loading = false,\n  onFilteredDataChange = null,\n  onEdit = null,\n  onDelete = null,\n  onViewHistory = null,\n  onQuickAdd = null\n}) => {\n  const [filteredBobine, setFilteredBobine] = useState(bobine);\n\n  // Aggiorna i dati filtrati quando cambiano le bobine\n  useEffect(() => {\n    setFilteredBobine(bobine);\n  }, [bobine]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = (data) => {\n    setFilteredBobine(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Definizione delle colonne\n  const columns = [\n    {\n      field: 'numero_bobina',\n      headerName: 'ID Bobina',\n      dataType: 'text',\n      width: 120,\n      headerStyle: { fontWeight: 'bold' },\n      renderCell: (row) => (\n        <Typography variant=\"body1\" sx={{ fontSize: '0.95rem', fontWeight: 600 }}>\n          {row.numero_bobina}\n        </Typography>\n      )\n    },\n    {\n      field: 'utility',\n      headerName: 'Utility',\n      dataType: 'text'\n    },\n    {\n      field: 'tipologia',\n      headerName: 'Tipologia',\n      dataType: 'text'\n    },\n    // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n    {\n      field: 'sezione',\n      headerName: 'Formazione',\n      dataType: 'text',\n      align: 'right',\n      cellStyle: { textAlign: 'right' }\n    },\n    {\n      field: 'metri_totali',\n      headerName: 'Metri Totali',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_totali ? row.metri_totali.toFixed(1) : '0'\n    },\n    {\n      field: 'metri_residui',\n      headerName: 'Metri Residui',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_residui ? row.metri_residui.toFixed(1) : '0'\n    },\n    {\n      field: 'stato_bobina',\n      headerName: 'Stato',\n      dataType: 'text',\n      renderCell: (row) => {\n        return (\n          <Chip\n            label={row.stato_bobina || 'N/D'}\n            size=\"small\"\n            color={getReelStateColor(row.stato_bobina)}\n            variant=\"outlined\"\n          />\n        );\n      }\n    },\n    {\n      field: 'ubicazione_bobina',\n      headerName: 'Ubicazione',\n      dataType: 'text'\n    },\n    {\n      field: 'fornitore',\n      headerName: 'Fornitore',\n      dataType: 'text'\n    },\n    {\n      field: 'n_DDT',\n      headerName: 'N° DDT',\n      dataType: 'text'\n    },\n    {\n      field: 'data_DDT',\n      headerName: 'Data DDT',\n      dataType: 'text'\n    },\n    {\n      field: 'actions',\n      headerName: 'Azioni',\n      disableFilter: true,\n      disableSort: true,\n      align: 'center',\n      renderCell: (row) => (\n        <Box sx={{ display: 'flex', justifyContent: 'center' }}>\n          {onEdit && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onEdit(row)}\n              title=\"Modifica bobina\"\n              color=\"primary\"\n            >\n              <EditIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onDelete && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onDelete(row)}\n              title=\"Elimina bobina\"\n              color=\"error\"\n              disabled={row.stato_bobina !== 'Disponibile' || row.metri_residui !== row.metri_totali}\n            >\n              <DeleteIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onViewHistory && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onViewHistory(row)}\n              title=\"Visualizza storico utilizzo\"\n              color=\"info\"\n            >\n              <HistoryIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onQuickAdd && row.stato_bobina !== 'Terminata' && row.stato_bobina !== 'Over' && (\n            <Tooltip title=\"Aggiungi cavi a questa bobina\">\n              <IconButton\n                size=\"small\"\n                onClick={() => onQuickAdd(row)}\n                color=\"success\"\n              >\n                <AddIcon fontSize=\"small\" />\n              </IconButton>\n            </Tooltip>\n          )}\n        </Box>\n      )\n    }\n  ];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_bobina === REEL_STATES.DISPONIBILE) bgColor = 'rgba(76, 175, 80, 0.1)';\n    else if (row.stato_bobina === REEL_STATES.IN_USO) bgColor = 'rgba(255, 152, 0, 0.1)';\n    else if (row.stato_bobina === REEL_STATES.TERMINATA) bgColor = 'rgba(255, 152, 0, 0.2)';\n    else if (row.stato_bobina === REEL_STATES.OVER) bgColor = 'rgba(244, 67, 54, 0.1)';\n\n    return (\n      <TableRow\n        key={index}\n        sx={{\n          backgroundColor: bgColor,\n          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            align={column.align || 'left'}\n            sx={column.cellStyle}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    );\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredBobine.length) return null;\n\n    const totalBobine = filteredBobine.length;\n    const disponibili = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.DISPONIBILE).length;\n    const inUso = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.IN_USO).length;\n    const terminate = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.TERMINATA).length;\n    const over = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.OVER).length;\n\n    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0);\n    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0);\n    const metriUtilizzati = metriTotali - metriResidui;\n\n    const percentualeUtilizzo = metriTotali ? Math.round((metriUtilizzati / metriTotali) * 100) : 0;\n\n    return {\n      totalBobine,\n      disponibili,\n      inUso,\n      terminate,\n      over,\n      metriTotali,\n      metriResidui,\n      metriUtilizzati,\n      percentualeUtilizzo\n    };\n  };\n\n  const stats = calculateStats();\n\n  return (\n    <Box>\n      {stats && (\n        <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Statistiche ({filteredBobine.length} bobine visualizzate)\n          </Typography>\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Utilizzo</Typography>\n              <Typography variant=\"h6\">{stats.percentualeUtilizzo}%</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Disponibili</Typography>\n              <Typography variant=\"h6\" color=\"success.main\">{stats.disponibili}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">In uso</Typography>\n              <Typography variant=\"h6\" color=\"warning.main\">{stats.inUso}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Terminate</Typography>\n              <Typography variant=\"h6\" color=\"warning.main\">{stats.terminate}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Over</Typography>\n              <Typography variant=\"h6\" color=\"error.main\">{stats.over}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri totali</Typography>\n              <Typography variant=\"h6\">{stats.metriTotali.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri residui</Typography>\n              <Typography variant=\"h6\">{stats.metriResidui.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri utilizzati</Typography>\n              <Typography variant=\"h6\">{stats.metriUtilizzati.toFixed(1)} m</Typography>\n            </Box>\n          </Box>\n        </Box>\n      )}\n\n      <FilterableTable\n        data={bobine}\n        columns={columns}\n        onFilteredDataChange={handleFilteredDataChange}\n        loading={loading}\n        emptyMessage=\"Nessuna bobina disponibile\"\n        renderRow={renderRow}\n      />\n    </Box>\n  );\n};\n\nexport default BobineFilterableTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,OAAO,QAAQ,eAAe;AAC/F,SAASC,IAAI,IAAIC,QAAQ,EAAEC,MAAM,IAAIC,UAAU,EAAEC,OAAO,IAAIC,WAAW,EAAEC,GAAG,IAAIC,OAAO,QAAQ,qBAAqB;AACpH,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,wBAAwB;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,SAAAC,MAAA,IAAAC,OAAA;AAYA,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,MAAM,GAAG,EAAE;EACXC,OAAO,GAAG,KAAK;EACfC,oBAAoB,GAAG,IAAI;EAC3BC,MAAM,GAAG,IAAI;EACbC,QAAQ,GAAG,IAAI;EACfC,aAAa,GAAG,IAAI;EACpBC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAACuB,MAAM,CAAC;;EAE5D;EACAtB,SAAS,CAAC,MAAM;IACd+B,iBAAiB,CAACT,MAAM,CAAC;EAC3B,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMU,wBAAwB,GAAIC,IAAI,IAAK;IACzCF,iBAAiB,CAACE,IAAI,CAAC;IACvB,IAAIT,oBAAoB,EAAE;MACxBA,oBAAoB,CAACS,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,GAAG;IACVC,WAAW,EAAE;MAAEC,UAAU,EAAE;IAAO,CAAC;IACnCC,UAAU,EAAGC,GAAG,iBACdtB,OAAA,CAAClB,UAAU;MAACyC,OAAO,EAAC,OAAO;MAACC,EAAE,EAAE;QAAEC,QAAQ,EAAE,SAAS;QAAEL,UAAU,EAAE;MAAI,CAAE;MAAAM,QAAA,EACtEJ,GAAG,CAACK;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEhB,CAAC,EACD;IACEhB,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE;EACZ,CAAC;EACD;EACA;IACEF,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,MAAM;IAChBe,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ;EAClC,CAAC,EACD;IACEnB,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,cAAc;IAC1BC,QAAQ,EAAE,QAAQ;IAClBe,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCb,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACa,YAAY,GAAGb,GAAG,CAACa,YAAY,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;EACxE,CAAC,EACD;IACErB,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE,QAAQ;IAClBe,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCb,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACe,aAAa,GAAGf,GAAG,CAACe,aAAa,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG;EAC1E,CAAC,EACD;IACErB,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,OAAO;IACnBC,QAAQ,EAAE,MAAM;IAChBI,UAAU,EAAGC,GAAG,IAAK;MACnB,oBACEtB,OAAA,CAACjB,IAAI;QACHuD,KAAK,EAAEhB,GAAG,CAACiB,YAAY,IAAI,KAAM;QACjCC,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE3C,iBAAiB,CAACwB,GAAG,CAACiB,YAAY,CAAE;QAC3ChB,OAAO,EAAC;MAAU;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEN;EACF,CAAC,EACD;IACEhB,KAAK,EAAE,mBAAmB;IAC1BC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,UAAU;IACjBC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,QAAQ;IACpB0B,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBX,KAAK,EAAE,QAAQ;IACfX,UAAU,EAAGC,GAAG,iBACdtB,OAAA,CAACnB,GAAG;MAAC2C,EAAE,EAAE;QAAEoB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAnB,QAAA,GACpDrB,MAAM,iBACLL,OAAA,CAACd,UAAU;QACTsD,IAAI,EAAC,OAAO;QACZM,OAAO,EAAEA,CAAA,KAAMzC,MAAM,CAACiB,GAAG,CAAE;QAC3ByB,KAAK,EAAC,iBAAiB;QACvBN,KAAK,EAAC,SAAS;QAAAf,QAAA,eAEf1B,OAAA,CAACX,QAAQ;UAACoC,QAAQ,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACb,EACAzB,QAAQ,iBACPN,OAAA,CAACd,UAAU;QACTsD,IAAI,EAAC,OAAO;QACZM,OAAO,EAAEA,CAAA,KAAMxC,QAAQ,CAACgB,GAAG,CAAE;QAC7ByB,KAAK,EAAC,gBAAgB;QACtBN,KAAK,EAAC,OAAO;QACbO,QAAQ,EAAE1B,GAAG,CAACiB,YAAY,KAAK,aAAa,IAAIjB,GAAG,CAACe,aAAa,KAAKf,GAAG,CAACa,YAAa;QAAAT,QAAA,eAEvF1B,OAAA,CAACT,UAAU;UAACkC,QAAQ,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CACb,EACAxB,aAAa,iBACZP,OAAA,CAACd,UAAU;QACTsD,IAAI,EAAC,OAAO;QACZM,OAAO,EAAEA,CAAA,KAAMvC,aAAa,CAACe,GAAG,CAAE;QAClCyB,KAAK,EAAC,6BAA6B;QACnCN,KAAK,EAAC,MAAM;QAAAf,QAAA,eAEZ1B,OAAA,CAACP,WAAW;UAACgC,QAAQ,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CACb,EACAvB,UAAU,IAAIc,GAAG,CAACiB,YAAY,KAAK,WAAW,IAAIjB,GAAG,CAACiB,YAAY,KAAK,MAAM,iBAC5EvC,OAAA,CAACb,OAAO;QAAC4D,KAAK,EAAC,+BAA+B;QAAArB,QAAA,eAC5C1B,OAAA,CAACd,UAAU;UACTsD,IAAI,EAAC,OAAO;UACZM,OAAO,EAAEA,CAAA,KAAMtC,UAAU,CAACc,GAAG,CAAE;UAC/BmB,KAAK,EAAC,SAAS;UAAAf,QAAA,eAEf1B,OAAA,CAACL,OAAO;YAAC8B,QAAQ,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,CACF;;EAED;EACA,MAAMkB,SAAS,GAAGA,CAAC3B,GAAG,EAAE4B,KAAK,KAAK;IAChC;IACA,IAAIC,OAAO,GAAG,SAAS;IACvB,IAAI7B,GAAG,CAACiB,YAAY,KAAK1C,WAAW,CAACuD,WAAW,EAAED,OAAO,GAAG,wBAAwB,CAAC,KAChF,IAAI7B,GAAG,CAACiB,YAAY,KAAK1C,WAAW,CAACwD,MAAM,EAAEF,OAAO,GAAG,wBAAwB,CAAC,KAChF,IAAI7B,GAAG,CAACiB,YAAY,KAAK1C,WAAW,CAACyD,SAAS,EAAEH,OAAO,GAAG,wBAAwB,CAAC,KACnF,IAAI7B,GAAG,CAACiB,YAAY,KAAK1C,WAAW,CAAC0D,IAAI,EAAEJ,OAAO,GAAG,wBAAwB;IAElF,oBACEnD,OAAA,CAAChB,QAAQ;MAEPwC,EAAE,EAAE;QACFgC,eAAe,EAAEL,OAAO;QACxB,SAAS,EAAE;UAAEK,eAAe,EAAE;QAAsB;MACtD,CAAE;MAAA9B,QAAA,EAEDZ,OAAO,CAAC2C,GAAG,CAAEC,MAAM,iBAClB1D,OAAA,CAACf,SAAS;QAER+C,KAAK,EAAE0B,MAAM,CAAC1B,KAAK,IAAI,MAAO;QAC9BR,EAAE,EAAEkC,MAAM,CAACzB,SAAU;QAAAP,QAAA,EAEpBgC,MAAM,CAACrC,UAAU,GAAGqC,MAAM,CAACrC,UAAU,CAACC,GAAG,CAAC,GAAGA,GAAG,CAACoC,MAAM,CAAC3C,KAAK;MAAC,GAJ1D2C,MAAM,CAAC3C,KAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKR,CACZ;IAAC,GAdGmB,KAAK;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeF,CAAC;EAEf,CAAC;;EAED;EACA,MAAM4B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACjD,cAAc,CAACkD,MAAM,EAAE,OAAO,IAAI;IAEvC,MAAMC,WAAW,GAAGnD,cAAc,CAACkD,MAAM;IACzC,MAAME,WAAW,GAAGpD,cAAc,CAACqD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,YAAY,KAAK1C,WAAW,CAACuD,WAAW,CAAC,CAACQ,MAAM;IACjG,MAAMK,KAAK,GAAGvD,cAAc,CAACqD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,YAAY,KAAK1C,WAAW,CAACwD,MAAM,CAAC,CAACO,MAAM;IACtF,MAAMM,SAAS,GAAGxD,cAAc,CAACqD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,YAAY,KAAK1C,WAAW,CAACyD,SAAS,CAAC,CAACM,MAAM;IAC7F,MAAMO,IAAI,GAAGzD,cAAc,CAACqD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,YAAY,KAAK1C,WAAW,CAAC0D,IAAI,CAAC,CAACK,MAAM;IAEnF,MAAMQ,WAAW,GAAG1D,cAAc,CAAC2D,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC,KAAKM,GAAG,IAAIN,CAAC,CAAC7B,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACrF,MAAMoC,YAAY,GAAG7D,cAAc,CAAC2D,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC,KAAKM,GAAG,IAAIN,CAAC,CAAC3B,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACvF,MAAMmC,eAAe,GAAGJ,WAAW,GAAGG,YAAY;IAElD,MAAME,mBAAmB,GAAGL,WAAW,GAAGM,IAAI,CAACC,KAAK,CAAEH,eAAe,GAAGJ,WAAW,GAAI,GAAG,CAAC,GAAG,CAAC;IAE/F,OAAO;MACLP,WAAW;MACXC,WAAW;MACXG,KAAK;MACLC,SAAS;MACTC,IAAI;MACJC,WAAW;MACXG,YAAY;MACZC,eAAe;MACfC;IACF,CAAC;EACH,CAAC;EAED,MAAMG,KAAK,GAAGjB,cAAc,CAAC,CAAC;EAE9B,oBACE3D,OAAA,CAACnB,GAAG;IAAA6C,QAAA,GACDkD,KAAK,iBACJ5E,OAAA,CAACnB,GAAG;MAAC2C,EAAE,EAAE;QAAEqD,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE,kBAAkB;QAAEC,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAE;MAAAvD,QAAA,gBACnF1B,OAAA,CAAClB,UAAU;QAACyC,OAAO,EAAC,WAAW;QAAC2D,YAAY;QAAAxD,QAAA,GAAC,eAC9B,EAAChB,cAAc,CAACkD,MAAM,EAAC,uBACtC;MAAA;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/B,OAAA,CAACnB,GAAG;QAAC2C,EAAE,EAAE;UAAEoB,OAAO,EAAE,MAAM;UAAEuC,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAA1D,QAAA,gBACrD1B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACkB,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxE/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAAAG,QAAA,GAAEkD,KAAK,CAACH,mBAAmB,EAAC,GAAC;UAAA;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACkB,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3E/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAACkB,KAAK,EAAC,cAAc;YAAAf,QAAA,EAAEkD,KAAK,CAACd;UAAW;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACkB,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtE/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAACkB,KAAK,EAAC,cAAc;YAAAf,QAAA,EAAEkD,KAAK,CAACX;UAAK;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACkB,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzE/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAACkB,KAAK,EAAC,cAAc;YAAAf,QAAA,EAAEkD,KAAK,CAACV;UAAS;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACkB,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpE/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAACkB,KAAK,EAAC,YAAY;YAAAf,QAAA,EAAEkD,KAAK,CAACT;UAAI;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACkB,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5E/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAAAG,QAAA,GAAEkD,KAAK,CAACR,WAAW,CAAChC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACkB,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7E/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAAAG,QAAA,GAAEkD,KAAK,CAACL,YAAY,CAACnC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACkB,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChF/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAAAG,QAAA,GAAEkD,KAAK,CAACJ,eAAe,CAACpC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED/B,OAAA,CAACJ,eAAe;MACdiB,IAAI,EAAEX,MAAO;MACbY,OAAO,EAAEA,OAAQ;MACjBV,oBAAoB,EAAEQ,wBAAyB;MAC/CT,OAAO,EAAEA,OAAQ;MACjBkF,YAAY,EAAC,4BAA4B;MACzCpC,SAAS,EAAEA;IAAU;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACtB,EAAA,CArRIR,qBAAqB;AAAAqF,EAAA,GAArBrF,qBAAqB;AAuR3B,eAAeA,qBAAqB;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}