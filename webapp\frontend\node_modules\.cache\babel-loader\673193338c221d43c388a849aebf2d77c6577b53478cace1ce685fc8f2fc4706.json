{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'өнгөрсөн' eeee 'гарагийн' p 'цагт'\",\n  yesterday: \"'өчигдөр' p 'цагт'\",\n  today: \"'өнөөдөр' p 'цагт'\",\n  tomorrow: \"'маргааш' p 'цагт'\",\n  nextWeek: \"'ирэх' eeee 'гарагийн' p 'цагт'\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/mn/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'өнгөрсөн' eeee 'гарагийн' p 'цагт'\",\n  yesterday: \"'өчигдөр' p 'цагт'\",\n  today: \"'өнөөдөр' p 'цагт'\",\n  tomorrow: \"'маргааш' p 'цагт'\",\n  nextWeek: \"'ирэх' eeee 'гарагийн' p 'цагт'\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,qCAAqC;EAC/CC,SAAS,EAAE,oBAAoB;EAC/BC,KAAK,EAAE,oBAAoB;EAC3BC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,iCAAiC;EAC3CC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}