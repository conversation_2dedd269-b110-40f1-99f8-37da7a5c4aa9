{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { PickerActionsContext } from \"../internals/components/PickerProvider.js\";\n/**\n * Returns a subset of the context passed by the Picker wrapping the current component.\n * It only contains the actions and never causes a re-render of the component using it.\n */\nexport const usePickerActionsContext = () => {\n  const value = React.useContext(PickerActionsContext);\n  if (value == null) {\n    throw new Error(['MUI X: The `usePickerActionsContext` can only be called in fields that are used as a slot of a Picker component'].join('\\n'));\n  }\n  return value;\n};", "map": {"version": 3, "names": ["React", "PickerActionsContext", "usePickerActionsContext", "value", "useContext", "Error", "join"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/hooks/usePickerActionsContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { PickerActionsContext } from \"../internals/components/PickerProvider.js\";\n/**\n * Returns a subset of the context passed by the Picker wrapping the current component.\n * It only contains the actions and never causes a re-render of the component using it.\n */\nexport const usePickerActionsContext = () => {\n  const value = React.useContext(PickerActionsContext);\n  if (value == null) {\n    throw new Error(['MUI X: The `usePickerActionsContext` can only be called in fields that are used as a slot of a Picker component'].join('\\n'));\n  }\n  return value;\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF;AACA;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EAC3C,MAAMC,KAAK,GAAGH,KAAK,CAACI,UAAU,CAACH,oBAAoB,CAAC;EACpD,IAAIE,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAIE,KAAK,CAAC,CAAC,iHAAiH,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACjJ;EACA,OAAOH,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}