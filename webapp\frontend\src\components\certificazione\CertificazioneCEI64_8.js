import React, { useState, useRef, forwardRef, useImperativeHandle } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Divider,
  Alert,
  Chip
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  Science as ScienceIcon,
  Warning as WarningIcon,
  Assessment as ReportIcon,
  Build as BuildIcon,
  Visibility as ViewIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';

import RapportiGenerali from './RapportiGenerali';
import ProveDettagliate from './ProveDettagliate';
import CertificazioneCavi from '../cavi/CertificazioneCavi';
import nonConformitaService from '../../services/nonConformitaService';

const CertificazioneCEI64_8 = forwardRef(({ cantiereId, onSuccess, onError }, ref) => {
  const [selectedModule, setSelectedModule] = useState(null);
  const [selectedCertificazione, setSelectedCertificazione] = useState(null);
  const [nonConformita, setNonConformita] = useState([]);
  const [loading, setLoading] = useState(false);

  // Refs per i componenti figli
  const rapportiGeneraliRef = useRef();
  const proveDettagliateRef = useRef();
  const certificazioniRef = useRef();

  // Espone i metodi tramite ref
  useImperativeHandle(ref, () => ({
    handleOptionSelect
  }));

  // Carica le non conformità
  const loadNonConformita = async () => {
    try {
      setLoading(true);
      const data = await nonConformitaService.getNonConformita(cantiereId);
      setNonConformita(data);
    } catch (error) {
      onError('Errore nel caricamento delle non conformità');
      console.error('Errore nel caricamento delle non conformità:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la selezione di un'opzione dal menu
  const handleOptionSelect = (option) => {
    switch (option) {
      // Rapporti Generali
      case 'visualizzaRapporti':
      case 'creaRapporto':
      case 'modificaRapporto':
      case 'eliminaRapporto':
      case 'dettagliRapporto':
        setSelectedModule('rapporti');
        if (rapportiGeneraliRef.current) {
          rapportiGeneraliRef.current.handleOptionSelect(option);
        }
        break;

      // Certificazioni Tradizionali
      case 'visualizzaCertificazioni':
      case 'creaCertificazione':
      case 'dettagliCertificazione':
      case 'eliminaCertificazione':
      case 'filtraCertificazioni':
      case 'generaPdf':
      case 'gestioneStrumenti':
        setSelectedModule('certificazioni');
        if (certificazioniRef.current) {
          certificazioniRef.current.handleOptionSelect(option);
        }
        break;

      // Prove Dettagliate
      case 'visualizzaProve':
      case 'creaProva':
      case 'modificaProva':
      case 'eliminaProva':
        if (selectedCertificazione) {
          setSelectedModule('prove');
          if (proveDettagliateRef.current) {
            proveDettagliateRef.current.handleOptionSelect(option);
          }
        } else {
          onError('Seleziona prima una certificazione per gestire le prove dettagliate');
        }
        break;

      // Non Conformità
      case 'visualizzaNonConformita':
        setSelectedModule('nonConformita');
        loadNonConformita();
        break;

      // Dashboard
      case 'dashboardCEI':
        setSelectedModule('dashboard');
        break;

      default:
        setSelectedModule(null);
        break;
    }
  };

  const renderDashboard = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h5" gutterBottom>
          Dashboard Certificazione CEI 64-8
        </Typography>
      </Grid>
      
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2}>
              <ReportIcon color="primary" />
              <Box>
                <Typography variant="h6">Rapporti Generali</Typography>
                <Typography variant="body2" color="text.secondary">
                  Gestione rapporti di collaudo conformi CEI 64-8
                </Typography>
              </Box>
            </Box>
          </CardContent>
          <CardActions>
            <Button 
              size="small" 
              onClick={() => handleOptionSelect('visualizzaRapporti')}
              startIcon={<ViewIcon />}
            >
              Visualizza
            </Button>
            <Button 
              size="small" 
              onClick={() => handleOptionSelect('creaRapporto')}
              startIcon={<AddIcon />}
            >
              Nuovo
            </Button>
          </CardActions>
        </Card>
      </Grid>

      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2}>
              <AssignmentIcon color="secondary" />
              <Box>
                <Typography variant="h6">Certificazioni Cavi</Typography>
                <Typography variant="body2" color="text.secondary">
                  Certificazioni singole con prove base
                </Typography>
              </Box>
            </Box>
          </CardContent>
          <CardActions>
            <Button 
              size="small" 
              onClick={() => handleOptionSelect('visualizzaCertificazioni')}
              startIcon={<ViewIcon />}
            >
              Visualizza
            </Button>
            <Button 
              size="small" 
              onClick={() => handleOptionSelect('creaCertificazione')}
              startIcon={<AddIcon />}
            >
              Nuova
            </Button>
          </CardActions>
        </Card>
      </Grid>

      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2}>
              <ScienceIcon color="info" />
              <Box>
                <Typography variant="h6">Prove Dettagliate</Typography>
                <Typography variant="body2" color="text.secondary">
                  Prove specifiche per conformità normativa
                </Typography>
              </Box>
            </Box>
          </CardContent>
          <CardActions>
            <Button 
              size="small" 
              onClick={() => handleOptionSelect('visualizzaProve')}
              startIcon={<ViewIcon />}
              disabled={!selectedCertificazione}
            >
              Visualizza
            </Button>
            <Button 
              size="small" 
              onClick={() => handleOptionSelect('creaProva')}
              startIcon={<AddIcon />}
              disabled={!selectedCertificazione}
            >
              Nuova
            </Button>
          </CardActions>
        </Card>
      </Grid>

      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2}>
              <WarningIcon color="warning" />
              <Box>
                <Typography variant="h6">Non Conformità</Typography>
                <Typography variant="body2" color="text.secondary">
                  Gestione NC e azioni correttive
                </Typography>
                {nonConformita.length > 0 && (
                  <Chip 
                    label={`${nonConformita.length} NC attive`} 
                    color="warning" 
                    size="small" 
                    sx={{ mt: 1 }}
                  />
                )}
              </Box>
            </Box>
          </CardContent>
          <CardActions>
            <Button 
              size="small" 
              onClick={() => handleOptionSelect('visualizzaNonConformita')}
              startIcon={<ViewIcon />}
            >
              Visualizza
            </Button>
          </CardActions>
        </Card>
      </Grid>

      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2}>
              <BuildIcon color="success" />
              <Box>
                <Typography variant="h6">Strumenti</Typography>
                <Typography variant="body2" color="text.secondary">
                  Gestione strumenti di misura certificati
                </Typography>
              </Box>
            </Box>
          </CardContent>
          <CardActions>
            <Button 
              size="small" 
              onClick={() => handleOptionSelect('gestioneStrumenti')}
              startIcon={<ViewIcon />}
            >
              Gestisci
            </Button>
          </CardActions>
        </Card>
      </Grid>
    </Grid>
  );

  const renderNonConformita = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Non Conformità Rilevate
      </Typography>
      {nonConformita.length === 0 ? (
        <Alert severity="success">Nessuna non conformità rilevata</Alert>
      ) : (
        <List>
          {nonConformita.map((nc) => (
            <ListItem key={nc.id_nc} divider>
              <ListItemIcon>
                <WarningIcon color={nc.tipo_nc === 'CRITICA' ? 'error' : 'warning'} />
              </ListItemIcon>
              <ListItemText
                primary={`${nc.codice_nc} - ${nc.descrizione}`}
                secondary={
                  <Box>
                    <Typography variant="body2">
                      Data: {new Date(nc.data_rilevazione).toLocaleDateString('it-IT')}
                    </Typography>
                    <Chip 
                      label={nc.stato_nc} 
                      color={nc.stato_nc === 'CHIUSA' ? 'success' : 'warning'}
                      size="small"
                      sx={{ mt: 0.5 }}
                    />
                  </Box>
                }
              />
            </ListItem>
          ))}
        </List>
      )}
    </Box>
  );

  return (
    <Box>
      {/* Contenuto principale */}
      {selectedModule === 'dashboard' && renderDashboard()}
      {selectedModule === 'rapporti' && (
        <RapportiGenerali
          ref={rapportiGeneraliRef}
          cantiereId={cantiereId}
          onSuccess={onSuccess}
          onError={onError}
        />
      )}
      {selectedModule === 'certificazioni' && (
        <CertificazioneCavi
          ref={certificazioniRef}
          cantiereId={cantiereId}
          onSuccess={onSuccess}
          onError={onError}
        />
      )}
      {selectedModule === 'prove' && selectedCertificazione && (
        <ProveDettagliate
          ref={proveDettagliateRef}
          cantiereId={cantiereId}
          certificazioneId={selectedCertificazione.id_certificazione}
          onSuccess={onSuccess}
          onError={onError}
        />
      )}
      {selectedModule === 'nonConformita' && renderNonConformita()}
      
      {/* Dashboard di default */}
      {!selectedModule && renderDashboard()}
    </Box>
  );
});

export default CertificazioneCEI64_8;
