{"ast": null, "code": "/**\n * Davant de les xifres que es diuen amb vocal inicial, 1 i 11, s'apostrofen els articles el i la i la preposició de igual que si estiguessin escrits amb lletres.\n *    l'1 de juliol ('l'u')\n *    l'11 de novembre ('l'onze')\n *    l'11a clàusula del contracte ('l'onzena')\n *    la contractació d'11 jugadors ('d'onze')\n *    l'aval d'11.000 socis ('d'onze mil')\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=apostrofaci%25F3+davant+xifres&action=Principal&method=detall_completa&numPagina=1&idHit=11236&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=11236&titol=apostrofaci%F3%20davant%20de%20xifres%20%2F%20apostrofaci%F3%20davant%20de%201%20i%2011&numeroResultat=1&clickLink=detall&tipusCerca=cerca.normes\n */\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"menys d'un segon\",\n    eleven: \"menys d'onze segons\",\n    other: \"menys de {{count}} segons\"\n  },\n  xSeconds: {\n    one: \"1 segon\",\n    other: \"{{count}} segons\"\n  },\n  halfAMinute: \"mig minut\",\n  lessThanXMinutes: {\n    one: \"menys d'un minut\",\n    eleven: \"menys d'onze minuts\",\n    other: \"menys de {{count}} minuts\"\n  },\n  xMinutes: {\n    one: \"1 minut\",\n    other: \"{{count}} minuts\"\n  },\n  aboutXHours: {\n    one: \"aproximadament una hora\",\n    other: \"aproximadament {{count}} hores\"\n  },\n  xHours: {\n    one: \"1 hora\",\n    other: \"{{count}} hores\"\n  },\n  xDays: {\n    one: \"1 dia\",\n    other: \"{{count}} dies\"\n  },\n  aboutXWeeks: {\n    one: \"aproximadament una setmana\",\n    other: \"aproximadament {{count}} setmanes\"\n  },\n  xWeeks: {\n    one: \"1 setmana\",\n    other: \"{{count}} setmanes\"\n  },\n  aboutXMonths: {\n    one: \"aproximadament un mes\",\n    other: \"aproximadament {{count}} mesos\"\n  },\n  xMonths: {\n    one: \"1 mes\",\n    other: \"{{count}} mesos\"\n  },\n  aboutXYears: {\n    one: \"aproximadament un any\",\n    other: \"aproximadament {{count}} anys\"\n  },\n  xYears: {\n    one: \"1 any\",\n    other: \"{{count}} anys\"\n  },\n  overXYears: {\n    one: \"més d'un any\",\n    eleven: \"més d'onze anys\",\n    other: \"més de {{count}} anys\"\n  },\n  almostXYears: {\n    one: \"gairebé un any\",\n    other: \"gairebé {{count}} anys\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 11 && tokenValue.eleven) {\n    result = tokenValue.eleven;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"en \" + result;\n    } else {\n      return \"fa \" + result;\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "eleven", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ca/_lib/formatDistance.js"], "sourcesContent": ["/**\n * Davant de les xifres que es diuen amb vocal inicial, 1 i 11, s'apostrofen els articles el i la i la preposició de igual que si estiguessin escrits amb lletres.\n *    l'1 de juliol ('l'u')\n *    l'11 de novembre ('l'onze')\n *    l'11a clàusula del contracte ('l'onzena')\n *    la contractació d'11 jugadors ('d'onze')\n *    l'aval d'11.000 socis ('d'onze mil')\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=apostrofaci%25F3+davant+xifres&action=Principal&method=detall_completa&numPagina=1&idHit=11236&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=11236&titol=apostrofaci%F3%20davant%20de%20xifres%20%2F%20apostrofaci%F3%20davant%20de%201%20i%2011&numeroResultat=1&clickLink=detall&tipusCerca=cerca.normes\n */\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"menys d'un segon\",\n    eleven: \"menys d'onze segons\",\n    other: \"menys de {{count}} segons\",\n  },\n\n  xSeconds: {\n    one: \"1 segon\",\n    other: \"{{count}} segons\",\n  },\n\n  halfAMinute: \"mig minut\",\n\n  lessThanXMinutes: {\n    one: \"menys d'un minut\",\n    eleven: \"menys d'onze minuts\",\n    other: \"menys de {{count}} minuts\",\n  },\n\n  xMinutes: {\n    one: \"1 minut\",\n    other: \"{{count}} minuts\",\n  },\n\n  aboutXHours: {\n    one: \"aproximadament una hora\",\n    other: \"aproximadament {{count}} hores\",\n  },\n\n  xHours: {\n    one: \"1 hora\",\n    other: \"{{count}} hores\",\n  },\n\n  xDays: {\n    one: \"1 dia\",\n    other: \"{{count}} dies\",\n  },\n\n  aboutXWeeks: {\n    one: \"aproximadament una setmana\",\n    other: \"aproximadament {{count}} setmanes\",\n  },\n\n  xWeeks: {\n    one: \"1 setmana\",\n    other: \"{{count}} setmanes\",\n  },\n\n  aboutXMonths: {\n    one: \"aproximadament un mes\",\n    other: \"aproximadament {{count}} mesos\",\n  },\n\n  xMonths: {\n    one: \"1 mes\",\n    other: \"{{count}} mesos\",\n  },\n\n  aboutXYears: {\n    one: \"aproximadament un any\",\n    other: \"aproximadament {{count}} anys\",\n  },\n\n  xYears: {\n    one: \"1 any\",\n    other: \"{{count}} anys\",\n  },\n\n  overXYears: {\n    one: \"més d'un any\",\n    eleven: \"més d'onze anys\",\n    other: \"més de {{count}} anys\",\n  },\n\n  almostXYears: {\n    one: \"gairebé un any\",\n    other: \"gairebé {{count}} anys\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 11 && tokenValue.eleven) {\n    result = tokenValue.eleven;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"en \" + result;\n    } else {\n      return \"fa \" + result;\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,qBAAqB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRH,GAAG,EAAE,SAAS;IACdE,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,WAAW;EAExBC,gBAAgB,EAAE;IAChBL,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,qBAAqB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRN,GAAG,EAAE,SAAS;IACdE,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXP,GAAG,EAAE,yBAAyB;IAC9BE,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNR,GAAG,EAAE,QAAQ;IACbE,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLT,GAAG,EAAE,OAAO;IACZE,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXV,GAAG,EAAE,4BAA4B;IACjCE,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNX,GAAG,EAAE,WAAW;IAChBE,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZZ,GAAG,EAAE,uBAAuB;IAC5BE,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPb,GAAG,EAAE,OAAO;IACZE,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXd,GAAG,EAAE,uBAAuB;IAC5BE,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNf,GAAG,EAAE,OAAO;IACZE,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVhB,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,iBAAiB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZjB,GAAG,EAAE,gBAAgB;IACrBE,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGzB,oBAAoB,CAACqB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM,IAAIoB,KAAK,KAAK,EAAE,IAAIG,UAAU,CAACtB,MAAM,EAAE;IAC5CqB,MAAM,GAAGC,UAAU,CAACtB,MAAM;EAC5B,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,KAAK,GAAGA,MAAM;IACvB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}