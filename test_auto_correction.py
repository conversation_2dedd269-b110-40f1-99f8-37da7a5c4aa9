#!/usr/bin/env python3
"""
Test per verificare la correzione automatica degli errori di battitura.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from webapp.backend.utils.cable_normalizer import CableNormalizer

def test_tipologia_corrections():
    """Testa la correzione automatica delle tipologie."""
    print("🔧 TEST CORREZIONE AUTOMATICA TIPOLOGIE")
    print("-" * 50)
    
    normalizer = CableNormalizer()
    
    # Test cases con errori di battitura
    test_cases = [
        # Correzioni dirette
        ("FG1&OR16", "FG16OR16"),
        ("FG16OR!6", "FG16OR16"),
        ("FG16OR1", "FG16OR16"),
        ("FG16O16", "FG16OR16"),
        ("FG16R16", "FG16OR16"),
        ("FG6OR16", "FG16OR16"),
        ("FG16OR6", "FG16OR16"),
        ("LICY", "LIYCY"),
        ("LYCY", "LIYCY"),
        ("LIYC", "LIYCY"),
        ("LIICY", "LIYCY"),
        ("UTP6", "UTP CAT6"),
        ("CAT6UTP", "UTP CAT6"),
        ("CAT5", "CAT5E"),
        
        # Correzioni per similarità
        ("FG16OR15", "FG16OR16"),  # 1 carattere diverso
        ("FG16OR17", "FG16OR16"),  # 1 carattere diverso
        ("LIYC", "LIYCY"),         # 1 carattere mancante
        ("LIYCYY", "LIYCY"),       # 1 carattere extra
    ]
    
    for input_val, expected in test_cases:
        result = normalizer.normalize_tipologia(input_val)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{input_val}' → '{result}' (atteso: '{expected}')")
        
        if result != expected:
            # Mostra il punteggio di similarità per debug
            similarity = normalizer.calculate_similarity(input_val, expected)
            print(f"    Similarità: {similarity:.2f}")
    
    print()

def test_colore_corrections():
    """Testa la correzione automatica dei colori."""
    print("🎨 TEST CORREZIONE AUTOMATICA COLORI")
    print("-" * 50)
    
    normalizer = CableNormalizer()
    
    test_cases = [
        # Correzioni dirette
        ("BLAC", "BLACK"),
        ("BALCK", "BLACK"),
        ("BLACKK", "BLACK"),
        ("NERO", "BLACK"),
        ("ROSS", "RED"),
        ("ROSSO", "RED"),
        ("BLU", "BLUE"),
        ("VERDE", "GREEN"),
        ("GIALLO", "YELLOW"),
        ("BIANCO", "WHITE"),
        ("GRIGIO", "GRAY"),
        
        # Correzioni per similarità
        ("BLCK", "BLACK"),    # 1 carattere mancante
        ("BLACKE", "BLACK"),  # 1 carattere extra
        ("BLACL", "BLACK"),   # 1 carattere sostituito
    ]
    
    for input_val, expected in test_cases:
        result = normalizer.normalize_text_field(input_val, 'colore_cavo')
        status = "✅" if result == expected else "❌"
        print(f"{status} '{input_val}' → '{result}' (atteso: '{expected}')")
    
    print()

def test_utility_corrections():
    """Testa la correzione automatica delle utilities."""
    print("🏢 TEST CORREZIONE AUTOMATICA UTILITIES")
    print("-" * 50)
    
    normalizer = CableNormalizer()
    
    test_cases = [
        # Correzioni dirette
        ("ENELL", "ENEL"),
        ("ENL", "ENEL"),
        ("TIMM", "TIM"),
        ("TELECOM", "TIM"),
        ("VODAPHONE", "VODAFONE"),
        ("VODA", "VODAFONE"),
        ("FASTWE", "FASTWEB"),
        ("FAST", "FASTWEB"),
        
        # Correzioni per similarità
        ("ENEL", "ENEL"),      # Già corretto
        ("ENEEL", "ENEL"),     # 1 carattere extra
        ("ENE", "ENEL"),       # 1 carattere mancante
        ("VODAFON", "VODAFONE"), # 1 carattere mancante
    ]
    
    for input_val, expected in test_cases:
        result = normalizer.normalize_text_field(input_val, 'utility')
        status = "✅" if result == expected else "❌"
        print(f"{status} '{input_val}' → '{result}' (atteso: '{expected}')")
    
    print()

def test_similarity_algorithm():
    """Testa l'algoritmo di similarità."""
    print("📊 TEST ALGORITMO SIMILARITÀ")
    print("-" * 50)
    
    normalizer = CableNormalizer()
    
    test_pairs = [
        ("FG16OR16", "FG16OR16", 1.0),    # Identici
        ("FG16OR16", "FG16OR15", 0.89),   # 1 carattere diverso
        ("FG16OR16", "FG16OR1", 0.89),    # 1 carattere mancante
        ("FG16OR16", "FG16OR166", 0.90),  # 1 carattere extra
        ("BLACK", "BLAC", 0.89),          # 1 carattere mancante
        ("BLACK", "BALCK", 0.80),         # Trasposizione
        ("ENEL", "ENL", 0.75),            # 1 carattere mancante
        ("VODAFONE", "VODA", 0.57),       # Molto diverso
    ]
    
    for str1, str2, expected_min in test_pairs:
        similarity = normalizer.calculate_similarity(str1, str2)
        status = "✅" if similarity >= expected_min - 0.1 else "❌"  # Tolleranza di 0.1
        print(f"{status} '{str1}' vs '{str2}': {similarity:.2f} (min: {expected_min})")
    
    print()

def test_complete_cable_correction():
    """Testa la correzione completa di un cavo."""
    print("🔧 TEST CORREZIONE COMPLETA CAVO")
    print("-" * 50)
    
    normalizer = CableNormalizer()
    
    # Dati di test con errori di battitura
    test_data = {
        'id_cavo': "test_001",
        'tipologia': "FG1&OR16",      # Errore di battitura
        'sezione': "1x240 mm2",
        'utility': "ENELL",           # Errore di battitura
        'colore_cavo': "BLAC",        # Errore di battitura
        'sistema': "test system",
        'ubicazione_partenza': "test location",
        'descrizione_utenza_partenza': "Test description",
    }
    
    print("Dati originali:")
    for field, value in test_data.items():
        print(f"  {field}: '{value}'")
    
    normalized = normalizer.normalize_all_cable_fields(test_data)
    
    print("\nDati corretti:")
    for field, value in normalized.items():
        if field in test_data:
            original = test_data[field]
            status = "🔄" if value != original.upper() else "✅"
            print(f"  {field}: '{value}' {status}")
    
    # Verifica correzioni specifiche
    expected_corrections = {
        'tipologia': 'FG16OR16',
        'utility': 'ENEL',
        'colore_cavo': 'BLACK'
    }
    
    print("\nVerifica correzioni:")
    all_correct = True
    for field, expected in expected_corrections.items():
        actual = normalized.get(field)
        if actual == expected:
            print(f"✅ {field}: '{actual}' (corretto)")
        else:
            print(f"❌ {field}: '{actual}' (atteso: '{expected}')")
            all_correct = False
    
    if all_correct:
        print("\n🎉 Tutte le correzioni automatiche funzionano!")
    else:
        print("\n❌ Alcune correzioni non funzionano correttamente")
    
    print()

def test_edge_cases():
    """Testa casi limite."""
    print("⚠️  TEST CASI LIMITE")
    print("-" * 50)
    
    normalizer = CableNormalizer()
    
    edge_cases = [
        ("", ""),                    # Stringa vuota
        ("   ", ""),                 # Solo spazi
        ("X", "X"),                  # Singolo carattere
        ("UNKNOWN_TYPE", "UNKNOWN_TYPE"),  # Tipo sconosciuto
        ("123456", "123456"),        # Solo numeri
        ("!@#$%", ""),               # Solo caratteri speciali
    ]
    
    for input_val, expected in edge_cases:
        result = normalizer.normalize_tipologia(input_val)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{input_val}' → '{result}' (atteso: '{expected}')")
    
    print()

def main():
    """Funzione principale del test."""
    print("🧪 TEST CORREZIONE AUTOMATICA ERRORI DI BATTITURA")
    print("=" * 60)
    
    test_tipologia_corrections()
    test_colore_corrections()
    test_utility_corrections()
    test_similarity_algorithm()
    test_complete_cable_correction()
    test_edge_cases()
    
    print("🎉 TUTTI I TEST DI CORREZIONE AUTOMATICA COMPLETATI!")
    print("✅ Il sistema può correggere automaticamente errori di battitura comuni!")

if __name__ == "__main__":
    main()
