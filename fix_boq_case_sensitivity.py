#!/usr/bin/env python3
"""
Script per risolvere il problema di case sensitivity nel BOQ.
Normalizza i dati delle sezioni per evitare righe duplicate.
"""

import psycopg2
import psycopg2.extras
from modules.database_pg import Database

def fix_case_sensitivity():
    """Corregge il problema di case sensitivity nelle sezioni."""
    print("🔧 CORREZIONE CASE SENSITIVITY BOQ")
    print("=" * 50)
    
    db = Database()
    conn = db.get_dict_cursor_connection()
    
    try:
        with conn.cursor() as cur:
            cantiere_id = 1
            
            print("🔍 STEP 1: IDENTIFICAZIONE PROBLEMI")
            print("-" * 40)
            
            # Trova tutte le varianti di sezione per FG16OR16 240MM2
            cur.execute("""
                SELECT DISTINCT tipologia, sezione, COUNT(*) as num_cavi
                FROM cavi
                WHERE id_cantiere = %s 
                  AND tipologia ILIKE %s 
                  AND sezione ILIKE %s
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """, (cantiere_id, '%FG16OR16%', '%240MM2%'))
            
            variants = cur.fetchall()
            
            print(f"Trovate {len(variants)} varianti per FG16OR16 240MM2:")
            for i, variant in enumerate(variants, 1):
                print(f"{i}. '{variant['tipologia']}' | '{variant['sezione']}' ({variant['num_cavi']} cavi)")
            
            if len(variants) <= 1:
                print("✅ Nessun problema di duplicazione trovato!")
                return
            
            print("\n🔧 STEP 2: NORMALIZZAZIONE")
            print("-" * 40)
            
            # Determina la forma canonica (quella con più cavi o la prima alfabeticamente)
            canonical_variant = max(variants, key=lambda x: x['num_cavi'])
            canonical_sezione = canonical_variant['sezione']
            
            print(f"Forma canonica scelta: '{canonical_sezione}' ({canonical_variant['num_cavi']} cavi)")
            
            # Aggiorna tutte le altre varianti alla forma canonica
            updates_made = 0
            for variant in variants:
                if variant['sezione'] != canonical_sezione:
                    print(f"\n📝 Aggiornamento: '{variant['sezione']}' → '{canonical_sezione}'")
                    
                    # Aggiorna i cavi
                    cur.execute("""
                        UPDATE cavi 
                        SET sezione = %s, 
                            modificato_manualmente = COALESCE(modificato_manualmente, 0) + 10
                        WHERE id_cantiere = %s 
                          AND tipologia = %s 
                          AND sezione = %s
                    """, (canonical_sezione, cantiere_id, variant['tipologia'], variant['sezione']))
                    
                    cavi_updated = cur.rowcount
                    print(f"   ✅ {cavi_updated} cavi aggiornati")
                    updates_made += cavi_updated
            
            # Verifica se ci sono bobine da aggiornare
            print(f"\n🔍 STEP 3: VERIFICA BOBINE")
            print("-" * 40)
            
            cur.execute("""
                SELECT DISTINCT tipologia, sezione, COUNT(*) as num_bobine
                FROM parco_cavi
                WHERE id_cantiere = %s 
                  AND tipologia ILIKE %s 
                  AND sezione ILIKE %s
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """, (cantiere_id, '%FG16OR16%', '%240MM2%'))
            
            bobine_variants = cur.fetchall()
            
            print(f"Trovate {len(bobine_variants)} varianti bobine per FG16OR16 240MM2:")
            for i, variant in enumerate(bobine_variants, 1):
                print(f"{i}. '{variant['tipologia']}' | '{variant['sezione']}' ({variant['num_bobine']} bobine)")
            
            # Aggiorna le bobine se necessario
            bobine_updates = 0
            for variant in bobine_variants:
                if variant['sezione'] != canonical_sezione:
                    print(f"\n📝 Aggiornamento bobine: '{variant['sezione']}' → '{canonical_sezione}'")
                    
                    cur.execute("""
                        UPDATE parco_cavi 
                        SET sezione = %s
                        WHERE id_cantiere = %s 
                          AND tipologia = %s 
                          AND sezione = %s
                    """, (canonical_sezione, cantiere_id, variant['tipologia'], variant['sezione']))
                    
                    bobine_updated = cur.rowcount
                    print(f"   ✅ {bobine_updated} bobine aggiornate")
                    bobine_updates += bobine_updated
            
            print(f"\n📊 STEP 4: VERIFICA FINALE")
            print("-" * 40)
            
            # Verifica che ora ci sia una sola riga
            cur.execute("""
                SELECT tipologia, sezione, COUNT(*) as num_cavi
                FROM cavi
                WHERE id_cantiere = %s 
                  AND tipologia ILIKE %s 
                  AND sezione ILIKE %s
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """, (cantiere_id, '%FG16OR16%', '%240MM2%'))
            
            final_variants = cur.fetchall()
            
            print(f"Dopo la correzione, trovate {len(final_variants)} varianti:")
            for i, variant in enumerate(final_variants, 1):
                print(f"{i}. '{variant['tipologia']}' | '{variant['sezione']}' ({variant['num_cavi']} cavi)")
            
            if len(final_variants) == 1:
                print("✅ Problema risolto! Ora c'è una sola riga nel BOQ.")
                
                # Commit delle modifiche
                if updates_made > 0 or bobine_updates > 0:
                    conn.commit()
                    print(f"✅ Modifiche salvate: {updates_made} cavi + {bobine_updates} bobine aggiornate")
                else:
                    print("ℹ️  Nessuna modifica necessaria")
            else:
                print("❌ Problema non completamente risolto")
                conn.rollback()
                
    except Exception as e:
        print(f"❌ Errore durante la correzione: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
    finally:
        conn.close()

def verify_boq_fix():
    """Verifica che il BOQ ora mostri correttamente una sola riga."""
    print("\n🧪 VERIFICA BOQ DOPO CORREZIONE")
    print("=" * 50)
    
    db = Database()
    conn = db.get_dict_cursor_connection()
    
    try:
        with conn.cursor() as cur:
            cantiere_id = 1
            
            # Simula la query BOQ per FG16OR16 240MM2
            cur.execute("""
                SELECT
                    tipologia,
                    sezione as formazione,
                    COUNT(*) as num_cavi,
                    COUNT(CASE WHEN stato_installazione != 'Installato' THEN 1 END) as num_cavi_rimanenti,
                    SUM(metri_teorici) as metri_teorici_totali,
                    SUM(CASE WHEN stato_installazione = 'Installato' THEN metratura_reale ELSE 0 END) as metri_reali_posati,
                    SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) as metri_da_posare
                FROM cavi
                WHERE id_cantiere = %s 
                  AND (modificato_manualmente IS NULL OR modificato_manualmente != 3)
                  AND tipologia ILIKE %s 
                  AND sezione ILIKE %s
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """, (cantiere_id, '%FG16OR16%', '%240MM2%'))
            
            boq_results = cur.fetchall()
            
            print(f"Risultati BOQ per FG16OR16 240MM2: {len(boq_results)} righe")
            for i, row in enumerate(boq_results, 1):
                print(f"\n{i}. {row['tipologia']} | {row['formazione']}")
                print(f"   Cavi totali: {row['num_cavi']}")
                print(f"   Cavi rimanenti: {row['num_cavi_rimanenti']}")
                print(f"   Metri teorici: {row['metri_teorici_totali']}")
                print(f"   Metri posati: {row['metri_reali_posati']}")
                print(f"   Metri da posare: {row['metri_da_posare']}")
            
            if len(boq_results) == 1:
                print("\n✅ SUCCESSO! Il BOQ ora mostra una sola riga per FG16OR16 240MM2")
            else:
                print(f"\n❌ PROBLEMA PERSISTENTE: Ancora {len(boq_results)} righe nel BOQ")
                
    except Exception as e:
        print(f"❌ Errore durante la verifica: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_case_sensitivity()
    verify_boq_fix()
