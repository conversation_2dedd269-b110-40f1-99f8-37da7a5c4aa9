{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'ಕಳೆದ' eeee p 'ಕ್ಕೆ'\",\n  yesterday: \"'ನಿನ್ನೆ' p 'ಕ್ಕೆ'\",\n  today: \"'ಇಂದು' p 'ಕ್ಕೆ'\",\n  tomorrow: \"'ನಾಳೆ' p 'ಕ್ಕೆ'\",\n  nextWeek: \"eeee p 'ಕ್ಕೆ'\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/kn/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'ಕಳೆದ' eeee p 'ಕ್ಕೆ'\",\n  yesterday: \"'ನಿನ್ನೆ' p 'ಕ್ಕೆ'\",\n  today: \"'ಇಂದು' p 'ಕ್ಕೆ'\",\n  tomorrow: \"'ನಾಳೆ' p 'ಕ್ಕೆ'\",\n  nextWeek: \"eeee p 'ಕ್ಕೆ'\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,sBAAsB;EAChCC,SAAS,EAAE,mBAAmB;EAC9BC,KAAK,EAAE,iBAAiB;EACxBC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}