{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { applyDefaultDate } from \"../internals/utils/date-utils.js\";\nimport { singleItemFieldValueManager, singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { validateDate } from \"../validation/index.js\";\nimport { useDefaultDates, useUtils } from \"../internals/hooks/useUtils.js\";\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nexport function useDateManager(parameters = {}) {\n  const {\n    enableAccessibleFieldDOMStructure = true\n  } = parameters;\n  return React.useMemo(() => ({\n    valueType: 'date',\n    validator: validateDate,\n    internal_valueManager: singleItemValueManager,\n    internal_fieldValueManager: singleItemFieldValueManager,\n    internal_enableAccessibleFieldDOMStructure: enableAccessibleFieldDOMStructure,\n    internal_useApplyDefaultValuesToFieldInternalProps: useApplyDefaultValuesToDateFieldInternalProps,\n    internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel\n  }), [enableAccessibleFieldDOMStructure]);\n}\nfunction useOpenPickerButtonAriaLabel(value) {\n  const utils = useUtils();\n  const translations = usePickerTranslations();\n  return React.useMemo(() => {\n    const formattedValue = utils.isValid(value) ? utils.format(value, 'fullDate') : null;\n    return translations.openDatePickerDialogue(formattedValue);\n  }, [value, translations, utils]);\n}\nfunction useApplyDefaultValuesToDateFieldInternalProps(internalProps) {\n  const utils = useUtils();\n  const validationProps = useApplyDefaultValuesToDateValidationProps(internalProps);\n  return React.useMemo(() => _extends({}, internalProps, validationProps, {\n    format: internalProps.format ?? utils.formats.keyboardDate\n  }), [internalProps, validationProps, utils]);\n}\nexport function useApplyDefaultValuesToDateValidationProps(props) {\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  return React.useMemo(() => ({\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false,\n    minDate: applyDefaultDate(utils, props.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, props.maxDate, defaultDates.maxDate)\n  }), [props.minDate, props.maxDate, props.disableFuture, props.disablePast, utils, defaultDates]);\n}", "map": {"version": 3, "names": ["_extends", "React", "applyDefaultDate", "singleItemFieldValueManager", "singleItemValueManager", "validateDate", "useDefaultDates", "useUtils", "usePickerTranslations", "useDateManager", "parameters", "enableAccessibleFieldDOMStructure", "useMemo", "valueType", "validator", "internal_valueManager", "internal_fieldValueManager", "internal_enableAccessibleFieldDOMStructure", "internal_useApplyDefaultValuesToFieldInternalProps", "useApplyDefaultValuesToDateFieldInternalProps", "internal_useOpenPickerButtonAriaLabel", "useOpenPickerButtonAriaLabel", "value", "utils", "translations", "formattedValue", "<PERSON><PERSON><PERSON><PERSON>", "format", "openDatePickerDialogue", "internalProps", "validationProps", "useApplyDefaultValuesToDateValidationProps", "formats", "keyboardDate", "props", "defaultDates", "disablePast", "disableFuture", "minDate", "maxDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/managers/useDateManager.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { applyDefaultDate } from \"../internals/utils/date-utils.js\";\nimport { singleItemFieldValueManager, singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { validateDate } from \"../validation/index.js\";\nimport { useDefaultDates, useUtils } from \"../internals/hooks/useUtils.js\";\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nexport function useDateManager(parameters = {}) {\n  const {\n    enableAccessibleFieldDOMStructure = true\n  } = parameters;\n  return React.useMemo(() => ({\n    valueType: 'date',\n    validator: validateDate,\n    internal_valueManager: singleItemValueManager,\n    internal_fieldValueManager: singleItemFieldValueManager,\n    internal_enableAccessibleFieldDOMStructure: enableAccessibleFieldDOMStructure,\n    internal_useApplyDefaultValuesToFieldInternalProps: useApplyDefaultValuesToDateFieldInternalProps,\n    internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel\n  }), [enableAccessibleFieldDOMStructure]);\n}\nfunction useOpenPickerButtonAriaLabel(value) {\n  const utils = useUtils();\n  const translations = usePickerTranslations();\n  return React.useMemo(() => {\n    const formattedValue = utils.isValid(value) ? utils.format(value, 'fullDate') : null;\n    return translations.openDatePickerDialogue(formattedValue);\n  }, [value, translations, utils]);\n}\nfunction useApplyDefaultValuesToDateFieldInternalProps(internalProps) {\n  const utils = useUtils();\n  const validationProps = useApplyDefaultValuesToDateValidationProps(internalProps);\n  return React.useMemo(() => _extends({}, internalProps, validationProps, {\n    format: internalProps.format ?? utils.formats.keyboardDate\n  }), [internalProps, validationProps, utils]);\n}\nexport function useApplyDefaultValuesToDateValidationProps(props) {\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  return React.useMemo(() => ({\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false,\n    minDate: applyDefaultDate(utils, props.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, props.maxDate, defaultDates.maxDate)\n  }), [props.minDate, props.maxDate, props.disableFuture, props.disablePast, utils, defaultDates]);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,2BAA2B,EAAEC,sBAAsB,QAAQ,qCAAqC;AACzG,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,eAAe,EAAEC,QAAQ,QAAQ,gCAAgC;AAC1E,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,OAAO,SAASC,cAAcA,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC9C,MAAM;IACJC,iCAAiC,GAAG;EACtC,CAAC,GAAGD,UAAU;EACd,OAAOT,KAAK,CAACW,OAAO,CAAC,OAAO;IAC1BC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAET,YAAY;IACvBU,qBAAqB,EAAEX,sBAAsB;IAC7CY,0BAA0B,EAAEb,2BAA2B;IACvDc,0CAA0C,EAAEN,iCAAiC;IAC7EO,kDAAkD,EAAEC,6CAA6C;IACjGC,qCAAqC,EAAEC;EACzC,CAAC,CAAC,EAAE,CAACV,iCAAiC,CAAC,CAAC;AAC1C;AACA,SAASU,4BAA4BA,CAACC,KAAK,EAAE;EAC3C,MAAMC,KAAK,GAAGhB,QAAQ,CAAC,CAAC;EACxB,MAAMiB,YAAY,GAAGhB,qBAAqB,CAAC,CAAC;EAC5C,OAAOP,KAAK,CAACW,OAAO,CAAC,MAAM;IACzB,MAAMa,cAAc,GAAGF,KAAK,CAACG,OAAO,CAACJ,KAAK,CAAC,GAAGC,KAAK,CAACI,MAAM,CAACL,KAAK,EAAE,UAAU,CAAC,GAAG,IAAI;IACpF,OAAOE,YAAY,CAACI,sBAAsB,CAACH,cAAc,CAAC;EAC5D,CAAC,EAAE,CAACH,KAAK,EAAEE,YAAY,EAAED,KAAK,CAAC,CAAC;AAClC;AACA,SAASJ,6CAA6CA,CAACU,aAAa,EAAE;EACpE,MAAMN,KAAK,GAAGhB,QAAQ,CAAC,CAAC;EACxB,MAAMuB,eAAe,GAAGC,0CAA0C,CAACF,aAAa,CAAC;EACjF,OAAO5B,KAAK,CAACW,OAAO,CAAC,MAAMZ,QAAQ,CAAC,CAAC,CAAC,EAAE6B,aAAa,EAAEC,eAAe,EAAE;IACtEH,MAAM,EAAEE,aAAa,CAACF,MAAM,IAAIJ,KAAK,CAACS,OAAO,CAACC;EAChD,CAAC,CAAC,EAAE,CAACJ,aAAa,EAAEC,eAAe,EAAEP,KAAK,CAAC,CAAC;AAC9C;AACA,OAAO,SAASQ,0CAA0CA,CAACG,KAAK,EAAE;EAChE,MAAMX,KAAK,GAAGhB,QAAQ,CAAC,CAAC;EACxB,MAAM4B,YAAY,GAAG7B,eAAe,CAAC,CAAC;EACtC,OAAOL,KAAK,CAACW,OAAO,CAAC,OAAO;IAC1BwB,WAAW,EAAEF,KAAK,CAACE,WAAW,IAAI,KAAK;IACvCC,aAAa,EAAEH,KAAK,CAACG,aAAa,IAAI,KAAK;IAC3CC,OAAO,EAAEpC,gBAAgB,CAACqB,KAAK,EAAEW,KAAK,CAACI,OAAO,EAAEH,YAAY,CAACG,OAAO,CAAC;IACrEC,OAAO,EAAErC,gBAAgB,CAACqB,KAAK,EAAEW,KAAK,CAACK,OAAO,EAAEJ,YAAY,CAACI,OAAO;EACtE,CAAC,CAAC,EAAE,CAACL,KAAK,CAACI,OAAO,EAAEJ,KAAK,CAACK,OAAO,EAAEL,KAAK,CAACG,aAAa,EAAEH,KAAK,CAACE,WAAW,EAAEb,KAAK,EAAEY,YAAY,CAAC,CAAC;AAClG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}