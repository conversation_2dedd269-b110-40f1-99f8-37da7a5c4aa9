{"ast": null, "code": "import { formatDistance } from \"./mk/_lib/formatDistance.js\";\nimport { formatLong } from \"./mk/_lib/formatLong.js\";\nimport { formatRelative } from \"./mk/_lib/formatRelative.js\";\nimport { localize } from \"./mk/_lib/localize.js\";\nimport { match } from \"./mk/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Macedonian locale.\n * @language Macedonian\n * @iso-639-2 mkd\n * <AUTHOR> [@vlahupetar](https://github.com/vlahupetar)\n * <AUTHOR> Beqiri [@altrim](https://github.com/altrim)\n */\nexport const mk = {\n  code: \"mk\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default mk;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "mk", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/mk.js"], "sourcesContent": ["import { formatDistance } from \"./mk/_lib/formatDistance.js\";\nimport { formatLong } from \"./mk/_lib/formatLong.js\";\nimport { formatRelative } from \"./mk/_lib/formatRelative.js\";\nimport { localize } from \"./mk/_lib/localize.js\";\nimport { match } from \"./mk/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Macedonian locale.\n * @language Macedonian\n * @iso-639-2 mkd\n * <AUTHOR> [@vlahupetar](https://github.com/vlahupetar)\n * <AUTHOR> Beqiri [@altrim](https://github.com/altrim)\n */\nexport const mk = {\n  code: \"mk\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default mk;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}