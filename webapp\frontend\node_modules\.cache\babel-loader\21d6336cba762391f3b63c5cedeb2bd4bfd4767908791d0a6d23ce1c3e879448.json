{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: date => {\n    const day = date.getDay();\n    switch (day) {\n      case 0:\n        return \"'прошле недеље у' p\";\n      case 3:\n        return \"'прошле среде у' p\";\n      case 6:\n        return \"'прошле суботе у' p\";\n      default:\n        return \"'прошли' EEEE 'у' p\";\n    }\n  },\n  yesterday: \"'јуче у' p\",\n  today: \"'данас у' p\",\n  tomorrow: \"'сутра у' p\",\n  nextWeek: date => {\n    const day = date.getDay();\n    switch (day) {\n      case 0:\n        return \"'следеће недеље у' p\";\n      case 3:\n        return \"'следећу среду у' p\";\n      case 6:\n        return \"'следећу суботу у' p\";\n      default:\n        return \"'следећи' EEEE 'у' p\";\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "day", "getDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_baseDate", "_options", "format"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/sr/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: (date) => {\n    const day = date.getDay();\n\n    switch (day) {\n      case 0:\n        return \"'прошле недеље у' p\";\n      case 3:\n        return \"'прошле среде у' p\";\n      case 6:\n        return \"'прошле суботе у' p\";\n      default:\n        return \"'прошли' EEEE 'у' p\";\n    }\n  },\n  yesterday: \"'јуче у' p\",\n  today: \"'данас у' p\",\n  tomorrow: \"'сутра у' p\",\n  nextWeek: (date) => {\n    const day = date.getDay();\n\n    switch (day) {\n      case 0:\n        return \"'следеће недеље у' p\";\n      case 3:\n        return \"'следећу среду у' p\";\n      case 6:\n        return \"'следећу суботу у' p\";\n      default:\n        return \"'следећи' EEEE 'у' p\";\n    }\n  },\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAGC,IAAI,IAAK;IAClB,MAAMC,GAAG,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC;IAEzB,QAAQD,GAAG;MACT,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B,KAAK,CAAC;QACJ,OAAO,oBAAoB;MAC7B,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B;QACE,OAAO,qBAAqB;IAChC;EACF,CAAC;EACDE,SAAS,EAAE,YAAY;EACvBC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAGN,IAAI,IAAK;IAClB,MAAMC,GAAG,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC;IAEzB,QAAQD,GAAG;MACT,KAAK,CAAC;QACJ,OAAO,sBAAsB;MAC/B,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B,KAAK,CAAC;QACJ,OAAO,sBAAsB;MAC/B;QACE,OAAO,sBAAsB;IACjC;EACF,CAAC;EACDM,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAET,IAAI,EAAEU,SAAS,EAAEC,QAAQ,KAAK;EAClE,MAAMC,MAAM,GAAGd,oBAAoB,CAACW,KAAK,CAAC;EAE1C,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACZ,IAAI,CAAC;EACrB;EAEA,OAAOY,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}