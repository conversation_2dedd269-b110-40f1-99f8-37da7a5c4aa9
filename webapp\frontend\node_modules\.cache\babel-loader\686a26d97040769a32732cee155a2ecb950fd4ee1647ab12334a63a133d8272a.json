{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"λιγότερο από ένα δευτερόλεπτο\",\n    other: \"λιγότερο από {{count}} δευτερόλεπτα\"\n  },\n  xSeconds: {\n    one: \"1 δευτερόλεπτο\",\n    other: \"{{count}} δευτερόλεπτα\"\n  },\n  halfAMinute: \"μισό λεπτό\",\n  lessThanXMinutes: {\n    one: \"λιγότερο από ένα λεπτό\",\n    other: \"λιγότερο από {{count}} λεπτά\"\n  },\n  xMinutes: {\n    one: \"1 λεπτό\",\n    other: \"{{count}} λεπτά\"\n  },\n  aboutXHours: {\n    one: \"περίπου 1 ώρα\",\n    other: \"περίπου {{count}} ώρες\"\n  },\n  xHours: {\n    one: \"1 ώρα\",\n    other: \"{{count}} ώρες\"\n  },\n  xDays: {\n    one: \"1 ημέρα\",\n    other: \"{{count}} ημέρες\"\n  },\n  aboutXWeeks: {\n    one: \"περίπου 1 εβδομάδα\",\n    other: \"περίπου {{count}} εβδομάδες\"\n  },\n  xWeeks: {\n    one: \"1 εβδομάδα\",\n    other: \"{{count}} εβδομάδες\"\n  },\n  aboutXMonths: {\n    one: \"περίπου 1 μήνας\",\n    other: \"περίπου {{count}} μήνες\"\n  },\n  xMonths: {\n    one: \"1 μήνας\",\n    other: \"{{count}} μήνες\"\n  },\n  aboutXYears: {\n    one: \"περίπου 1 χρόνο\",\n    other: \"περίπου {{count}} χρόνια\"\n  },\n  xYears: {\n    one: \"1 χρόνο\",\n    other: \"{{count}} χρόνια\"\n  },\n  overXYears: {\n    one: \"πάνω από 1 χρόνο\",\n    other: \"πάνω από {{count}} χρόνια\"\n  },\n  almostXYears: {\n    one: \"περίπου 1 χρόνο\",\n    other: \"περίπου {{count}} χρόνια\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"σε \" + result;\n    } else {\n      return result + \" πριν\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/el/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"λιγότερο από ένα δευτερόλεπτο\",\n    other: \"λιγότερο από {{count}} δευτερόλεπτα\",\n  },\n\n  xSeconds: {\n    one: \"1 δευτερόλεπτο\",\n    other: \"{{count}} δευτερόλεπτα\",\n  },\n\n  halfAMinute: \"μισό λεπτό\",\n\n  lessThanXMinutes: {\n    one: \"λιγότερο από ένα λεπτό\",\n    other: \"λιγότερο από {{count}} λεπτά\",\n  },\n\n  xMinutes: {\n    one: \"1 λεπτό\",\n    other: \"{{count}} λεπτά\",\n  },\n\n  aboutXHours: {\n    one: \"περίπου 1 ώρα\",\n    other: \"περίπου {{count}} ώρες\",\n  },\n\n  xHours: {\n    one: \"1 ώρα\",\n    other: \"{{count}} ώρες\",\n  },\n\n  xDays: {\n    one: \"1 ημέρα\",\n    other: \"{{count}} ημέρες\",\n  },\n\n  aboutXWeeks: {\n    one: \"περίπου 1 εβδομάδα\",\n    other: \"περίπου {{count}} εβδομάδες\",\n  },\n\n  xWeeks: {\n    one: \"1 εβδομάδα\",\n    other: \"{{count}} εβδομάδες\",\n  },\n\n  aboutXMonths: {\n    one: \"περίπου 1 μήνας\",\n    other: \"περίπου {{count}} μήνες\",\n  },\n\n  xMonths: {\n    one: \"1 μήνας\",\n    other: \"{{count}} μήνες\",\n  },\n\n  aboutXYears: {\n    one: \"περίπου 1 χρόνο\",\n    other: \"περίπου {{count}} χρόνια\",\n  },\n\n  xYears: {\n    one: \"1 χρόνο\",\n    other: \"{{count}} χρόνια\",\n  },\n\n  overXYears: {\n    one: \"πάνω από 1 χρόνο\",\n    other: \"πάνω από {{count}} χρόνια\",\n  },\n\n  almostXYears: {\n    one: \"περίπου 1 χρόνο\",\n    other: \"περίπου {{count}} χρόνια\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"σε \" + result;\n    } else {\n      return result + \" πριν\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,+BAA+B;IACpCC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,YAAY;EAEzBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,wBAAwB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,OAAO;IACzB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}