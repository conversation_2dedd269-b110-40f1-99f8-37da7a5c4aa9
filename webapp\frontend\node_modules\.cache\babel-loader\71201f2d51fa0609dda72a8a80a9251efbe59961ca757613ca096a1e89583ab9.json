{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"М.А\", \"М\"],\n  abbreviated: [\"М.А\", \"М\"],\n  wide: [\"Милоддан Аввалги\", \"Мило<PERSON><PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-чор.\", \"2-чор.\", \"3-чор.\", \"4-чор.\"],\n  wide: [\"1-чорак\", \"2-чорак\", \"3-чорак\", \"4-чорак\"]\n};\nconst monthValues = {\n  narrow: [\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"],\n  abbreviated: [\"янв\", \"фев\", \"мар\", \"апр\", \"май\", \"июн\", \"июл\", \"авг\", \"сен\", \"окт\", \"ноя\", \"дек\"],\n  wide: [\"январ\", \"феврал\", \"март\", \"апрел\", \"май\", \"июн\", \"июл\", \"август\", \"сентабр\", \"октабр\", \"ноябр\", \"декабр\"]\n};\nconst dayValues = {\n  narrow: [\"Я\", \"Д\", \"С\", \"Ч\", \"П\", \"Ж\", \"Ш\"],\n  short: [\"як\", \"ду\", \"се\", \"чо\", \"па\", \"жу\", \"ша\"],\n  abbreviated: [\"якш\", \"душ\", \"сеш\", \"чор\", \"пай\", \"жум\", \"шан\"],\n  wide: [\"якшанба\", \"душанба\", \"сешанба\", \"чоршанба\", \"пайшанба\", \"жума\", \"шанба\"]\n};\nconst dayPeriodValues = {\n  any: {\n    am: \"П.О.\",\n    pm: \"П.К.\",\n    midnight: \"ярим тун\",\n    noon: \"пешин\",\n    morning: \"эрталаб\",\n    afternoon: \"пешиндан кейин\",\n    evening: \"кечаси\",\n    night: \"тун\"\n  }\n};\nconst formattingDayPeriodValues = {\n  any: {\n    am: \"П.О.\",\n    pm: \"П.К.\",\n    midnight: \"ярим тун\",\n    noon: \"пешин\",\n    morning: \"эрталаб\",\n    afternoon: \"пешиндан кейин\",\n    evening: \"кечаси\",\n    night: \"тун\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"any\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "any", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/uz-Cyrl/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"М.А\", \"М\"],\n  abbreviated: [\"М.А\", \"М\"],\n  wide: [\"Милоддан Аввалги\", \"Мило<PERSON><PERSON>\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-чор.\", \"2-чор.\", \"3-чор.\", \"4-чор.\"],\n  wide: [\"1-чорак\", \"2-чорак\", \"3-чорак\", \"4-чорак\"],\n};\n\nconst monthValues = {\n  narrow: [\"Я\", \"Ф\", \"М\", \"А\", \"М\", \"И\", \"И\", \"А\", \"С\", \"О\", \"Н\", \"Д\"],\n  abbreviated: [\n    \"янв\",\n    \"фев\",\n    \"мар\",\n    \"апр\",\n    \"май\",\n    \"июн\",\n    \"июл\",\n    \"авг\",\n    \"сен\",\n    \"окт\",\n    \"ноя\",\n    \"дек\",\n  ],\n\n  wide: [\n    \"январ\",\n    \"феврал\",\n    \"март\",\n    \"апрел\",\n    \"май\",\n    \"июн\",\n    \"июл\",\n    \"август\",\n    \"сентабр\",\n    \"октабр\",\n    \"ноябр\",\n    \"декабр\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Я\", \"Д\", \"С\", \"Ч\", \"П\", \"Ж\", \"Ш\"],\n  short: [\"як\", \"ду\", \"се\", \"чо\", \"па\", \"жу\", \"ша\"],\n  abbreviated: [\"якш\", \"душ\", \"сеш\", \"чор\", \"пай\", \"жум\", \"шан\"],\n  wide: [\n    \"якшанба\",\n    \"душанба\",\n    \"сешанба\",\n    \"чоршанба\",\n    \"пайшанба\",\n    \"жума\",\n    \"шанба\",\n  ],\n};\n\nconst dayPeriodValues = {\n  any: {\n    am: \"П.О.\",\n    pm: \"П.К.\",\n    midnight: \"ярим тун\",\n    noon: \"пешин\",\n    morning: \"эрталаб\",\n    afternoon: \"пешиндан кейин\",\n    evening: \"кечаси\",\n    night: \"тун\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  any: {\n    am: \"П.О.\",\n    pm: \"П.К.\",\n    midnight: \"ярим тун\",\n    noon: \"пешин\",\n    morning: \"эрталаб\",\n    afternoon: \"пешиндан кейин\",\n    evening: \"кечаси\",\n    night: \"тун\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"any\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpBC,WAAW,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACzBC,IAAI,EAAE,CAAC,kBAAkB,EAAE,SAAS;AACtC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrDC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;AACnD,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,OAAO,EACP,QAAQ;AAEZ,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CACJ,SAAS,EACT,SAAS,EACT,SAAS,EACT,UAAU,EACV,UAAU,EACV,MAAM,EACN,OAAO;AAEX,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBC,GAAG,EAAE;IACHC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChCT,GAAG,EAAE;IACHC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,gBAAgB;IAC3BC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBJ,aAAa;EAEbK,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,KAAK;IACnBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}