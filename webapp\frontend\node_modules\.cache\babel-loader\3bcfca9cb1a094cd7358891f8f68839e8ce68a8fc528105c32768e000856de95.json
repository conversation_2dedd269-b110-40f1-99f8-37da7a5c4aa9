{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ម.គស\", \"គស\"],\n  abbreviated: [\"មុនគ.ស\", \"គ.ស\"],\n  wide: [\"មុនគ្រិស្តសករាជ\", \"នៃគ្រិស្តសករាជ\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"ត្រីមាសទី 1\", \"ត្រីមាសទី 2\", \"ត្រីមាសទី 3\", \"ត្រីមាសទី 4\"]\n};\nconst monthValues = {\n  narrow: [\"ម.ក\", \"ក.ម\", \"មិ\", \"ម.ស\", \"ឧ.ស\", \"ម.ថ\", \"ក.ដ\", \"សី\", \"កញ\", \"តុ\", \"វិ\", \"ធ\"],\n  abbreviated: [\"មករា\", \"កុម្ភៈ\", \"មីនា\", \"មេសា\", \"ឧសភា\", \"មិថុនា\", \"កក្កដា\", \"សីហា\", \"កញ្ញា\", \"តុលា\", \"វិច្ឆិកា\", \"ធ្នូ\"],\n  wide: [\"មករា\", \"កុម្ភៈ\", \"មីនា\", \"មេសា\", \"ឧសភា\", \"មិថុនា\", \"កក្កដា\", \"សីហា\", \"កញ្ញា\", \"តុលា\", \"វិច្ឆិកា\", \"ធ្នូ\"]\n};\nconst dayValues = {\n  narrow: [\"អា\", \"ច\", \"អ\", \"ព\", \"ព្រ\", \"សុ\", \"ស\"],\n  short: [\"អា\", \"ច\", \"អ\", \"ព\", \"ព្រ\", \"សុ\", \"ស\"],\n  abbreviated: [\"អា\", \"ច\", \"អ\", \"ព\", \"ព្រ\", \"សុ\", \"ស\"],\n  wide: [\"អាទិត្យ\", \"ចន្ទ\", \"អង្គារ\", \"ពុធ\", \"ព្រហស្បតិ៍\", \"សុក្រ\", \"សៅរ៍\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  },\n  abbreviated: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  },\n  wide: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  },\n  abbreviated: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  },\n  wide: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _) => {\n  const number = Number(dirtyNumber);\n  return number.toString();\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_", "number", "Number", "toString", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/km/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"ម.គស\", \"គស\"],\n  abbreviated: [\"មុនគ.ស\", \"គ.ស\"],\n  wide: [\"មុនគ្រិស្តសករាជ\", \"នៃគ្រិស្តសករាជ\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"ត្រីមាសទី 1\", \"ត្រីមាសទី 2\", \"ត្រីមាសទី 3\", \"ត្រីមាសទី 4\"],\n};\n\nconst monthValues = {\n  narrow: [\n    \"ម.ក\",\n    \"ក.ម\",\n    \"មិ\",\n    \"ម.ស\",\n    \"ឧ.ស\",\n    \"ម.ថ\",\n    \"ក.ដ\",\n    \"សី\",\n    \"កញ\",\n    \"តុ\",\n    \"វិ\",\n    \"ធ\",\n  ],\n\n  abbreviated: [\n    \"មករា\",\n    \"កុម្ភៈ\",\n    \"មីនា\",\n    \"មេសា\",\n    \"ឧសភា\",\n    \"មិថុនា\",\n    \"កក្កដា\",\n    \"សីហា\",\n    \"កញ្ញា\",\n    \"តុលា\",\n    \"វិច្ឆិកា\",\n    \"ធ្នូ\",\n  ],\n\n  wide: [\n    \"មករា\",\n    \"កុម្ភៈ\",\n    \"មីនា\",\n    \"មេសា\",\n    \"ឧសភា\",\n    \"មិថុនា\",\n    \"កក្កដា\",\n    \"សីហា\",\n    \"កញ្ញា\",\n    \"តុលា\",\n    \"វិច្ឆិកា\",\n    \"ធ្នូ\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"អា\", \"ច\", \"អ\", \"ព\", \"ព្រ\", \"សុ\", \"ស\"],\n  short: [\"អា\", \"ច\", \"អ\", \"ព\", \"ព្រ\", \"សុ\", \"ស\"],\n  abbreviated: [\"អា\", \"ច\", \"អ\", \"ព\", \"ព្រ\", \"សុ\", \"ស\"],\n  wide: [\"អាទិត្យ\", \"ចន្ទ\", \"អង្គារ\", \"ពុធ\", \"ព្រហស្បតិ៍\", \"សុក្រ\", \"សៅរ៍\"],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\",\n  },\n  abbreviated: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\",\n  },\n  wide: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\",\n  },\n  abbreviated: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\",\n  },\n  wide: {\n    am: \"ព្រឹក\",\n    pm: \"ល្ងាច\",\n    midnight: \"​ពេលកណ្ដាលអធ្រាត្រ\",\n    noon: \"ពេលថ្ងៃត្រង់\",\n    morning: \"ពេលព្រឹក\",\n    afternoon: \"ពេលរសៀល\",\n    evening: \"ពេលល្ងាច\",\n    night: \"ពេលយប់\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _) => {\n  const number = Number(dirtyNumber);\n  return number.toString();\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;EACtBC,WAAW,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC;EAC9BC,IAAI,EAAE,CAAC,iBAAiB,EAAE,gBAAgB;AAC5C,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CACN,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,GAAG,CACJ;EAEDC,WAAW,EAAE,CACX,MAAM,EACN,QAAQ,EACR,MAAM,EACN,MAAM,EACN,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,EACP,MAAM,EACN,UAAU,EACV,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,MAAM,EACN,QAAQ,EACR,MAAM,EACN,MAAM,EACN,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,EACP,MAAM,EACN,UAAU,EACV,MAAM;AAEV,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC;EAC/CM,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC;EAC9CL,WAAW,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC;EACpDC,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM;AAC1E,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,QAAQ,EAAE,oBAAoB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,CAAC,KAAK;EACxC,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,OAAOE,MAAM,CAACE,QAAQ,CAAC,CAAC;AAC1B,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAG;EACtBN,aAAa;EAEbO,GAAG,EAAE1B,eAAe,CAAC;IACnB2B,MAAM,EAAE1B,SAAS;IACjB2B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE7B,eAAe,CAAC;IACvB2B,MAAM,EAAEtB,aAAa;IACrBuB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE/B,eAAe,CAAC;IACrB2B,MAAM,EAAErB,WAAW;IACnBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAEhC,eAAe,CAAC;IACnB2B,MAAM,EAAEpB,SAAS;IACjBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEjC,eAAe,CAAC;IACzB2B,MAAM,EAAElB,eAAe;IACvBmB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEhB,yBAAyB;IAC3CiB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}