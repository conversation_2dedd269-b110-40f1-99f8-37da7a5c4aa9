{"ast": null, "code": "import { formatDistance } from \"./zh-HK/_lib/formatDistance.js\";\nimport { formatLong } from \"./zh-HK/_lib/formatLong.js\";\nimport { formatRelative } from \"./zh-HK/_lib/formatRelative.js\";\nimport { localize } from \"./zh-HK/_lib/localize.js\";\nimport { match } from \"./zh-HK/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Chinese Traditional locale.\n * @language Chinese Traditional\n * @iso-639-2 zho\n * <AUTHOR> [@gaplo](https://github.com/gaplo)\n */\nexport const zhHK = {\n  code: \"zh-HK\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default zhHK;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "zhHK", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/zh-HK.js"], "sourcesContent": ["import { formatDistance } from \"./zh-HK/_lib/formatDistance.js\";\nimport { formatLong } from \"./zh-HK/_lib/formatLong.js\";\nimport { formatRelative } from \"./zh-HK/_lib/formatRelative.js\";\nimport { localize } from \"./zh-HK/_lib/localize.js\";\nimport { match } from \"./zh-HK/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Chinese Traditional locale.\n * @language Chinese Traditional\n * @iso-639-2 zho\n * <AUTHOR> [@gaplo](https://github.com/gaplo)\n */\nexport const zhHK = {\n  code: \"zh-HK\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default zhHK;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,KAAK,QAAQ,uBAAuB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}