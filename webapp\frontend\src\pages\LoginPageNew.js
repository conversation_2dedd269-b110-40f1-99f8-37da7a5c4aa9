import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Card,
  CardContent,
  CardActions,
  Grid,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import {
  AdminPanelSettings as AdminIcon,
  Person as UserIcon,
  Construction as ConstructionIcon,
  Password as PasswordIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const LoginPageNew = () => {
  const [loginType, setLoginType] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPasswordRecoveryDialog, setShowPasswordRecoveryDialog] = useState(false);
  const [recoveryEmail, setRecoveryEmail] = useState('');

  // Credenziali per i diversi tipi di login
  const [adminCredentials, setAdminCredentials] = useState({
    username: '',
    password: ''
  });

  const [userCredentials, setUserCredentials] = useState({
    username: '',
    password: ''
  });

  const [cantiereCredentials, setCantiereCredentials] = useState({
    codice_univoco: '',
    password: ''
  });

  const { login } = useAuth();
  const navigate = useNavigate();

  // Gestione dell'input per il login amministratore
  const handleAdminInputChange = (e) => {
    const { name, value } = e.target;
    setAdminCredentials({
      ...adminCredentials,
      [name]: value
    });
  };

  // Gestione dell'input per il login utente standard
  const handleUserInputChange = (e) => {
    const { name, value } = e.target;
    setUserCredentials({
      ...userCredentials,
      [name]: value
    });
  };

  // Gestione dell'input per il login cantiere
  const handleCantiereInputChange = (e) => {
    const { name, value } = e.target;
    setCantiereCredentials({
      ...cantiereCredentials,
      [name]: value
    });
  };

  // Gestione dell'input per il recupero password
  const handleRecoveryEmailChange = (e) => {
    setRecoveryEmail(e.target.value);
  };

  // Gestione del login amministratore
  const handleAdminLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    console.log('Tentativo di login amministratore con:', adminCredentials);

    try {
      // Verifica che i campi non siano vuoti
      if (!adminCredentials.username || !adminCredentials.password) {
        console.log('Campi vuoti nel form di login admin');
        setError('Username e password non possono essere vuoti');
        setLoading(false);
        return;
      }

      console.log('Chiamata alla funzione login per admin...');
      try {
        const userData = await login(adminCredentials, 'standard');
        console.log('Login admin completato, dati utente:', userData);

        // Verifica che l'utente sia un amministratore
        if (userData.role !== 'owner') {
          console.log('Utente non ha ruolo owner:', userData.role);
          setError('Non hai i permessi di amministratore');
          setLoading(false);
          return;
        }

        // Reindirizza alla dashboard di amministrazione
        console.log('Reindirizzamento a /dashboard/admin');
        // Utilizza navigate invece di window.location per evitare refresh completo
        navigate('/dashboard/admin');
      } catch (loginError) {
        console.error('Errore specifico durante login admin:', loginError);
        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');
        setLoading(false);
      }
    } catch (err) {
      console.error('Errore generale durante login admin:', err);
      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');
      setLoading(false);
    }
    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione
  };

  // Gestione del login utente standard
  const handleUserLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    console.log('Tentativo di login utente standard con:', userCredentials);

    try {
      // Verifica che i campi non siano vuoti
      if (!userCredentials.username || !userCredentials.password) {
        console.log('Campi vuoti nel form di login utente standard');
        setError('Username e password non possono essere vuoti');
        setLoading(false);
        return;
      }

      console.log('Chiamata alla funzione login per utente standard...');
      try {
        const userData = await login(userCredentials, 'standard');
        console.log('Login utente standard completato, dati utente:', userData);

        // Verifica che l'utente sia un utente standard
        if (userData.role !== 'user') {
          console.log('Utente non ha ruolo user:', userData.role);
          setError('Non hai i permessi di utente standard');
          setLoading(false);
          return;
        }

        // Reindirizza alla dashboard utente
        console.log('Reindirizzamento a /dashboard/cantieri');
        // Utilizza navigate invece di window.location per evitare refresh completo
        navigate('/dashboard/cantieri');
      } catch (loginError) {
        console.error('Errore specifico durante login utente standard:', loginError);
        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');
        setLoading(false);
      }
    } catch (err) {
      console.error('Errore generale durante login utente standard:', err);
      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');
      setLoading(false);
    }
    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione
  };

  // Gestione del login cantiere
  const handleCantiereLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    console.log('Tentativo di login cantiere con:', cantiereCredentials);

    try {
      // Verifica che i campi non siano vuoti
      if (!cantiereCredentials.codice_univoco || !cantiereCredentials.password) {
        console.log('Campi vuoti nel form di login cantiere');
        setError('Codice univoco e password non possono essere vuoti');
        setLoading(false);
        return;
      }

      console.log('Chiamata alla funzione login per cantiere...');
      try {
        const userData = await login(cantiereCredentials, 'cantiere');
        console.log('Login cantiere completato, dati utente:', userData);

        // Salva esplicitamente l'ID e il nome del cantiere nel localStorage
        if (userData.cantiere_id) {
          console.log('Salvando ID cantiere nel localStorage:', userData.cantiere_id);
          localStorage.setItem('selectedCantiereId', userData.cantiere_id.toString());
          localStorage.setItem('selectedCantiereName', userData.cantiere_name || `Cantiere ${userData.cantiere_id}`);
        } else {
          console.warn('Risposta login cantiere non contiene cantiere_id:', userData);
        }

        // Breve ritardo per assicurarsi che i dati siano salvati nel localStorage
        setTimeout(() => {
          // Reindirizza alla pagina di visualizzazione cavi
          console.log('Reindirizzamento a /dashboard/cavi/visualizza');
          // Utilizza navigate invece di window.location per evitare refresh completo
          navigate('/dashboard/cavi/visualizza');
        }, 500);
      } catch (loginError) {
        console.error('Errore specifico durante login cantiere:', loginError);
        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');
        setLoading(false);
      }
    } catch (err) {
      console.error('Errore generale durante login cantiere:', err);
      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');
      setLoading(false);
    }
    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione
  };

  // Gestione del recupero password
  const handlePasswordRecovery = () => {
    // Qui implementeremo la logica per il recupero della password
    alert('Funzionalità di recupero password non ancora implementata');
    setShowPasswordRecoveryDialog(false);
  };

  // Torna al menu principale
  const handleBackToMainMenu = () => {
    setLoginType(null);
    setError('');
  };

  // Renderizza il form di login appropriato in base al tipo selezionato
  const renderLoginForm = () => {
    switch (loginType) {
      case 'admin':
        return (
          <Box
            component="form"
            onSubmit={handleAdminLogin}
            noValidate
            sx={{
              maxWidth: '400px',
              margin: '0 auto',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}
          >
            <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
              Login Amministratore
            </Typography>
            <TextField
              margin="normal"
              required
              id="admin-username"
              label="Username"
              name="username"
              autoComplete="username"
              autoFocus
              value={adminCredentials.username}
              onChange={handleAdminInputChange}
              sx={{ width: '100%', maxWidth: '350px' }}
              variant="outlined"
            />
            <TextField
              margin="normal"
              required
              name="password"
              label="Password"
              type="password"
              id="admin-password"
              autoComplete="current-password"
              value={adminCredentials.password}
              onChange={handleAdminInputChange}
              sx={{ width: '100%', maxWidth: '350px' }}
              variant="outlined"
            />
            <Button
              type="submit"
              variant="contained"
              sx={{
                mt: 3,
                mb: 2,
                width: '100%',
                maxWidth: '350px',
                py: 1.5,
                fontSize: '1rem',
                fontWeight: 600
              }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Accedi'}
            </Button>
            <Button
              variant="outlined"
              onClick={handleBackToMainMenu}
              sx={{
                mb: 2,
                width: '100%',
                maxWidth: '350px',
                py: 1.5
              }}
            >
              Torna al Menu Principale
            </Button>
          </Box>
        );
      case 'user':
        return (
          <Box
            component="form"
            onSubmit={handleUserLogin}
            noValidate
            sx={{
              maxWidth: '400px',
              margin: '0 auto',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}
          >
            <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
              Login Utente Standard
            </Typography>
            <TextField
              margin="normal"
              required
              id="user-username"
              label="Username"
              name="username"
              autoComplete="username"
              autoFocus
              value={userCredentials.username}
              onChange={handleUserInputChange}
              sx={{ width: '100%', maxWidth: '350px' }}
              variant="outlined"
            />
            <TextField
              margin="normal"
              required
              name="password"
              label="Password"
              type="password"
              id="user-password"
              autoComplete="current-password"
              value={userCredentials.password}
              onChange={handleUserInputChange}
              sx={{ width: '100%', maxWidth: '350px' }}
              variant="outlined"
            />
            <Button
              type="submit"
              variant="contained"
              sx={{
                mt: 3,
                mb: 2,
                width: '100%',
                maxWidth: '350px',
                py: 1.5,
                fontSize: '1rem',
                fontWeight: 600
              }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Accedi'}
            </Button>
            <Button
              variant="outlined"
              onClick={handleBackToMainMenu}
              sx={{
                mb: 2,
                width: '100%',
                maxWidth: '350px',
                py: 1.5
              }}
            >
              Torna al Menu Principale
            </Button>
          </Box>
        );
      case 'cantiere':
        return (
          <Box
            component="form"
            onSubmit={handleCantiereLogin}
            noValidate
            sx={{
              maxWidth: '400px',
              margin: '0 auto',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}
          >
            <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
              Login Utente Cantiere
            </Typography>
            <TextField
              margin="normal"
              required
              id="codice_univoco"
              label="Codice Univoco del Cantiere"
              name="codice_univoco"
              autoComplete="off"
              autoFocus
              value={cantiereCredentials.codice_univoco}
              onChange={handleCantiereInputChange}
              sx={{ width: '100%', maxWidth: '350px' }}
              variant="outlined"
            />
            <TextField
              margin="normal"
              required
              name="password"
              label="Password"
              type="password"
              id="cantiere-password"
              autoComplete="current-password"
              value={cantiereCredentials.password}
              onChange={handleCantiereInputChange}
              sx={{ width: '100%', maxWidth: '350px' }}
              variant="outlined"
            />
            <Button
              type="submit"
              variant="contained"
              sx={{
                mt: 3,
                mb: 2,
                width: '100%',
                maxWidth: '350px',
                py: 1.5,
                fontSize: '1rem',
                fontWeight: 600
              }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Accedi'}
            </Button>
            <Button
              variant="outlined"
              onClick={handleBackToMainMenu}
              sx={{
                mb: 2,
                width: '100%',
                maxWidth: '350px',
                py: 1.5
              }}
            >
              Torna al Menu Principale
            </Button>
          </Box>
        );
      default:
        return null;
    }
  };

  // Renderizza il menu principale di login
  const renderMainMenu = () => {
    return (
      <Grid container spacing={3} sx={{ maxWidth: '900px', margin: '0 auto' }}>
        <Grid item xs={12} sm={6} md={6}>
          <Card
            sx={{
              height: '100%',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: 4
              },
              background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              color: 'white'
            }}
            onClick={() => setLoginType('admin')}
          >
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <AdminIcon sx={{ fontSize: 64, mb: 2, opacity: 0.9 }} />
              <Typography variant="h5" component="div" gutterBottom fontWeight="600">
                Amministratore
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9, lineHeight: 1.6 }}>
                Gestione completa del sistema, utenti e configurazioni
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <Card
            sx={{
              height: '100%',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: 4
              },
              background: 'linear-gradient(135deg, #dc004e 0%, #c51162 100%)',
              color: 'white'
            }}
            onClick={() => setLoginType('user')}
          >
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <UserIcon sx={{ fontSize: 64, mb: 2, opacity: 0.9 }} />
              <Typography variant="h5" component="div" gutterBottom fontWeight="600">
                Utente Standard
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9, lineHeight: 1.6 }}>
                Gestione cantieri e operazioni quotidiane
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <Card
            sx={{
              height: '100%',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: 4
              },
              background: 'linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%)',
              color: 'white'
            }}
            onClick={() => setLoginType('cantiere')}
          >
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <ConstructionIcon sx={{ fontSize: 64, mb: 2, opacity: 0.9 }} />
              <Typography variant="h5" component="div" gutterBottom fontWeight="600">
                Utente Cantiere
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9, lineHeight: 1.6 }}>
                Accesso diretto alle operazioni di cantiere
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <Card
            sx={{
              height: '100%',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: 4
              },
              background: 'linear-gradient(135deg, #ed6c02 0%, #e65100 100%)',
              color: 'white'
            }}
            onClick={() => setShowPasswordRecoveryDialog(true)}
          >
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <PasswordIcon sx={{ fontSize: 64, mb: 2, opacity: 0.9 }} />
              <Typography variant="h5" component="div" gutterBottom fontWeight="600">
                Recupero Password
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9, lineHeight: 1.6 }}>
                Ripristina l'accesso amministratore
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  return (
    <Container component="main" maxWidth="md">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Typography component="h1" variant="h4" sx={{ mb: 4 }}>
          Sistema di Gestione Cantieri
        </Typography>

        <Paper elevation={3} sx={{ width: '100%', p: 3 }}>
          <Typography variant="h5" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
            {loginType ? 'Login' : 'Seleziona Tipo di Accesso'}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {loginType ? renderLoginForm() : renderMainMenu()}
        </Paper>
      </Box>

      {/* Dialog per il recupero password */}
      <Dialog open={showPasswordRecoveryDialog} onClose={() => setShowPasswordRecoveryDialog(false)}>
        <DialogTitle>Recupero Password Amministratore</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Inserisci l'indirizzo email associato all'account amministratore per ricevere le istruzioni per il recupero della password.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="email"
            label="Indirizzo Email"
            type="email"
            fullWidth
            variant="outlined"
            value={recoveryEmail}
            onChange={handleRecoveryEmailChange}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPasswordRecoveryDialog(false)}>Annulla</Button>
          <Button onClick={handlePasswordRecovery} variant="contained">Invia</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default LoginPageNew;
