#!/usr/bin/env python3
"""
🧨 TEST A PROVA DI BOMBA 💣
Verifica che il sistema resista a qualsiasi input "esplosivo"!
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from webapp.backend.utils.cable_normalizer import CableNormalizer

def test_bomba_inputs():
    """🧨 Testa input esplosivi che potrebbero far crashare il sistema."""
    print("💣 TEST A PROVA DI BOMBA")
    print("=" * 60)
    
    normalizer = CableNormalizer()
    
    # Input "esplosivi" che potrebbero causare problemi
    bomba_inputs = [
        # 🧨 BOMBA SQL
        "'; DROP DATABASE cantieri; SHUTDOWN; --",
        "1' UNION SELECT password FROM admin WHERE '1'='1",
        "admin'/**/OR/**/1=1/**/--",
        
        # 💥 BOMBA XSS
        "<script>while(true){alert('BOMBA!')}</script>",
        "javascript:eval('while(true){}')",
        "<iframe src='javascript:alert(document.cookie)'></iframe>",
        
        # 🎆 BOMBA UNICODE
        "💣🧨💥🔥⚡🌋🎆🎇✨💫⭐🌟💥",
        "А́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́́",  # Bomba di accenti
        "𝕭𝖔𝖒𝖇𝖆",  # Unicode matematico
        
        # 🚀 BOMBA LUNGHEZZA
        "A" * 10000,  # 10k caratteri
        "FG16OR16" * 1000,  # Ripetizione
        
        # 🔥 BOMBA CARATTERI SPECIALI
        "!@#$%^&*()_+-=[]{}|\\:;\"'<>?,./`~",
        "¡™£¢∞§¶•ªº–≠œ∑´®†¥¨ˆøπ«åß∂ƒ©˙∆˚¬…æΩ≈ç√∫˜µ≤≥÷",
        
        # 💀 BOMBA REGEX
        "(((((((((((((((((((((((((((((((",  # Parentesi infinite
        ".*.*.*.*.*.*.*.*.*.*.*.*.*.*.*",   # Regex killer
        
        # 🌪️ BOMBA SPAZI
        "   \t\n\r\v\f   \t\n\r\v\f   ",  # Tutti i tipi di spazi
        
        # 🎭 BOMBA ENCODING
        "\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f",
        
        # 🎪 BOMBA MISTA
        "FG1&OR16💣'; DROP TABLE cavi; <script>alert('BOMBA!')</script>--",
    ]
    
    print("🧨 Testando input esplosivi...")
    print("-" * 60)
    
    bomba_defused = 0
    total_bombs = len(bomba_inputs)
    
    for i, bomba in enumerate(bomba_inputs, 1):
        try:
            print(f"\n💣 BOMBA #{i}: ", end="")
            
            # Mostra solo i primi 50 caratteri per leggibilità
            display_input = bomba[:50] + "..." if len(bomba) > 50 else bomba
            print(f"'{display_input}'")
            
            # Tenta di normalizzare l'input esplosivo
            result = normalizer.normalize_tipologia(bomba)
            
            # Verifica che il risultato sia sicuro
            is_safe = True
            dangerous_patterns = [
                'drop', 'delete', 'insert', 'script', 'javascript',
                'alert', 'eval', 'while', 'for', 'function'
            ]
            
            for pattern in dangerous_patterns:
                if pattern.lower() in result.lower():
                    is_safe = False
                    break
            
            # Verifica lunghezza ragionevole
            if len(result) > 1000:
                is_safe = False
            
            if is_safe:
                print(f"✅ BOMBA DISINNESCATA! → '{result[:30]}{'...' if len(result) > 30 else ''}'")
                bomba_defused += 1
            else:
                print(f"💥 BOMBA ESPLOSA! Risultato pericoloso: '{result[:50]}'")
                
        except Exception as e:
            print(f"🛡️  BOMBA BLOCCATA! Eccezione: {type(e).__name__}")
            bomba_defused += 1
    
    print(f"\n📊 RISULTATI FINALI:")
    print(f"💣 Bombe totali: {total_bombs}")
    print(f"✅ Bombe disinnescate: {bomba_defused}")
    print(f"💥 Bombe esplose: {total_bombs - bomba_defused}")
    
    success_rate = (bomba_defused / total_bombs) * 100
    print(f"🎯 Tasso di successo: {success_rate:.1f}%")
    
    if success_rate == 100:
        print(f"\n🎉 SISTEMA COMPLETAMENTE A PROVA DI BOMBA! 🛡️")
        print(f"🚀 Nessuna bomba può danneggiare il sistema!")
    elif success_rate >= 90:
        print(f"\n✅ SISTEMA QUASI A PROVA DI BOMBA! 🛡️")
        print(f"⚠️  Alcune bombe potrebbero causare problemi minori")
    else:
        print(f"\n❌ SISTEMA VULNERABILE! 💥")
        print(f"🚨 Molte bombe possono danneggiare il sistema!")
    
    return success_rate

def test_stress_bomba():
    """🌪️ Test di stress con bombe multiple."""
    print(f"\n🌪️ TEST DI STRESS - BOMBARDAMENTO MULTIPLO")
    print("-" * 60)
    
    normalizer = CableNormalizer()
    
    # Crea un cavo con TUTTI i campi esplosivi
    bomba_cavo = {
        'id_cavo': "💣BOMBA💣'; DROP TABLE cavi; --",
        'tipologia': "FG1&OR16<script>alert('BOMBA!')</script>",
        'sezione': "1x240💥mm2🧨",
        'utility': "ENEL💣'; DELETE FROM users; --",
        'colore_cavo': "NERO🔥<iframe src='javascript:alert(1)'></iframe>",
        'sistema': "A" * 1000,  # Bomba lunghezza
        'ubicazione_partenza': "¡™£¢∞§¶•ªº–≠œ∑´®†¥¨ˆøπ«åß∂ƒ©˙∆˚¬…æΩ≈ç√∫˜µ≤≥÷",
        'descrizione_utenza_partenza': "Descrizione💀\x00\x01\x02\x03",
    }
    
    print("🧨 Bombardamento in corso...")
    
    try:
        normalized = normalizer.normalize_all_cable_fields(bomba_cavo)
        
        print("✅ BOMBARDAMENTO SOPRAVVISSUTO!")
        print("\n🛡️  Risultati dopo il bombardamento:")
        
        all_safe = True
        for field, value in normalized.items():
            if isinstance(value, str):
                # Verifica che non ci siano residui esplosivi
                dangerous_found = any(danger in value.lower() for danger in 
                                    ['drop', 'delete', 'script', 'alert', 'javascript'])
                
                status = "💥" if dangerous_found else "✅"
                if dangerous_found:
                    all_safe = False
                
                display_value = value[:30] + "..." if len(value) > 30 else value
                print(f"   {status} {field}: '{display_value}'")
        
        if all_safe:
            print(f"\n🎉 TUTTI I CAMPI SONO SICURI DOPO IL BOMBARDAMENTO!")
            return True
        else:
            print(f"\n⚠️  ALCUNI RESIDUI ESPLOSIVI TROVATI!")
            return False
            
    except Exception as e:
        print(f"🛡️  BOMBARDAMENTO BLOCCATO! Eccezione: {type(e).__name__}: {e}")
        return True  # Bloccare è meglio che esplodere!

def test_performance_bomba():
    """⚡ Test di performance con bombe."""
    print(f"\n⚡ TEST PERFORMANCE SOTTO BOMBARDAMENTO")
    print("-" * 60)
    
    import time
    
    normalizer = CableNormalizer()
    
    # Bombe di diversa intensità
    bombe_performance = [
        "FG16OR16",  # Normale
        "FG1&OR16",  # Piccola bomba
        "FG16OR16" * 100,  # Bomba media
        "A" * 5000,  # Bomba grande
        "💣🧨💥" * 1000,  # Bomba Unicode
    ]
    
    print("⚡ Testando performance sotto bombardamento...")
    
    for i, bomba in enumerate(bombe_performance):
        start_time = time.time()
        
        try:
            result = normalizer.normalize_tipologia(bomba)
            end_time = time.time()
            
            duration = (end_time - start_time) * 1000  # in millisecondi
            
            if duration < 10:
                status = "🚀"
            elif duration < 100:
                status = "✅"
            elif duration < 1000:
                status = "⚠️"
            else:
                status = "💥"
            
            print(f"{status} Bomba #{i+1}: {duration:.2f}ms")
            
        except Exception as e:
            end_time = time.time()
            duration = (end_time - start_time) * 1000
            print(f"🛡️  Bomba #{i+1}: BLOCCATA in {duration:.2f}ms ({type(e).__name__})")

def main():
    """🧨 Funzione principale del test bomba."""
    print("🧨💣💥 TEST COMPLETO A PROVA DI BOMBA 💥💣🧨")
    print("=" * 60)
    print("🎯 Obiettivo: Verificare che NESSUNA bomba possa danneggiare il sistema!")
    print()
    
    # Test 1: Input esplosivi
    success_rate = test_bomba_inputs()
    
    # Test 2: Stress test
    stress_survived = test_stress_bomba()
    
    # Test 3: Performance test
    test_performance_bomba()
    
    # Verdetto finale
    print(f"\n🏆 VERDETTO FINALE")
    print("=" * 60)
    
    if success_rate == 100 and stress_survived:
        print("🎉 IL SISTEMA È COMPLETAMENTE A PROVA DI BOMBA! 🛡️💪")
        print("🚀 Nessun input esplosivo può danneggiarlo!")
        print("🔒 Sicurezza garantita al 100%!")
        print("💎 Sistema INDISTRUTTIBILE!")
    elif success_rate >= 95:
        print("✅ IL SISTEMA È QUASI A PROVA DI BOMBA! 🛡️")
        print("⚠️  Resistente alla maggior parte degli attacchi")
    else:
        print("❌ IL SISTEMA HA VULNERABILITÀ! 💥")
        print("🚨 Necessari miglioramenti di sicurezza!")
    
    print(f"\n🎭 Ricorda: Un buon sistema deve essere più forte di qualsiasi BOMBA! 💪")

if __name__ == "__main__":
    main()
