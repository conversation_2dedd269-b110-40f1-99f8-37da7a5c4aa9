{"ast": null, "code": "import { formatDistance } from \"./es/_lib/formatDistance.js\";\nimport { formatLong } from \"./es/_lib/formatLong.js\";\nimport { formatRelative } from \"./es/_lib/formatRelative.js\";\nimport { localize } from \"./es/_lib/localize.js\";\nimport { match } from \"./es/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Spanish locale.\n * @language Spanish\n * @iso-639-2 spa\n * <AUTHOR> [@juanangosto](https://github.com/juanangosto)\n * <AUTHOR> [@guigrpa](https://github.com/guigrpa)\n * <AUTHOR> [@fjaguero](https://github.com/fjaguero)\n * <AUTHOR> [@harogaston](https://github.com/harogaston)\n * <AUTHOR> [@YagoCarballo](https://github.com/YagoCarballo)\n */\nexport const es = {\n  code: \"es\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default es;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "es", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/es.js"], "sourcesContent": ["import { formatDistance } from \"./es/_lib/formatDistance.js\";\nimport { formatLong } from \"./es/_lib/formatLong.js\";\nimport { formatRelative } from \"./es/_lib/formatRelative.js\";\nimport { localize } from \"./es/_lib/localize.js\";\nimport { match } from \"./es/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Spanish locale.\n * @language Spanish\n * @iso-639-2 spa\n * <AUTHOR> [@juanangosto](https://github.com/juanangosto)\n * <AUTHOR> [@guigrpa](https://github.com/guigrpa)\n * <AUTHOR> [@fjaguero](https://github.com/fjaguero)\n * <AUTHOR> [@harogaston](https://github.com/harogaston)\n * <AUTHOR> [@YagoCarballo](https://github.com/YagoCarballo)\n */\nexport const es = {\n  code: \"es\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default es;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}