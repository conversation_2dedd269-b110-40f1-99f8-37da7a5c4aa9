{"ast": null, "code": "import { formatDistance } from \"./mt/_lib/formatDistance.js\";\nimport { formatLong } from \"./mt/_lib/formatLong.js\";\nimport { formatRelative } from \"./mt/_lib/formatRelative.js\";\nimport { localize } from \"./mt/_lib/localize.js\";\nimport { match } from \"./mt/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Maltese locale.\n * @language Maltese\n * @iso-639-2 mlt\n * <AUTHOR> [@amatzon](@link https://github.com/amatzon)\n * <AUTHOR> [@bryanMt](@link https://github.com/bryanMt)\n */\nexport const mt = {\n  code: \"mt\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default mt;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "mt", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/mt.js"], "sourcesContent": ["import { formatDistance } from \"./mt/_lib/formatDistance.js\";\nimport { formatLong } from \"./mt/_lib/formatLong.js\";\nimport { formatRelative } from \"./mt/_lib/formatRelative.js\";\nimport { localize } from \"./mt/_lib/localize.js\";\nimport { match } from \"./mt/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Maltese locale.\n * @language Maltese\n * @iso-639-2 mlt\n * <AUTHOR> [@amatzon](@link https://github.com/amatzon)\n * <AUTHOR> [@bryanMt](@link https://github.com/bryanMt)\n */\nexport const mt = {\n  code: \"mt\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default mt;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}