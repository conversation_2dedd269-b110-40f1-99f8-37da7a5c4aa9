{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ampm\", \"ampmInClock\", \"autoFocus\", \"slots\", \"slotProps\", \"value\", \"defaultValue\", \"referenceDate\", \"disableIgnoringDatePartForTimeValidation\", \"maxTime\", \"minTime\", \"disableFuture\", \"disablePast\", \"minutesStep\", \"shouldDisableTime\", \"showViewSwitcher\", \"onChange\", \"view\", \"views\", \"openTo\", \"onViewChange\", \"focusedView\", \"onFocusedViewChange\", \"className\", \"classes\", \"disabled\", \"readOnly\", \"timezone\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses, unstable_useId as useId } from '@mui/utils';\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nimport { useUtils, useNow } from \"../internals/hooks/useUtils.js\";\nimport { PickersArrowSwitcher } from \"../internals/components/PickersArrowSwitcher/index.js\";\nimport { convertValueToMeridiem, createIsAfterIgnoreDatePart } from \"../internals/utils/time-utils.js\";\nimport { useViews } from \"../internals/hooks/useViews.js\";\nimport { useMeridiemMode } from \"../internals/hooks/date-helpers-hooks.js\";\nimport { PickerViewRoot } from \"../internals/components/PickerViewRoot/index.js\";\nimport { getTimeClockUtilityClass } from \"./timeClockClasses.js\";\nimport { Clock } from \"./Clock.js\";\nimport { getHourNumbers, getMinutesNumbers } from \"./ClockNumbers.js\";\nimport { useControlledValue } from \"../internals/hooks/useControlledValue.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { useClockReferenceDate } from \"../internals/hooks/useClockReferenceDate.js\";\nimport { usePickerPrivateContext } from \"../internals/hooks/usePickerPrivateContext.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root'],\n    arrowSwitcher: ['arrowSwitcher']\n  };\n  return composeClasses(slots, getTimeClockUtilityClass, classes);\n};\nconst TimeClockRoot = styled(PickerViewRoot, {\n  name: 'MuiTimeClock',\n  slot: 'Root'\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  position: 'relative'\n});\nconst TimeClockArrowSwitcher = styled(PickersArrowSwitcher, {\n  name: 'MuiTimeClock',\n  slot: 'ArrowSwitcher'\n})({\n  position: 'absolute',\n  right: 12,\n  top: 15\n});\nconst TIME_CLOCK_DEFAULT_VIEWS = ['hours', 'minutes'];\n\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [TimeClock](https://mui.com/x/react-date-pickers/time-clock/)\n *\n * API:\n *\n * - [TimeClock API](https://mui.com/x/api/date-pickers/time-clock/)\n */\nexport const TimeClock = /*#__PURE__*/React.forwardRef(function TimeClock(inProps, ref) {\n  const utils = useUtils();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimeClock'\n  });\n  const {\n      ampm = utils.is12HourCycleInCurrentLocale(),\n      ampmInClock = false,\n      autoFocus,\n      slots,\n      slotProps,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disableIgnoringDatePartForTimeValidation = false,\n      maxTime,\n      minTime,\n      disableFuture,\n      disablePast,\n      minutesStep = 1,\n      shouldDisableTime,\n      showViewSwitcher,\n      onChange,\n      view: inView,\n      views = TIME_CLOCK_DEFAULT_VIEWS,\n      openTo,\n      onViewChange,\n      focusedView,\n      onFocusedViewChange,\n      className,\n      classes: classesProp,\n      disabled,\n      readOnly,\n      timezone: timezoneProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'TimeClock',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const valueOrReferenceDate = useClockReferenceDate({\n    value,\n    referenceDate: referenceDateProp,\n    utils,\n    props,\n    timezone\n  });\n  const translations = usePickerTranslations();\n  const now = useNow(timezone);\n  const selectedId = useId();\n  const {\n    ownerState\n  } = usePickerPrivateContext();\n  const {\n    view,\n    setView,\n    previousView,\n    nextView,\n    setValueAndGoToNextView\n  } = useViews({\n    view: inView,\n    views,\n    openTo,\n    onViewChange,\n    onChange: handleValueChange,\n    focusedView,\n    onFocusedViewChange\n  });\n  const {\n    meridiemMode,\n    handleMeridiemChange\n  } = useMeridiemMode(valueOrReferenceDate, ampm, setValueAndGoToNextView);\n  const isTimeDisabled = React.useCallback((rawValue, viewType) => {\n    const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, utils);\n    const shouldCheckPastEnd = viewType === 'hours' || viewType === 'minutes' && views.includes('seconds');\n    const containsValidTime = ({\n      start,\n      end\n    }) => {\n      if (minTime && isAfter(minTime, end)) {\n        return false;\n      }\n      if (maxTime && isAfter(start, maxTime)) {\n        return false;\n      }\n      if (disableFuture && isAfter(start, now)) {\n        return false;\n      }\n      if (disablePast && isAfter(now, shouldCheckPastEnd ? end : start)) {\n        return false;\n      }\n      return true;\n    };\n    const isValidValue = (timeValue, step = 1) => {\n      if (timeValue % step !== 0) {\n        return false;\n      }\n      if (shouldDisableTime) {\n        switch (viewType) {\n          case 'hours':\n            return !shouldDisableTime(utils.setHours(valueOrReferenceDate, timeValue), 'hours');\n          case 'minutes':\n            return !shouldDisableTime(utils.setMinutes(valueOrReferenceDate, timeValue), 'minutes');\n          case 'seconds':\n            return !shouldDisableTime(utils.setSeconds(valueOrReferenceDate, timeValue), 'seconds');\n          default:\n            return false;\n        }\n      }\n      return true;\n    };\n    switch (viewType) {\n      case 'hours':\n        {\n          const valueWithMeridiem = convertValueToMeridiem(rawValue, meridiemMode, ampm);\n          const dateWithNewHours = utils.setHours(valueOrReferenceDate, valueWithMeridiem);\n          if (utils.getHours(dateWithNewHours) !== valueWithMeridiem) {\n            return true;\n          }\n          const start = utils.setSeconds(utils.setMinutes(dateWithNewHours, 0), 0);\n          const end = utils.setSeconds(utils.setMinutes(dateWithNewHours, 59), 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(valueWithMeridiem);\n        }\n      case 'minutes':\n        {\n          const dateWithNewMinutes = utils.setMinutes(valueOrReferenceDate, rawValue);\n          const start = utils.setSeconds(dateWithNewMinutes, 0);\n          const end = utils.setSeconds(dateWithNewMinutes, 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue, minutesStep);\n        }\n      case 'seconds':\n        {\n          const dateWithNewSeconds = utils.setSeconds(valueOrReferenceDate, rawValue);\n          const start = dateWithNewSeconds;\n          const end = dateWithNewSeconds;\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue);\n        }\n      default:\n        throw new Error('not supported');\n    }\n  }, [ampm, valueOrReferenceDate, disableIgnoringDatePartForTimeValidation, maxTime, meridiemMode, minTime, minutesStep, shouldDisableTime, utils, disableFuture, disablePast, now, views]);\n  const viewProps = React.useMemo(() => {\n    switch (view) {\n      case 'hours':\n        {\n          const handleHoursChange = (hourValue, isFinish) => {\n            const valueWithMeridiem = convertValueToMeridiem(hourValue, meridiemMode, ampm);\n            setValueAndGoToNextView(utils.setHours(valueOrReferenceDate, valueWithMeridiem), isFinish, 'hours');\n          };\n          const viewValue = utils.getHours(valueOrReferenceDate);\n          let viewRange;\n          if (ampm) {\n            if (viewValue > 12) {\n              viewRange = [12, 23];\n            } else {\n              viewRange = [0, 11];\n            }\n          } else {\n            viewRange = [0, 23];\n          }\n          return {\n            onChange: handleHoursChange,\n            viewValue,\n            children: getHourNumbers({\n              value,\n              utils,\n              ampm,\n              onChange: handleHoursChange,\n              getClockNumberText: translations.hoursClockNumberText,\n              isDisabled: hourValue => disabled || isTimeDisabled(hourValue, 'hours'),\n              selectedId\n            }),\n            viewRange\n          };\n        }\n      case 'minutes':\n        {\n          const minutesValue = utils.getMinutes(valueOrReferenceDate);\n          const handleMinutesChange = (minuteValue, isFinish) => {\n            setValueAndGoToNextView(utils.setMinutes(valueOrReferenceDate, minuteValue), isFinish, 'minutes');\n          };\n          return {\n            viewValue: minutesValue,\n            onChange: handleMinutesChange,\n            children: getMinutesNumbers({\n              utils,\n              value: minutesValue,\n              onChange: handleMinutesChange,\n              getClockNumberText: translations.minutesClockNumberText,\n              isDisabled: minuteValue => disabled || isTimeDisabled(minuteValue, 'minutes'),\n              selectedId\n            }),\n            viewRange: [0, 59]\n          };\n        }\n      case 'seconds':\n        {\n          const secondsValue = utils.getSeconds(valueOrReferenceDate);\n          const handleSecondsChange = (secondValue, isFinish) => {\n            setValueAndGoToNextView(utils.setSeconds(valueOrReferenceDate, secondValue), isFinish, 'seconds');\n          };\n          return {\n            viewValue: secondsValue,\n            onChange: handleSecondsChange,\n            children: getMinutesNumbers({\n              utils,\n              value: secondsValue,\n              onChange: handleSecondsChange,\n              getClockNumberText: translations.secondsClockNumberText,\n              isDisabled: secondValue => disabled || isTimeDisabled(secondValue, 'seconds'),\n              selectedId\n            }),\n            viewRange: [0, 59]\n          };\n        }\n      default:\n        throw new Error('You must provide the type for ClockView');\n    }\n  }, [view, utils, value, ampm, translations.hoursClockNumberText, translations.minutesClockNumberText, translations.secondsClockNumberText, meridiemMode, setValueAndGoToNextView, valueOrReferenceDate, isTimeDisabled, selectedId, disabled]);\n  const classes = useUtilityClasses(classesProp);\n  return /*#__PURE__*/_jsxs(TimeClockRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: ownerState\n  }, other, {\n    children: [/*#__PURE__*/_jsx(Clock, _extends({\n      autoFocus: autoFocus ?? !!focusedView,\n      ampmInClock: ampmInClock && views.includes('hours'),\n      value: value,\n      type: view,\n      ampm: ampm,\n      minutesStep: minutesStep,\n      isTimeDisabled: isTimeDisabled,\n      meridiemMode: meridiemMode,\n      handleMeridiemChange: handleMeridiemChange,\n      selectedId: selectedId,\n      disabled: disabled,\n      readOnly: readOnly\n    }, viewProps)), showViewSwitcher && /*#__PURE__*/_jsx(TimeClockArrowSwitcher, {\n      className: classes.arrowSwitcher,\n      slots: slots,\n      slotProps: slotProps,\n      onGoToPrevious: () => setView(previousView),\n      isPreviousDisabled: !previousView,\n      previousLabel: translations.openPreviousView,\n      onGoToNext: () => setView(nextView),\n      isNextDisabled: !nextView,\n      nextLabel: translations.openNextView,\n      ownerState: ownerState\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimeClock.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default false\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the component is disabled.\n   * When disabled, the value cannot be changed and no interaction is possible.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * Controlled focused view.\n   */\n  focusedView: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TView The view type. Will be one of date or time views.\n   * @param {TValue} value The new value.\n   * @param {PickerSelectionState | undefined} selectionState Indicates if the date selection is complete.\n   * @param {TView | undefined} selectedView Indicates the view in which the selection has been made.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired on focused view change.\n   * @template TView\n   * @param {TView} view The new view to focus or not.\n   * @param {boolean} hasFocus `true` if the view should be focused.\n   */\n  onFocusedViewChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * If `true`, the component is read-only.\n   * When read-only, the value cannot be changed but the user can interact with the interface.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid time using the validation props, except callbacks such as `shouldDisableTime`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific time.\n   * @param {PickerValidDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  showViewSwitcher: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Available views.\n   * @default ['hours', 'minutes']\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'minutes', 'seconds']).isRequired)\n} : void 0;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "PropTypes", "styled", "useThemeProps", "unstable_composeClasses", "composeClasses", "unstable_useId", "useId", "usePickerTranslations", "useUtils", "useNow", "PickersArrowSwitcher", "convertValueToMeridiem", "createIsAfterIgnoreDatePart", "useViews", "useMeridiemMode", "PickerViewRoot", "getTimeClockUtilityClass", "Clock", "getHourNumbers", "getMinutesNumbers", "useControlledValue", "singleItemValueManager", "useClockReferenceDate", "usePickerPrivateContext", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "classes", "slots", "root", "arrowSwitcher", "TimeClockRoot", "name", "slot", "display", "flexDirection", "position", "TimeClockArrowSwitcher", "right", "top", "TIME_CLOCK_DEFAULT_VIEWS", "TimeClock", "forwardRef", "inProps", "ref", "utils", "props", "ampm", "is12HourCycleInCurrentLocale", "ampmInClock", "autoFocus", "slotProps", "value", "valueProp", "defaultValue", "referenceDate", "referenceDateProp", "disableIgnoringDatePartForTimeValidation", "maxTime", "minTime", "disableFuture", "disablePast", "minutesStep", "shouldDisableTime", "showViewSwitcher", "onChange", "view", "inView", "views", "openTo", "onViewChange", "focused<PERSON>iew", "onFocusedViewChange", "className", "classesProp", "disabled", "readOnly", "timezone", "timezoneProp", "other", "handleValueChange", "valueManager", "valueOrReferenceDate", "translations", "now", "selectedId", "ownerState", "<PERSON><PERSON><PERSON><PERSON>", "previousView", "next<PERSON>iew", "setValueAndGoToNextView", "meridiemMode", "handleMeridiemChange", "isTimeDisabled", "useCallback", "rawValue", "viewType", "isAfter", "shouldCheckPastEnd", "includes", "containsValidTime", "start", "end", "isValidValue", "timeValue", "step", "setHours", "setMinutes", "setSeconds", "valueWithMeridiem", "dateWithNewHours", "getHours", "dateWithNewMinutes", "dateWithNewSeconds", "Error", "viewProps", "useMemo", "handleHoursChange", "hourValue", "is<PERSON><PERSON><PERSON>", "viewValue", "viewRange", "children", "getClockNumberText", "hoursClockNumberText", "isDisabled", "minutesValue", "getMinutes", "handleMinutesChange", "minuteValue", "minutesClockNumberText", "secondsValue", "getSeconds", "handleSecondsChange", "secondValue", "secondsClockNumberText", "type", "onGoToPrevious", "isPreviousDisabled", "previousLabel", "openPreviousView", "onGoToNext", "isNextDisabled", "next<PERSON><PERSON><PERSON>", "openNextView", "process", "env", "NODE_ENV", "propTypes", "bool", "object", "string", "oneOf", "number", "func", "sx", "oneOfType", "arrayOf", "isRequired"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/TimeClock/TimeClock.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ampm\", \"ampmInClock\", \"autoFocus\", \"slots\", \"slotProps\", \"value\", \"defaultValue\", \"referenceDate\", \"disableIgnoringDatePartForTimeValidation\", \"maxTime\", \"minTime\", \"disableFuture\", \"disablePast\", \"minutesStep\", \"shouldDisableTime\", \"showViewSwitcher\", \"onChange\", \"view\", \"views\", \"openTo\", \"onViewChange\", \"focusedView\", \"onFocusedViewChange\", \"className\", \"classes\", \"disabled\", \"readOnly\", \"timezone\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses, unstable_useId as useId } from '@mui/utils';\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nimport { useUtils, useNow } from \"../internals/hooks/useUtils.js\";\nimport { PickersArrowSwitcher } from \"../internals/components/PickersArrowSwitcher/index.js\";\nimport { convertValueToMeridiem, createIsAfterIgnoreDatePart } from \"../internals/utils/time-utils.js\";\nimport { useViews } from \"../internals/hooks/useViews.js\";\nimport { useMeridiemMode } from \"../internals/hooks/date-helpers-hooks.js\";\nimport { PickerViewRoot } from \"../internals/components/PickerViewRoot/index.js\";\nimport { getTimeClockUtilityClass } from \"./timeClockClasses.js\";\nimport { Clock } from \"./Clock.js\";\nimport { getHourNumbers, getMinutesNumbers } from \"./ClockNumbers.js\";\nimport { useControlledValue } from \"../internals/hooks/useControlledValue.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { useClockReferenceDate } from \"../internals/hooks/useClockReferenceDate.js\";\nimport { usePickerPrivateContext } from \"../internals/hooks/usePickerPrivateContext.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root'],\n    arrowSwitcher: ['arrowSwitcher']\n  };\n  return composeClasses(slots, getTimeClockUtilityClass, classes);\n};\nconst TimeClockRoot = styled(PickerViewRoot, {\n  name: 'MuiTimeClock',\n  slot: 'Root'\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  position: 'relative'\n});\nconst TimeClockArrowSwitcher = styled(PickersArrowSwitcher, {\n  name: 'MuiTimeClock',\n  slot: 'ArrowSwitcher'\n})({\n  position: 'absolute',\n  right: 12,\n  top: 15\n});\nconst TIME_CLOCK_DEFAULT_VIEWS = ['hours', 'minutes'];\n\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [TimeClock](https://mui.com/x/react-date-pickers/time-clock/)\n *\n * API:\n *\n * - [TimeClock API](https://mui.com/x/api/date-pickers/time-clock/)\n */\nexport const TimeClock = /*#__PURE__*/React.forwardRef(function TimeClock(inProps, ref) {\n  const utils = useUtils();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimeClock'\n  });\n  const {\n      ampm = utils.is12HourCycleInCurrentLocale(),\n      ampmInClock = false,\n      autoFocus,\n      slots,\n      slotProps,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disableIgnoringDatePartForTimeValidation = false,\n      maxTime,\n      minTime,\n      disableFuture,\n      disablePast,\n      minutesStep = 1,\n      shouldDisableTime,\n      showViewSwitcher,\n      onChange,\n      view: inView,\n      views = TIME_CLOCK_DEFAULT_VIEWS,\n      openTo,\n      onViewChange,\n      focusedView,\n      onFocusedViewChange,\n      className,\n      classes: classesProp,\n      disabled,\n      readOnly,\n      timezone: timezoneProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'TimeClock',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const valueOrReferenceDate = useClockReferenceDate({\n    value,\n    referenceDate: referenceDateProp,\n    utils,\n    props,\n    timezone\n  });\n  const translations = usePickerTranslations();\n  const now = useNow(timezone);\n  const selectedId = useId();\n  const {\n    ownerState\n  } = usePickerPrivateContext();\n  const {\n    view,\n    setView,\n    previousView,\n    nextView,\n    setValueAndGoToNextView\n  } = useViews({\n    view: inView,\n    views,\n    openTo,\n    onViewChange,\n    onChange: handleValueChange,\n    focusedView,\n    onFocusedViewChange\n  });\n  const {\n    meridiemMode,\n    handleMeridiemChange\n  } = useMeridiemMode(valueOrReferenceDate, ampm, setValueAndGoToNextView);\n  const isTimeDisabled = React.useCallback((rawValue, viewType) => {\n    const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, utils);\n    const shouldCheckPastEnd = viewType === 'hours' || viewType === 'minutes' && views.includes('seconds');\n    const containsValidTime = ({\n      start,\n      end\n    }) => {\n      if (minTime && isAfter(minTime, end)) {\n        return false;\n      }\n      if (maxTime && isAfter(start, maxTime)) {\n        return false;\n      }\n      if (disableFuture && isAfter(start, now)) {\n        return false;\n      }\n      if (disablePast && isAfter(now, shouldCheckPastEnd ? end : start)) {\n        return false;\n      }\n      return true;\n    };\n    const isValidValue = (timeValue, step = 1) => {\n      if (timeValue % step !== 0) {\n        return false;\n      }\n      if (shouldDisableTime) {\n        switch (viewType) {\n          case 'hours':\n            return !shouldDisableTime(utils.setHours(valueOrReferenceDate, timeValue), 'hours');\n          case 'minutes':\n            return !shouldDisableTime(utils.setMinutes(valueOrReferenceDate, timeValue), 'minutes');\n          case 'seconds':\n            return !shouldDisableTime(utils.setSeconds(valueOrReferenceDate, timeValue), 'seconds');\n          default:\n            return false;\n        }\n      }\n      return true;\n    };\n    switch (viewType) {\n      case 'hours':\n        {\n          const valueWithMeridiem = convertValueToMeridiem(rawValue, meridiemMode, ampm);\n          const dateWithNewHours = utils.setHours(valueOrReferenceDate, valueWithMeridiem);\n          if (utils.getHours(dateWithNewHours) !== valueWithMeridiem) {\n            return true;\n          }\n          const start = utils.setSeconds(utils.setMinutes(dateWithNewHours, 0), 0);\n          const end = utils.setSeconds(utils.setMinutes(dateWithNewHours, 59), 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(valueWithMeridiem);\n        }\n      case 'minutes':\n        {\n          const dateWithNewMinutes = utils.setMinutes(valueOrReferenceDate, rawValue);\n          const start = utils.setSeconds(dateWithNewMinutes, 0);\n          const end = utils.setSeconds(dateWithNewMinutes, 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue, minutesStep);\n        }\n      case 'seconds':\n        {\n          const dateWithNewSeconds = utils.setSeconds(valueOrReferenceDate, rawValue);\n          const start = dateWithNewSeconds;\n          const end = dateWithNewSeconds;\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue);\n        }\n      default:\n        throw new Error('not supported');\n    }\n  }, [ampm, valueOrReferenceDate, disableIgnoringDatePartForTimeValidation, maxTime, meridiemMode, minTime, minutesStep, shouldDisableTime, utils, disableFuture, disablePast, now, views]);\n  const viewProps = React.useMemo(() => {\n    switch (view) {\n      case 'hours':\n        {\n          const handleHoursChange = (hourValue, isFinish) => {\n            const valueWithMeridiem = convertValueToMeridiem(hourValue, meridiemMode, ampm);\n            setValueAndGoToNextView(utils.setHours(valueOrReferenceDate, valueWithMeridiem), isFinish, 'hours');\n          };\n          const viewValue = utils.getHours(valueOrReferenceDate);\n          let viewRange;\n          if (ampm) {\n            if (viewValue > 12) {\n              viewRange = [12, 23];\n            } else {\n              viewRange = [0, 11];\n            }\n          } else {\n            viewRange = [0, 23];\n          }\n          return {\n            onChange: handleHoursChange,\n            viewValue,\n            children: getHourNumbers({\n              value,\n              utils,\n              ampm,\n              onChange: handleHoursChange,\n              getClockNumberText: translations.hoursClockNumberText,\n              isDisabled: hourValue => disabled || isTimeDisabled(hourValue, 'hours'),\n              selectedId\n            }),\n            viewRange\n          };\n        }\n      case 'minutes':\n        {\n          const minutesValue = utils.getMinutes(valueOrReferenceDate);\n          const handleMinutesChange = (minuteValue, isFinish) => {\n            setValueAndGoToNextView(utils.setMinutes(valueOrReferenceDate, minuteValue), isFinish, 'minutes');\n          };\n          return {\n            viewValue: minutesValue,\n            onChange: handleMinutesChange,\n            children: getMinutesNumbers({\n              utils,\n              value: minutesValue,\n              onChange: handleMinutesChange,\n              getClockNumberText: translations.minutesClockNumberText,\n              isDisabled: minuteValue => disabled || isTimeDisabled(minuteValue, 'minutes'),\n              selectedId\n            }),\n            viewRange: [0, 59]\n          };\n        }\n      case 'seconds':\n        {\n          const secondsValue = utils.getSeconds(valueOrReferenceDate);\n          const handleSecondsChange = (secondValue, isFinish) => {\n            setValueAndGoToNextView(utils.setSeconds(valueOrReferenceDate, secondValue), isFinish, 'seconds');\n          };\n          return {\n            viewValue: secondsValue,\n            onChange: handleSecondsChange,\n            children: getMinutesNumbers({\n              utils,\n              value: secondsValue,\n              onChange: handleSecondsChange,\n              getClockNumberText: translations.secondsClockNumberText,\n              isDisabled: secondValue => disabled || isTimeDisabled(secondValue, 'seconds'),\n              selectedId\n            }),\n            viewRange: [0, 59]\n          };\n        }\n      default:\n        throw new Error('You must provide the type for ClockView');\n    }\n  }, [view, utils, value, ampm, translations.hoursClockNumberText, translations.minutesClockNumberText, translations.secondsClockNumberText, meridiemMode, setValueAndGoToNextView, valueOrReferenceDate, isTimeDisabled, selectedId, disabled]);\n  const classes = useUtilityClasses(classesProp);\n  return /*#__PURE__*/_jsxs(TimeClockRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: ownerState\n  }, other, {\n    children: [/*#__PURE__*/_jsx(Clock, _extends({\n      autoFocus: autoFocus ?? !!focusedView,\n      ampmInClock: ampmInClock && views.includes('hours'),\n      value: value,\n      type: view,\n      ampm: ampm,\n      minutesStep: minutesStep,\n      isTimeDisabled: isTimeDisabled,\n      meridiemMode: meridiemMode,\n      handleMeridiemChange: handleMeridiemChange,\n      selectedId: selectedId,\n      disabled: disabled,\n      readOnly: readOnly\n    }, viewProps)), showViewSwitcher && /*#__PURE__*/_jsx(TimeClockArrowSwitcher, {\n      className: classes.arrowSwitcher,\n      slots: slots,\n      slotProps: slotProps,\n      onGoToPrevious: () => setView(previousView),\n      isPreviousDisabled: !previousView,\n      previousLabel: translations.openPreviousView,\n      onGoToNext: () => setView(nextView),\n      isNextDisabled: !nextView,\n      nextLabel: translations.openNextView,\n      ownerState: ownerState\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TimeClock.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default false\n   */\n  ampmInClock: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the component is disabled.\n   * When disabled, the value cannot be changed and no interaction is possible.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * Controlled focused view.\n   */\n  focusedView: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TView The view type. Will be one of date or time views.\n   * @param {TValue} value The new value.\n   * @param {PickerSelectionState | undefined} selectionState Indicates if the date selection is complete.\n   * @param {TView | undefined} selectedView Indicates the view in which the selection has been made.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired on focused view change.\n   * @template TView\n   * @param {TView} view The new view to focus or not.\n   * @param {boolean} hasFocus `true` if the view should be focused.\n   */\n  onFocusedViewChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * If `true`, the component is read-only.\n   * When read-only, the value cannot be changed but the user can interact with the interface.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid time using the validation props, except callbacks such as `shouldDisableTime`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific time.\n   * @param {PickerValidDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  showViewSwitcher: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'minutes', 'seconds']),\n  /**\n   * Available views.\n   * @default ['hours', 'minutes']\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'minutes', 'seconds']).isRequired)\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,0CAA0C,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,qBAAqB,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;AACza,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,uBAAuB,IAAIC,cAAc,EAAEC,cAAc,IAAIC,KAAK,QAAQ,YAAY;AAC/F,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,QAAQ,EAAEC,MAAM,QAAQ,gCAAgC;AACjE,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,sBAAsB,EAAEC,2BAA2B,QAAQ,kCAAkC;AACtG,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,eAAe,QAAQ,0CAA0C;AAC1E,SAASC,cAAc,QAAQ,iDAAiD;AAChF,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,mBAAmB;AACrE,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,OAAO,IAAI;EACnC,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,aAAa,EAAE,CAAC,eAAe;EACjC,CAAC;EACD,OAAO5B,cAAc,CAAC0B,KAAK,EAAEd,wBAAwB,EAAEa,OAAO,CAAC;AACjE,CAAC;AACD,MAAMI,aAAa,GAAGhC,MAAM,CAACc,cAAc,EAAE;EAC3CmB,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAMC,sBAAsB,GAAGtC,MAAM,CAACS,oBAAoB,EAAE;EAC1DwB,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDG,QAAQ,EAAE,UAAU;EACpBE,KAAK,EAAE,EAAE;EACTC,GAAG,EAAE;AACP,CAAC,CAAC;AACF,MAAMC,wBAAwB,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,GAAG,aAAa7C,KAAK,CAAC8C,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACtF,MAAMC,KAAK,GAAGvC,QAAQ,CAAC,CAAC;EACxB,MAAMwC,KAAK,GAAG9C,aAAa,CAAC;IAC1B8C,KAAK,EAAEH,OAAO;IACdX,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFe,IAAI,GAAGF,KAAK,CAACG,4BAA4B,CAAC,CAAC;MAC3CC,WAAW,GAAG,KAAK;MACnBC,SAAS;MACTtB,KAAK;MACLuB,SAAS;MACTC,KAAK,EAAEC,SAAS;MAChBC,YAAY;MACZC,aAAa,EAAEC,iBAAiB;MAChCC,wCAAwC,GAAG,KAAK;MAChDC,OAAO;MACPC,OAAO;MACPC,aAAa;MACbC,WAAW;MACXC,WAAW,GAAG,CAAC;MACfC,iBAAiB;MACjBC,gBAAgB;MAChBC,QAAQ;MACRC,IAAI,EAAEC,MAAM;MACZC,KAAK,GAAG5B,wBAAwB;MAChC6B,MAAM;MACNC,YAAY;MACZC,WAAW;MACXC,mBAAmB;MACnBC,SAAS;MACT9C,OAAO,EAAE+C,WAAW;MACpBC,QAAQ;MACRC,QAAQ;MACRC,QAAQ,EAAEC;IACZ,CAAC,GAAGhC,KAAK;IACTiC,KAAK,GAAGrF,6BAA6B,CAACoD,KAAK,EAAEnD,SAAS,CAAC;EACzD,MAAM;IACJyD,KAAK;IACL4B,iBAAiB;IACjBH;EACF,CAAC,GAAG3D,kBAAkB,CAAC;IACrBc,IAAI,EAAE,WAAW;IACjB6C,QAAQ,EAAEC,YAAY;IACtB1B,KAAK,EAAEC,SAAS;IAChBC,YAAY;IACZC,aAAa,EAAEC,iBAAiB;IAChCS,QAAQ;IACRgB,YAAY,EAAE9D;EAChB,CAAC,CAAC;EACF,MAAM+D,oBAAoB,GAAG9D,qBAAqB,CAAC;IACjDgC,KAAK;IACLG,aAAa,EAAEC,iBAAiB;IAChCX,KAAK;IACLC,KAAK;IACL+B;EACF,CAAC,CAAC;EACF,MAAMM,YAAY,GAAG9E,qBAAqB,CAAC,CAAC;EAC5C,MAAM+E,GAAG,GAAG7E,MAAM,CAACsE,QAAQ,CAAC;EAC5B,MAAMQ,UAAU,GAAGjF,KAAK,CAAC,CAAC;EAC1B,MAAM;IACJkF;EACF,CAAC,GAAGjE,uBAAuB,CAAC,CAAC;EAC7B,MAAM;IACJ6C,IAAI;IACJqB,OAAO;IACPC,YAAY;IACZC,QAAQ;IACRC;EACF,CAAC,GAAG/E,QAAQ,CAAC;IACXuD,IAAI,EAAEC,MAAM;IACZC,KAAK;IACLC,MAAM;IACNC,YAAY;IACZL,QAAQ,EAAEe,iBAAiB;IAC3BT,WAAW;IACXC;EACF,CAAC,CAAC;EACF,MAAM;IACJmB,YAAY;IACZC;EACF,CAAC,GAAGhF,eAAe,CAACsE,oBAAoB,EAAEnC,IAAI,EAAE2C,uBAAuB,CAAC;EACxE,MAAMG,cAAc,GAAGjG,KAAK,CAACkG,WAAW,CAAC,CAACC,QAAQ,EAAEC,QAAQ,KAAK;IAC/D,MAAMC,OAAO,GAAGvF,2BAA2B,CAAC+C,wCAAwC,EAAEZ,KAAK,CAAC;IAC5F,MAAMqD,kBAAkB,GAAGF,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,SAAS,IAAI5B,KAAK,CAAC+B,QAAQ,CAAC,SAAS,CAAC;IACtG,MAAMC,iBAAiB,GAAGA,CAAC;MACzBC,KAAK;MACLC;IACF,CAAC,KAAK;MACJ,IAAI3C,OAAO,IAAIsC,OAAO,CAACtC,OAAO,EAAE2C,GAAG,CAAC,EAAE;QACpC,OAAO,KAAK;MACd;MACA,IAAI5C,OAAO,IAAIuC,OAAO,CAACI,KAAK,EAAE3C,OAAO,CAAC,EAAE;QACtC,OAAO,KAAK;MACd;MACA,IAAIE,aAAa,IAAIqC,OAAO,CAACI,KAAK,EAAEjB,GAAG,CAAC,EAAE;QACxC,OAAO,KAAK;MACd;MACA,IAAIvB,WAAW,IAAIoC,OAAO,CAACb,GAAG,EAAEc,kBAAkB,GAAGI,GAAG,GAAGD,KAAK,CAAC,EAAE;QACjE,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;IACD,MAAME,YAAY,GAAGA,CAACC,SAAS,EAAEC,IAAI,GAAG,CAAC,KAAK;MAC5C,IAAID,SAAS,GAAGC,IAAI,KAAK,CAAC,EAAE;QAC1B,OAAO,KAAK;MACd;MACA,IAAI1C,iBAAiB,EAAE;QACrB,QAAQiC,QAAQ;UACd,KAAK,OAAO;YACV,OAAO,CAACjC,iBAAiB,CAAClB,KAAK,CAAC6D,QAAQ,CAACxB,oBAAoB,EAAEsB,SAAS,CAAC,EAAE,OAAO,CAAC;UACrF,KAAK,SAAS;YACZ,OAAO,CAACzC,iBAAiB,CAAClB,KAAK,CAAC8D,UAAU,CAACzB,oBAAoB,EAAEsB,SAAS,CAAC,EAAE,SAAS,CAAC;UACzF,KAAK,SAAS;YACZ,OAAO,CAACzC,iBAAiB,CAAClB,KAAK,CAAC+D,UAAU,CAAC1B,oBAAoB,EAAEsB,SAAS,CAAC,EAAE,SAAS,CAAC;UACzF;YACE,OAAO,KAAK;QAChB;MACF;MACA,OAAO,IAAI;IACb,CAAC;IACD,QAAQR,QAAQ;MACd,KAAK,OAAO;QACV;UACE,MAAMa,iBAAiB,GAAGpG,sBAAsB,CAACsF,QAAQ,EAAEJ,YAAY,EAAE5C,IAAI,CAAC;UAC9E,MAAM+D,gBAAgB,GAAGjE,KAAK,CAAC6D,QAAQ,CAACxB,oBAAoB,EAAE2B,iBAAiB,CAAC;UAChF,IAAIhE,KAAK,CAACkE,QAAQ,CAACD,gBAAgB,CAAC,KAAKD,iBAAiB,EAAE;YAC1D,OAAO,IAAI;UACb;UACA,MAAMR,KAAK,GAAGxD,KAAK,CAAC+D,UAAU,CAAC/D,KAAK,CAAC8D,UAAU,CAACG,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UACxE,MAAMR,GAAG,GAAGzD,KAAK,CAAC+D,UAAU,CAAC/D,KAAK,CAAC8D,UAAU,CAACG,gBAAgB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;UACxE,OAAO,CAACV,iBAAiB,CAAC;YACxBC,KAAK;YACLC;UACF,CAAC,CAAC,IAAI,CAACC,YAAY,CAACM,iBAAiB,CAAC;QACxC;MACF,KAAK,SAAS;QACZ;UACE,MAAMG,kBAAkB,GAAGnE,KAAK,CAAC8D,UAAU,CAACzB,oBAAoB,EAAEa,QAAQ,CAAC;UAC3E,MAAMM,KAAK,GAAGxD,KAAK,CAAC+D,UAAU,CAACI,kBAAkB,EAAE,CAAC,CAAC;UACrD,MAAMV,GAAG,GAAGzD,KAAK,CAAC+D,UAAU,CAACI,kBAAkB,EAAE,EAAE,CAAC;UACpD,OAAO,CAACZ,iBAAiB,CAAC;YACxBC,KAAK;YACLC;UACF,CAAC,CAAC,IAAI,CAACC,YAAY,CAACR,QAAQ,EAAEjC,WAAW,CAAC;QAC5C;MACF,KAAK,SAAS;QACZ;UACE,MAAMmD,kBAAkB,GAAGpE,KAAK,CAAC+D,UAAU,CAAC1B,oBAAoB,EAAEa,QAAQ,CAAC;UAC3E,MAAMM,KAAK,GAAGY,kBAAkB;UAChC,MAAMX,GAAG,GAAGW,kBAAkB;UAC9B,OAAO,CAACb,iBAAiB,CAAC;YACxBC,KAAK;YACLC;UACF,CAAC,CAAC,IAAI,CAACC,YAAY,CAACR,QAAQ,CAAC;QAC/B;MACF;QACE,MAAM,IAAImB,KAAK,CAAC,eAAe,CAAC;IACpC;EACF,CAAC,EAAE,CAACnE,IAAI,EAAEmC,oBAAoB,EAAEzB,wCAAwC,EAAEC,OAAO,EAAEiC,YAAY,EAAEhC,OAAO,EAAEG,WAAW,EAAEC,iBAAiB,EAAElB,KAAK,EAAEe,aAAa,EAAEC,WAAW,EAAEuB,GAAG,EAAEhB,KAAK,CAAC,CAAC;EACzL,MAAM+C,SAAS,GAAGvH,KAAK,CAACwH,OAAO,CAAC,MAAM;IACpC,QAAQlD,IAAI;MACV,KAAK,OAAO;QACV;UACE,MAAMmD,iBAAiB,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;YACjD,MAAMV,iBAAiB,GAAGpG,sBAAsB,CAAC6G,SAAS,EAAE3B,YAAY,EAAE5C,IAAI,CAAC;YAC/E2C,uBAAuB,CAAC7C,KAAK,CAAC6D,QAAQ,CAACxB,oBAAoB,EAAE2B,iBAAiB,CAAC,EAAEU,QAAQ,EAAE,OAAO,CAAC;UACrG,CAAC;UACD,MAAMC,SAAS,GAAG3E,KAAK,CAACkE,QAAQ,CAAC7B,oBAAoB,CAAC;UACtD,IAAIuC,SAAS;UACb,IAAI1E,IAAI,EAAE;YACR,IAAIyE,SAAS,GAAG,EAAE,EAAE;cAClBC,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;YACtB,CAAC,MAAM;cACLA,SAAS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;YACrB;UACF,CAAC,MAAM;YACLA,SAAS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;UACrB;UACA,OAAO;YACLxD,QAAQ,EAAEoD,iBAAiB;YAC3BG,SAAS;YACTE,QAAQ,EAAE1G,cAAc,CAAC;cACvBoC,KAAK;cACLP,KAAK;cACLE,IAAI;cACJkB,QAAQ,EAAEoD,iBAAiB;cAC3BM,kBAAkB,EAAExC,YAAY,CAACyC,oBAAoB;cACrDC,UAAU,EAAEP,SAAS,IAAI3C,QAAQ,IAAIkB,cAAc,CAACyB,SAAS,EAAE,OAAO,CAAC;cACvEjC;YACF,CAAC,CAAC;YACFoC;UACF,CAAC;QACH;MACF,KAAK,SAAS;QACZ;UACE,MAAMK,YAAY,GAAGjF,KAAK,CAACkF,UAAU,CAAC7C,oBAAoB,CAAC;UAC3D,MAAM8C,mBAAmB,GAAGA,CAACC,WAAW,EAAEV,QAAQ,KAAK;YACrD7B,uBAAuB,CAAC7C,KAAK,CAAC8D,UAAU,CAACzB,oBAAoB,EAAE+C,WAAW,CAAC,EAAEV,QAAQ,EAAE,SAAS,CAAC;UACnG,CAAC;UACD,OAAO;YACLC,SAAS,EAAEM,YAAY;YACvB7D,QAAQ,EAAE+D,mBAAmB;YAC7BN,QAAQ,EAAEzG,iBAAiB,CAAC;cAC1B4B,KAAK;cACLO,KAAK,EAAE0E,YAAY;cACnB7D,QAAQ,EAAE+D,mBAAmB;cAC7BL,kBAAkB,EAAExC,YAAY,CAAC+C,sBAAsB;cACvDL,UAAU,EAAEI,WAAW,IAAItD,QAAQ,IAAIkB,cAAc,CAACoC,WAAW,EAAE,SAAS,CAAC;cAC7E5C;YACF,CAAC,CAAC;YACFoC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE;UACnB,CAAC;QACH;MACF,KAAK,SAAS;QACZ;UACE,MAAMU,YAAY,GAAGtF,KAAK,CAACuF,UAAU,CAAClD,oBAAoB,CAAC;UAC3D,MAAMmD,mBAAmB,GAAGA,CAACC,WAAW,EAAEf,QAAQ,KAAK;YACrD7B,uBAAuB,CAAC7C,KAAK,CAAC+D,UAAU,CAAC1B,oBAAoB,EAAEoD,WAAW,CAAC,EAAEf,QAAQ,EAAE,SAAS,CAAC;UACnG,CAAC;UACD,OAAO;YACLC,SAAS,EAAEW,YAAY;YACvBlE,QAAQ,EAAEoE,mBAAmB;YAC7BX,QAAQ,EAAEzG,iBAAiB,CAAC;cAC1B4B,KAAK;cACLO,KAAK,EAAE+E,YAAY;cACnBlE,QAAQ,EAAEoE,mBAAmB;cAC7BV,kBAAkB,EAAExC,YAAY,CAACoD,sBAAsB;cACvDV,UAAU,EAAES,WAAW,IAAI3D,QAAQ,IAAIkB,cAAc,CAACyC,WAAW,EAAE,SAAS,CAAC;cAC7EjD;YACF,CAAC,CAAC;YACFoC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE;UACnB,CAAC;QACH;MACF;QACE,MAAM,IAAIP,KAAK,CAAC,yCAAyC,CAAC;IAC9D;EACF,CAAC,EAAE,CAAChD,IAAI,EAAErB,KAAK,EAAEO,KAAK,EAAEL,IAAI,EAAEoC,YAAY,CAACyC,oBAAoB,EAAEzC,YAAY,CAAC+C,sBAAsB,EAAE/C,YAAY,CAACoD,sBAAsB,EAAE5C,YAAY,EAAED,uBAAuB,EAAER,oBAAoB,EAAEW,cAAc,EAAER,UAAU,EAAEV,QAAQ,CAAC,CAAC;EAC9O,MAAMhD,OAAO,GAAGD,iBAAiB,CAACgD,WAAW,CAAC;EAC9C,OAAO,aAAajD,KAAK,CAACM,aAAa,EAAEtC,QAAQ,CAAC;IAChDmD,GAAG,EAAEA,GAAG;IACR6B,SAAS,EAAE5E,IAAI,CAAC8B,OAAO,CAACE,IAAI,EAAE4C,SAAS,CAAC;IACxCa,UAAU,EAAEA;EACd,CAAC,EAAEP,KAAK,EAAE;IACR2C,QAAQ,EAAE,CAAC,aAAanG,IAAI,CAACR,KAAK,EAAEtB,QAAQ,CAAC;MAC3CyD,SAAS,EAAEA,SAAS,IAAI,CAAC,CAACqB,WAAW;MACrCtB,WAAW,EAAEA,WAAW,IAAImB,KAAK,CAAC+B,QAAQ,CAAC,OAAO,CAAC;MACnD/C,KAAK,EAAEA,KAAK;MACZoF,IAAI,EAAEtE,IAAI;MACVnB,IAAI,EAAEA,IAAI;MACVe,WAAW,EAAEA,WAAW;MACxB+B,cAAc,EAAEA,cAAc;MAC9BF,YAAY,EAAEA,YAAY;MAC1BC,oBAAoB,EAAEA,oBAAoB;MAC1CP,UAAU,EAAEA,UAAU;MACtBV,QAAQ,EAAEA,QAAQ;MAClBC,QAAQ,EAAEA;IACZ,CAAC,EAAEuC,SAAS,CAAC,CAAC,EAAEnD,gBAAgB,IAAI,aAAazC,IAAI,CAACc,sBAAsB,EAAE;MAC5EoC,SAAS,EAAE9C,OAAO,CAACG,aAAa;MAChCF,KAAK,EAAEA,KAAK;MACZuB,SAAS,EAAEA,SAAS;MACpBsF,cAAc,EAAEA,CAAA,KAAMlD,OAAO,CAACC,YAAY,CAAC;MAC3CkD,kBAAkB,EAAE,CAAClD,YAAY;MACjCmD,aAAa,EAAExD,YAAY,CAACyD,gBAAgB;MAC5CC,UAAU,EAAEA,CAAA,KAAMtD,OAAO,CAACE,QAAQ,CAAC;MACnCqD,cAAc,EAAE,CAACrD,QAAQ;MACzBsD,SAAS,EAAE5D,YAAY,CAAC6D,YAAY;MACpC1D,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF2D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1G,SAAS,CAAC2G,SAAS,GAAG;EAC5D;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACErG,IAAI,EAAEjD,SAAS,CAACuJ,IAAI;EACpB;AACF;AACA;AACA;EACEpG,WAAW,EAAEnD,SAAS,CAACuJ,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEnG,SAAS,EAAEpD,SAAS,CAACuJ,IAAI;EACzB;AACF;AACA;EACE1H,OAAO,EAAE7B,SAAS,CAACwJ,MAAM;EACzB7E,SAAS,EAAE3E,SAAS,CAACyJ,MAAM;EAC3B;AACF;AACA;AACA;EACEjG,YAAY,EAAExD,SAAS,CAACwJ,MAAM;EAC9B;AACF;AACA;AACA;AACA;EACE3E,QAAQ,EAAE7E,SAAS,CAACuJ,IAAI;EACxB;AACF;AACA;AACA;EACEzF,aAAa,EAAE9D,SAAS,CAACuJ,IAAI;EAC7B;AACF;AACA;AACA;EACE5F,wCAAwC,EAAE3D,SAAS,CAACuJ,IAAI;EACxD;AACF;AACA;AACA;EACExF,WAAW,EAAE/D,SAAS,CAACuJ,IAAI;EAC3B;AACF;AACA;EACE9E,WAAW,EAAEzE,SAAS,CAAC0J,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EAC7D;AACF;AACA;AACA;EACE9F,OAAO,EAAE5D,SAAS,CAACwJ,MAAM;EACzB;AACF;AACA;AACA;EACE3F,OAAO,EAAE7D,SAAS,CAACwJ,MAAM;EACzB;AACF;AACA;AACA;EACExF,WAAW,EAAEhE,SAAS,CAAC2J,MAAM;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACExF,QAAQ,EAAEnE,SAAS,CAAC4J,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACElF,mBAAmB,EAAE1E,SAAS,CAAC4J,IAAI;EACnC;AACF;AACA;AACA;AACA;EACEpF,YAAY,EAAExE,SAAS,CAAC4J,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACErF,MAAM,EAAEvE,SAAS,CAAC0J,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACxD;AACF;AACA;AACA;AACA;EACE5E,QAAQ,EAAE9E,SAAS,CAACuJ,IAAI;EACxB;AACF;AACA;AACA;EACE9F,aAAa,EAAEzD,SAAS,CAACwJ,MAAM;EAC/B;AACF;AACA;AACA;AACA;AACA;EACEvF,iBAAiB,EAAEjE,SAAS,CAAC4J,IAAI;EACjC1F,gBAAgB,EAAElE,SAAS,CAACuJ,IAAI;EAChC;AACF;AACA;AACA;EACElG,SAAS,EAAErD,SAAS,CAACwJ,MAAM;EAC3B;AACF;AACA;AACA;EACE1H,KAAK,EAAE9B,SAAS,CAACwJ,MAAM;EACvB;AACF;AACA;EACEK,EAAE,EAAE7J,SAAS,CAAC8J,SAAS,CAAC,CAAC9J,SAAS,CAAC+J,OAAO,CAAC/J,SAAS,CAAC8J,SAAS,CAAC,CAAC9J,SAAS,CAAC4J,IAAI,EAAE5J,SAAS,CAACwJ,MAAM,EAAExJ,SAAS,CAACuJ,IAAI,CAAC,CAAC,CAAC,EAAEvJ,SAAS,CAAC4J,IAAI,EAAE5J,SAAS,CAACwJ,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;EACEzE,QAAQ,EAAE/E,SAAS,CAACyJ,MAAM;EAC1B;AACF;AACA;AACA;EACEnG,KAAK,EAAEtD,SAAS,CAACwJ,MAAM;EACvB;AACF;AACA;AACA;AACA;EACEpF,IAAI,EAAEpE,SAAS,CAAC0J,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACtD;AACF;AACA;AACA;EACEpF,KAAK,EAAEtE,SAAS,CAAC+J,OAAO,CAAC/J,SAAS,CAAC0J,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAACM,UAAU;AACtF,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}