#!/usr/bin/env python3
"""
Script per applicare la normalizzazione intelligente a tutti i dati esistenti.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cable_normalizer import CableNormalizer
from modules.database_pg import Database
import psycopg2.extras

def analyze_current_data():
    """Analizza i dati attuali per identificare le normalizzazioni necessarie."""
    print("🔍 ANALISI DATI ATTUALI")
    print("=" * 60)
    
    db = Database()
    conn = db.get_dict_cursor_connection()
    normalizer = CableNormalizer()
    
    try:
        with conn.cursor() as cur:
            # Analizza cavi
            print("📋 ANALISI CAVI")
            print("-" * 40)
            
            cur.execute("""
                SELECT DISTINCT tipologia, sezione, COUNT(*) as count
                FROM cavi
                WHERE id_cantiere = 1
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """)
            
            cavi_data = cur.fetchall()
            
            changes_needed = []
            
            for row in cavi_data:
                tipologia = row['tipologia']
                sezione = row['sezione']
                count = row['count']
                
                norm_tipologia, norm_sezione = normalizer.normalize_cable_data(tipologia, sezione)
                
                if tipologia != norm_tipologia or sezione != norm_sezione:
                    changes_needed.append({
                        'original_tipologia': tipologia,
                        'original_sezione': sezione,
                        'normalized_tipologia': norm_tipologia,
                        'normalized_sezione': norm_sezione,
                        'count': count,
                        'table': 'cavi'
                    })
                    print(f"❌ {tipologia} | {sezione} ({count} cavi)")
                    print(f"   → {norm_tipologia} | {norm_sezione}")
                else:
                    print(f"✅ {tipologia} | {sezione} ({count} cavi)")
            
            # Analizza bobine
            print(f"\n📦 ANALISI BOBINE")
            print("-" * 40)
            
            cur.execute("""
                SELECT DISTINCT tipologia, sezione, COUNT(*) as count
                FROM parco_cavi
                WHERE id_cantiere = 1
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """)
            
            bobine_data = cur.fetchall()
            
            for row in bobine_data:
                tipologia = row['tipologia']
                sezione = row['sezione']
                count = row['count']
                
                norm_tipologia, norm_sezione = normalizer.normalize_cable_data(tipologia, sezione)
                
                if tipologia != norm_tipologia or sezione != norm_sezione:
                    changes_needed.append({
                        'original_tipologia': tipologia,
                        'original_sezione': sezione,
                        'normalized_tipologia': norm_tipologia,
                        'normalized_sezione': norm_sezione,
                        'count': count,
                        'table': 'parco_cavi'
                    })
                    print(f"❌ {tipologia} | {sezione} ({count} bobine)")
                    print(f"   → {norm_tipologia} | {norm_sezione}")
                else:
                    print(f"✅ {tipologia} | {sezione} ({count} bobine)")
            
            print(f"\n📊 RIEPILOGO")
            print("-" * 40)
            print(f"Modifiche necessarie: {len(changes_needed)}")
            
            return changes_needed
            
    except Exception as e:
        print(f"❌ Errore durante l'analisi: {e}")
        import traceback
        traceback.print_exc()
        return []
    finally:
        conn.close()

def apply_normalization(changes_needed, dry_run=True):
    """Applica le normalizzazioni necessarie."""
    if not changes_needed:
        print("✅ Nessuna normalizzazione necessaria!")
        return
    
    print(f"\n🔧 {'SIMULAZIONE' if dry_run else 'APPLICAZIONE'} NORMALIZZAZIONE")
    print("=" * 60)
    
    db = Database()
    conn = db.get_dict_cursor_connection()
    
    try:
        with conn.cursor() as cur:
            total_updates = 0
            
            for change in changes_needed:
                table = change['table']
                orig_tip = change['original_tipologia']
                orig_sez = change['original_sezione']
                norm_tip = change['normalized_tipologia']
                norm_sez = change['normalized_sezione']
                count = change['count']
                
                print(f"\n📝 {table.upper()}: {orig_tip} | {orig_sez} → {norm_tip} | {norm_sez}")
                print(f"   Righe interessate: {count}")
                
                if not dry_run:
                    if table == 'cavi':
                        cur.execute("""
                            UPDATE cavi 
                            SET tipologia = %s, 
                                sezione = %s,
                                modificato_manualmente = COALESCE(modificato_manualmente, 0) + 100
                            WHERE id_cantiere = 1 
                              AND tipologia = %s 
                              AND sezione = %s
                        """, (norm_tip, norm_sez, orig_tip, orig_sez))
                    
                    elif table == 'parco_cavi':
                        cur.execute("""
                            UPDATE parco_cavi 
                            SET tipologia = %s, 
                                sezione = %s
                            WHERE id_cantiere = 1 
                              AND tipologia = %s 
                              AND sezione = %s
                        """, (norm_tip, norm_sez, orig_tip, orig_sez))
                    
                    rows_updated = cur.rowcount
                    total_updates += rows_updated
                    print(f"   ✅ {rows_updated} righe aggiornate")
                else:
                    print(f"   🔍 Simulazione: {count} righe sarebbero aggiornate")
            
            if not dry_run:
                conn.commit()
                print(f"\n✅ Normalizzazione completata: {total_updates} righe aggiornate totali")
            else:
                print(f"\n🔍 Simulazione completata: {sum(c['count'] for c in changes_needed)} righe sarebbero aggiornate")
                
    except Exception as e:
        print(f"❌ Errore durante la normalizzazione: {e}")
        import traceback
        traceback.print_exc()
        if not dry_run:
            conn.rollback()
    finally:
        conn.close()

def test_normalization_examples():
    """Testa il normalizzatore con esempi reali."""
    print("🧪 TEST NORMALIZZATORE CON ESEMPI REALI")
    print("=" * 60)
    
    normalizer = CableNormalizer()
    
    # Esempi reali dal database
    real_examples = [
        "1x240MM2",
        "1X240MM2", 
        "3x2.5",
        "3x2.5+2.5YG",
        "4x1.5+SH",
        "1x2.5",
        "4x1.5",
        # Esempi problematici che l'utente potrebbe inserire
        "1x240 mm2",
        "1 X 240 MM2",
        "4x1,5+sh",
        "4 x 1,5 + SH",
        "(3x2,5+2,5yg)",
        "240MM2",
        "240 mm²",
        "1.5",
        "1,5"
    ]
    
    for example in real_examples:
        normalized = normalizer.normalize_section(example)
        status = "✅" if example == normalized else "🔄"
        print(f"{status} '{example}' → '{normalized}'")

def main():
    """Funzione principale."""
    print("🚀 SISTEMA DI NORMALIZZAZIONE INTELLIGENTE")
    print("=" * 60)
    
    # 1. Test del normalizzatore
    test_normalization_examples()
    
    # 2. Analisi dati attuali
    changes_needed = analyze_current_data()
    
    if not changes_needed:
        print("\n🎉 Tutti i dati sono già normalizzati!")
        return
    
    # 3. Chiedi conferma per applicare le modifiche
    print(f"\n❓ Vuoi applicare le normalizzazioni? (y/N): ", end="")
    response = input().strip().lower()
    
    if response in ['y', 'yes', 'si', 's']:
        # Prima simulazione
        print("\n🔍 Esecuzione simulazione...")
        apply_normalization(changes_needed, dry_run=True)
        
        print(f"\n❓ Confermi l'applicazione delle modifiche? (y/N): ", end="")
        confirm = input().strip().lower()
        
        if confirm in ['y', 'yes', 'si', 's']:
            apply_normalization(changes_needed, dry_run=False)
        else:
            print("❌ Operazione annullata")
    else:
        print("❌ Operazione annullata")

if __name__ == "__main__":
    main()
