{"ast": null, "code": "export { useDesktopPicker } from \"./useDesktopPicker.js\";", "map": {"version": 3, "names": ["useDesktopPicker"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useDesktopPicker/index.js"], "sourcesContent": ["export { useDesktopPicker } from \"./useDesktopPicker.js\";"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}