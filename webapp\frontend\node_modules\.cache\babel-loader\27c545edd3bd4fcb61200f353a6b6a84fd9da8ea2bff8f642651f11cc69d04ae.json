{"ast": null, "code": "import { formatDistance } from \"./zh-TW/_lib/formatDistance.js\";\nimport { formatLong } from \"./zh-TW/_lib/formatLong.js\";\nimport { formatRelative } from \"./zh-TW/_lib/formatRelative.js\";\nimport { localize } from \"./zh-TW/_lib/localize.js\";\nimport { match } from \"./zh-TW/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Chinese Traditional locale.\n * @language Chinese Traditional\n * @iso-639-2 zho\n * <AUTHOR> [@tpai](https://github.com/tpai)\n * <AUTHOR> [@jackhsu978](https://github.com/jackhsu978)\n * <AUTHOR> [@skyuplam](https://github.com/skyuplam)\n */\nexport const zhTW = {\n  code: \"zh-TW\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default zhTW;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "zhTW", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/zh-TW.js"], "sourcesContent": ["import { formatDistance } from \"./zh-TW/_lib/formatDistance.js\";\nimport { formatLong } from \"./zh-TW/_lib/formatLong.js\";\nimport { formatRelative } from \"./zh-TW/_lib/formatRelative.js\";\nimport { localize } from \"./zh-TW/_lib/localize.js\";\nimport { match } from \"./zh-TW/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Chinese Traditional locale.\n * @language Chinese Traditional\n * @iso-639-2 zho\n * <AUTHOR> [@tpai](https://github.com/tpai)\n * <AUTHOR> [@jackhsu978](https://github.com/jackhsu978)\n * <AUTHOR> [@skyuplam](https://github.com/skyuplam)\n */\nexport const zhTW = {\n  code: \"zh-TW\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default zhTW;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,KAAK,QAAQ,uBAAuB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}