{"ast": null, "code": "import { createStepNavigation } from \"./createStepNavigation.js\";\nexport function createNonRangePickerStepNavigation(parameters) {\n  const {\n    steps\n  } = parameters;\n  return createStepNavigation({\n    steps,\n    isViewMatchingStep: (view, step) => {\n      return step.views == null || step.views.includes(view);\n    },\n    onStepChange: ({\n      step,\n      defaultView,\n      setView,\n      view,\n      views\n    }) => {\n      const targetView = step.views == null ? defaultView : step.views.find(viewBis => views.includes(viewBis));\n      if (targetView !== view) {\n        setView(targetView);\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["createStepNavigation", "createNonRangePickerStepNavigation", "parameters", "steps", "isViewMatchingStep", "view", "step", "views", "includes", "onStepChange", "defaultView", "<PERSON><PERSON><PERSON><PERSON>", "targetView", "find", "viewBis"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/utils/createNonRangePickerStepNavigation.js"], "sourcesContent": ["import { createStepNavigation } from \"./createStepNavigation.js\";\nexport function createNonRangePickerStepNavigation(parameters) {\n  const {\n    steps\n  } = parameters;\n  return createStepNavigation({\n    steps,\n    isViewMatchingStep: (view, step) => {\n      return step.views == null || step.views.includes(view);\n    },\n    onStepChange: ({\n      step,\n      defaultView,\n      setView,\n      view,\n      views\n    }) => {\n      const targetView = step.views == null ? defaultView : step.views.find(viewBis => views.includes(viewBis));\n      if (targetView !== view) {\n        setView(targetView);\n      }\n    }\n  });\n}"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,OAAO,SAASC,kCAAkCA,CAACC,UAAU,EAAE;EAC7D,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,OAAOF,oBAAoB,CAAC;IAC1BG,KAAK;IACLC,kBAAkB,EAAEA,CAACC,IAAI,EAAEC,IAAI,KAAK;MAClC,OAAOA,IAAI,CAACC,KAAK,IAAI,IAAI,IAAID,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACH,IAAI,CAAC;IACxD,CAAC;IACDI,YAAY,EAAEA,CAAC;MACbH,IAAI;MACJI,WAAW;MACXC,OAAO;MACPN,IAAI;MACJE;IACF,CAAC,KAAK;MACJ,MAAMK,UAAU,GAAGN,IAAI,CAACC,KAAK,IAAI,IAAI,GAAGG,WAAW,GAAGJ,IAAI,CAACC,KAAK,CAACM,IAAI,CAACC,OAAO,IAAIP,KAAK,CAACC,QAAQ,CAACM,OAAO,CAAC,CAAC;MACzG,IAAIF,UAAU,KAAKP,IAAI,EAAE;QACvBM,OAAO,CAACC,UAAU,CAAC;MACrB;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}