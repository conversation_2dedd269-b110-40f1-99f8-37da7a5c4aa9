import React, { useState, useEffect } from 'react';
import {
  Box,
  IconButton,
  Chip,
  Tooltip,
  Typography
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  ContentCopy as ContentCopyIcon,
  Info as InfoIcon,
  Construction as ConstructionIcon,
  Lock as LockIcon
} from '@mui/icons-material';
import FilterableTable from '../common/FilterableTable';

/**
 * Componente per visualizzare la lista dei cantieri con filtri in stile Excel
 *
 * @param {Object} props - Proprietà del componente
 * @param {Array} props.cantieri - Lista dei cantieri da visualizzare
 * @param {boolean} props.loading - Indica se i dati sono in caricamento
 * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano
 * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare un cantiere
 * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare un cantiere
 * @param {Function} props.onManageCavi - Funzione chiamata quando si vuole gestire i cavi di un cantiere
 * @param {Function} props.onCopyCode - Funzione chiamata quando si vuole copiare il codice univoco
 * @param {Function} props.onManagePassword - Funzione chiamata quando si vuole gestire la password di un cantiere
 */
const CantieriFilterableTable = ({
  cantieri = [],
  loading = false,
  onFilteredDataChange = null,
  onEdit = null,
  onDelete = null,
  onManageCavi = null,
  onCopyCode = null,
  onManagePassword = null
}) => {
  const [filteredCantieri, setFilteredCantieri] = useState(cantieri);

  // Aggiorna i dati filtrati quando cambiano i cantieri
  useEffect(() => {
    setFilteredCantieri(cantieri);
  }, [cantieri]);

  // Notifica il componente padre quando cambiano i dati filtrati
  const handleFilteredDataChange = (data) => {
    setFilteredCantieri(data);
    if (onFilteredDataChange) {
      onFilteredDataChange(data);
    }
  };

  // Funzione per formattare la data
  const formatDate = (dateString) => {
    if (!dateString) return 'N/D';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('it-IT', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      return 'N/D';
    }
  };

  // Definizione delle colonne
  const columns = [
    {
      field: 'nome',
      headerName: 'Nome Cantiere',
      dataType: 'text',
      width: 250,
      renderCell: (row) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <ConstructionIcon fontSize="small" sx={{ mr: 1.5, color: 'primary.main' }} />
          <Typography variant="body1" fontWeight="600" sx={{ fontSize: '1rem' }}>
            {row.nome}
          </Typography>
        </Box>
      )
    },
    {
      field: 'descrizione',
      headerName: 'Descrizione',
      dataType: 'text',
      width: 280,
      renderCell: (row) => (
        <Typography variant="body1" color="text.secondary" sx={{ fontSize: '0.95rem' }}>
          {row.descrizione || 'N/D'}
        </Typography>
      )
    },
    {
      field: 'codice_univoco',
      headerName: 'Codice Univoco',
      dataType: 'text',
      width: 180,
      renderCell: (row) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body1" sx={{ mr: 1, fontSize: '0.95rem', fontWeight: 500 }}>
            {row.codice_univoco}
          </Typography>
          <Tooltip title="Copia codice univoco">
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                if (onCopyCode) onCopyCode(row.codice_univoco);
              }}
            >
              <ContentCopyIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      )
    },
    {
      field: 'data_creazione',
      headerName: 'Data Creazione',
      dataType: 'text',
      width: 140,
      renderCell: (row) => (
        <Typography variant="body1" sx={{ fontSize: '0.95rem' }}>
          {formatDate(row.data_creazione)}
        </Typography>
      )
    },
    {
      field: 'password_status',
      headerName: 'Password',
      dataType: 'text',
      width: 120,
      disableFilter: true,
      renderCell: (row) => (
        <Tooltip title="Clicca per gestire la password">
          <Chip
            label="Gestisci"
            size="small"
            color="primary"
            variant="outlined"
            icon={<LockIcon fontSize="small" />}
            onClick={(e) => {
              e.stopPropagation();
              if (onManagePassword) onManagePassword(row);
            }}
            sx={{
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: 'primary.light',
                color: 'primary.contrastText'
              }
            }}
          />
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Azioni',
      disableFilter: true,
      disableSort: true,
      align: 'center',
      width: 150,
      renderCell: (row) => (
        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5 }}>
          {onManageCavi && (
            <Tooltip title="Gestione Cantiere">
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onManageCavi(row);
                }}
                color="primary"
              >
                <SettingsIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          {onEdit && (
            <Tooltip title="Modifica cantiere">
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(row);
                }}
                color="info"
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          {onDelete && (
            <Tooltip title="Elimina cantiere">
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(row);
                }}
                color="error"
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      )
    }
  ];

  return (
    <Box>
      <FilterableTable
        data={cantieri}
        columns={columns}
        onFilteredDataChange={handleFilteredDataChange}
        loading={loading}
        emptyMessage="Nessun cantiere disponibile"
      />
    </Box>
  );
};

export default CantieriFilterableTable;
