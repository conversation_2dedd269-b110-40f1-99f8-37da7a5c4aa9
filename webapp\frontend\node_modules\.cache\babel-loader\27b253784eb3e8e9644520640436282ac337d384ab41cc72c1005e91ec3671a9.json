{"ast": null, "code": "import { formatDistance } from \"./hr/_lib/formatDistance.js\";\nimport { formatLong } from \"./hr/_lib/formatLong.js\";\nimport { formatRelative } from \"./hr/_lib/formatRelative.js\";\nimport { localize } from \"./hr/_lib/localize.js\";\nimport { match } from \"./hr/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Croatian locale.\n * @language Croatian\n * @iso-639-2 hrv\n * <AUTHOR> [@silvenon](https://github.com/silvenon)\n * <AUTHOR> [@manico](https://github.com/manico)\n * <AUTHOR> [@jerzabek](https://github.com/jerzabek)\n */\nexport const hr = {\n  code: \"hr\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default hr;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "hr", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/hr.js"], "sourcesContent": ["import { formatDistance } from \"./hr/_lib/formatDistance.js\";\nimport { formatLong } from \"./hr/_lib/formatLong.js\";\nimport { formatRelative } from \"./hr/_lib/formatRelative.js\";\nimport { localize } from \"./hr/_lib/localize.js\";\nimport { match } from \"./hr/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Croatian locale.\n * @language Croatian\n * @iso-639-2 hrv\n * <AUTHOR> [@silvenon](https://github.com/silvenon)\n * <AUTHOR> [@manico](https://github.com/manico)\n * <AUTHOR> [@jerzabek](https://github.com/jerzabek)\n */\nexport const hr = {\n  code: \"hr\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default hr;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}