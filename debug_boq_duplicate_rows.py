#!/usr/bin/env python3
"""
Script per debuggare il problema delle righe duplicate nel BOQ.
Analizza i dati dei cavi e delle bobine per identificare le cause delle duplicazioni.
"""

import psycopg2
import psycopg2.extras
from modules.database_pg import Database

def analyze_boq_duplicates():
    """Analizza le cause delle righe duplicate nel BOQ."""
    print("🔍 ANALISI RIGHE DUPLICATE BOQ")
    print("=" * 60)
    
    db = Database()
    conn = db.get_dict_cursor_connection()
    
    try:
        with conn.cursor() as cur:
            cantiere_id = 1
            
            print("📋 STEP 1: ANALISI CAVI PER TIPOLOGIA/SEZIONE")
            print("-" * 50)
            
            # Query per analizzare i cavi
            cur.execute("""
                SELECT
                    tipologia,
                    sezione,
                    COUNT(*) as num_cavi,
                    COUNT(CASE WHEN stato_installazione = 'Installato' THEN 1 END) as cavi_installati,
                    COUNT(CASE WHEN stato_installazione != 'Installato' THEN 1 END) as cavi_da_installare,
                    SUM(metri_teorici) as metri_teorici_totali,
                    SUM(CASE WHEN stato_installazione = 'Installato' THEN metratura_reale ELSE 0 END) as metri_posati,
                    SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) as metri_da_posare,
                    STRING_AGG(DISTINCT id_bobina, ', ') as bobine_associate
                FROM cavi
                WHERE id_cantiere = %s AND (modificato_manualmente IS NULL OR modificato_manualmente != 3)
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """, (cantiere_id,))
            
            cavi_results = cur.fetchall()
            
            print(f"Trovati {len(cavi_results)} gruppi di cavi:")
            for i, cavo in enumerate(cavi_results, 1):
                print(f"\n{i}. TIPOLOGIA: '{cavo['tipologia']}' | SEZIONE: '{cavo['sezione']}'")
                print(f"   Cavi totali: {cavo['num_cavi']}")
                print(f"   Cavi installati: {cavo['cavi_installati']}")
                print(f"   Cavi da installare: {cavo['cavi_da_installare']}")
                print(f"   Metri teorici: {cavo['metri_teorici_totali']}")
                print(f"   Metri posati: {cavo['metri_posati']}")
                print(f"   Metri da posare: {cavo['metri_da_posare']}")
                print(f"   Bobine associate: {cavo['bobine_associate']}")
                
                # Evidenzia possibili duplicati
                if "FG16OR16" in cavo['tipologia'] and "240MM2" in cavo['sezione']:
                    print(f"   ⚠️  POSSIBILE DUPLICATO: Controlla case sensitivity!")
            
            print("\n📦 STEP 2: ANALISI BOBINE PER TIPOLOGIA/SEZIONE")
            print("-" * 50)
            
            # Query per analizzare le bobine
            cur.execute("""
                SELECT
                    tipologia,
                    sezione,
                    COUNT(*) as num_bobine,
                    SUM(metri_totali) as metri_totali,
                    SUM(metri_residui) as metri_residui,
                    stato_bobina,
                    STRING_AGG(id_bobina, ', ') as id_bobine
                FROM parco_cavi
                WHERE id_cantiere = %s
                GROUP BY tipologia, sezione, stato_bobina
                ORDER BY tipologia, sezione, stato_bobina
            """, (cantiere_id,))
            
            bobine_results = cur.fetchall()
            
            print(f"Trovati {len(bobine_results)} gruppi di bobine:")
            for i, bobina in enumerate(bobine_results, 1):
                print(f"\n{i}. TIPOLOGIA: '{bobina['tipologia']}' | SEZIONE: '{bobina['sezione']}'")
                print(f"   Stato: {bobina['stato_bobina']}")
                print(f"   Numero bobine: {bobina['num_bobine']}")
                print(f"   Metri totali: {bobina['metri_totali']}")
                print(f"   Metri residui: {bobina['metri_residui']}")
                print(f"   ID bobine: {bobina['id_bobine']}")
                
                # Evidenzia possibili duplicati
                if "FG16OR16" in bobina['tipologia'] and "240MM2" in bobina['sezione']:
                    print(f"   ⚠️  POSSIBILE DUPLICATO: Controlla case sensitivity!")
            
            print("\n🔍 STEP 3: ANALISI CASE SENSITIVITY")
            print("-" * 50)
            
            # Analizza case sensitivity per FG16OR16
            cur.execute("""
                SELECT DISTINCT tipologia, sezione
                FROM cavi
                WHERE id_cantiere = %s
                  AND tipologia ILIKE %s
                  AND sezione ILIKE %s
                ORDER BY tipologia, sezione
            """, (cantiere_id, '%FG16OR16%', '%240MM2%'))
            
            case_results = cur.fetchall()
            
            print("Varianti trovate per FG16OR16 240MM2:")
            for i, variant in enumerate(case_results, 1):
                print(f"{i}. Tipologia: '{variant['tipologia']}' | Sezione: '{variant['sezione']}'")
                print(f"   Tipologia bytes: {variant['tipologia'].encode()}")
                print(f"   Sezione bytes: {variant['sezione'].encode()}")
            
            print("\n🔧 STEP 4: SIMULAZIONE BOQ QUERY")
            print("-" * 50)
            
            # Simula la query BOQ esatta
            cur.execute("""
                SELECT
                    tipologia,
                    sezione as formazione,
                    COUNT(*) as num_cavi,
                    COUNT(CASE WHEN stato_installazione != 'Installato' THEN 1 END) as num_cavi_rimanenti,
                    SUM(metri_teorici) as metri_teorici_totali,
                    SUM(CASE WHEN stato_installazione = 'Installato' THEN metratura_reale ELSE 0 END) as metri_reali_posati,
                    SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) as metri_da_posare
                FROM cavi
                WHERE id_cantiere = %s AND (modificato_manualmente IS NULL OR modificato_manualmente != 3)
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """, (cantiere_id,))
            
            boq_cavi = cur.fetchall()
            
            print("Risultati query BOQ cavi:")
            for i, row in enumerate(boq_cavi, 1):
                print(f"{i}. {row['tipologia']} | {row['formazione']} | {row['num_cavi']} cavi | {row['metri_da_posare']}m da posare")
                
                if "FG16OR16" in row['tipologia'] and "240MM2" in row['formazione']:
                    print(f"   ⚠️  RIGA PROBLEMATICA IDENTIFICATA!")
            
            # Query bobine BOQ
            cur.execute("""
                SELECT
                    tipologia,
                    sezione as formazione,
                    COUNT(*) as num_bobine,
                    SUM(metri_totali) as metri_acquistati,
                    SUM(metri_residui) as metri_residui
                FROM parco_cavi
                WHERE id_cantiere = %s AND stato_bobina != 'TERMINATA'
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """, (cantiere_id,))
            
            boq_bobine = cur.fetchall()
            
            print("\nRisultati query BOQ bobine:")
            for i, row in enumerate(boq_bobine, 1):
                print(f"{i}. {row['tipologia']} | {row['formazione']} | {row['num_bobine']} bobine | {row['metri_residui']}m residui")
                
                if "FG16OR16" in row['tipologia'] and "240MM2" in row['formazione']:
                    print(f"   ⚠️  RIGA PROBLEMATICA IDENTIFICATA!")
            
            print("\n💡 STEP 5: RACCOMANDAZIONI")
            print("-" * 50)
            
            # Conta le varianti problematiche
            fg16_variants = [r for r in case_results if "FG16OR16" in r['tipologia'] and "240MM2" in r['sezione']]
            
            if len(fg16_variants) > 1:
                print("🚨 PROBLEMA IDENTIFICATO: Case sensitivity nelle sezioni!")
                print(f"   Trovate {len(fg16_variants)} varianti per FG16OR16 240MM2:")
                for variant in fg16_variants:
                    print(f"   - '{variant['tipologia']}' | '{variant['sezione']}'")
                
                print("\n🔧 SOLUZIONI PROPOSTE:")
                print("1. Normalizzare i dati esistenti (UPDATE)")
                print("2. Modificare le query BOQ per usare UPPER() o LOWER()")
                print("3. Implementare normalizzazione nell'import dei dati")
            else:
                print("✅ Nessun problema di case sensitivity rilevato")
                print("   Il problema potrebbe essere altrove...")
                
    except Exception as e:
        print(f"❌ Errore durante l'analisi: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    analyze_boq_duplicates()
