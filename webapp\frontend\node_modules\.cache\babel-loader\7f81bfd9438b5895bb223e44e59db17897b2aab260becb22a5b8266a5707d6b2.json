{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'passat a' p\",\n  yesterday: \"'ièr a' p\",\n  today: \"'uèi a' p\",\n  tomorrow: \"'deman a' p\",\n  nextWeek: \"eeee 'a' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/oc/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee 'passat a' p\",\n  yesterday: \"'ièr a' p\",\n  today: \"'uèi a' p\",\n  tomorrow: \"'deman a' p\",\n  nextWeek: \"eeee 'a' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,WAAW;EACtBC,KAAK,EAAE,WAAW;EAClBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,YAAY;EACtBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}