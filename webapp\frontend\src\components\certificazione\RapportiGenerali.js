import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Assessment as ReportIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { it } from 'date-fns/locale';

import rapportiGeneraliService from '../../services/rapportiGeneraliService';

const RapportiGenerali = forwardRef(({ cantiereId, onError, onSuccess }, ref) => {
  const [rapporti, setRapporti] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedRapporto, setSelectedRapporto] = useState(null);
  const [formData, setFormData] = useState({
    numero_rapporto: '',
    data_rapporto: new Date(),
    nome_progetto: '',
    codice_progetto: '',
    cliente_finale: '',
    localita_impianto: '',
    societa_installatrice: '',
    societa_responsabile_prove: '',
    data_inizio_collaudo: null,
    data_fine_collaudo: null,
    scopo_rapporto: '',
    ambito_collaudo: '',
    temperatura_ambiente: '',
    umidita_ambiente: '',
    responsabile_tecnico: '',
    rappresentante_cliente: '',
    stato_rapporto: 'BOZZA',
    conclusioni: '',
    dichiarazione_conformita: false
  });

  // Carica i rapporti
  const loadRapporti = async () => {
    try {
      setLoading(true);
      const data = await rapportiGeneraliService.getRapporti(cantiereId);
      setRapporti(data);
    } catch (error) {
      onError('Errore nel caricamento dei rapporti generali');
      console.error('Errore nel caricamento dei rapporti:', error);
    } finally {
      setLoading(false);
    }
  };

  // Espone i metodi tramite ref
  useImperativeHandle(ref, () => ({
    handleOptionSelect
  }));

  const handleOptionSelect = (option) => {
    switch (option) {
      case 'visualizzaRapporti':
        loadRapporti();
        break;
      case 'creaRapporto':
        resetForm();
        setDialogType('creaRapporto');
        setOpenDialog(true);
        break;
      case 'modificaRapporto':
        loadRapporti();
        setDialogType('selezionaRapportoModifica');
        setOpenDialog(true);
        break;
      case 'eliminaRapporto':
        loadRapporti();
        setDialogType('selezionaRapportoElimina');
        setOpenDialog(true);
        break;
      case 'dettagliRapporto':
        loadRapporti();
        setDialogType('selezionaRapportoDettagli');
        setOpenDialog(true);
        break;
      default:
        break;
    }
  };

  const resetForm = () => {
    setFormData({
      numero_rapporto: '',
      data_rapporto: new Date(),
      nome_progetto: '',
      codice_progetto: '',
      cliente_finale: '',
      localita_impianto: '',
      societa_installatrice: '',
      societa_responsabile_prove: '',
      data_inizio_collaudo: null,
      data_fine_collaudo: null,
      scopo_rapporto: '',
      ambito_collaudo: '',
      temperatura_ambiente: '',
      umidita_ambiente: '',
      responsabile_tecnico: '',
      rappresentante_cliente: '',
      stato_rapporto: 'BOZZA',
      conclusioni: '',
      dichiarazione_conformita: false
    });
    setSelectedRapporto(null);
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      const submitData = {
        ...formData,
        data_rapporto: formData.data_rapporto.toISOString().split('T')[0],
        data_inizio_collaudo: formData.data_inizio_collaudo ? 
          formData.data_inizio_collaudo.toISOString().split('T')[0] : null,
        data_fine_collaudo: formData.data_fine_collaudo ? 
          formData.data_fine_collaudo.toISOString().split('T')[0] : null,
        temperatura_ambiente: formData.temperatura_ambiente ? 
          parseFloat(formData.temperatura_ambiente) : null,
        umidita_ambiente: formData.umidita_ambiente ? 
          parseFloat(formData.umidita_ambiente) : null
      };

      if (dialogType === 'creaRapporto') {
        await rapportiGeneraliService.createRapporto(cantiereId, submitData);
        onSuccess('Rapporto generale creato con successo');
      } else if (dialogType === 'modificaRapporto') {
        await rapportiGeneraliService.updateRapporto(cantiereId, selectedRapporto.id_rapporto, submitData);
        onSuccess('Rapporto generale aggiornato con successo');
      }

      setOpenDialog(false);
      loadRapporti();
    } catch (error) {
      onError('Errore nel salvataggio del rapporto generale');
      console.error('Errore nel salvataggio:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (rapporto) => {
    if (window.confirm(`Sei sicuro di voler eliminare il rapporto ${rapporto.numero_rapporto}?`)) {
      try {
        setLoading(true);
        await rapportiGeneraliService.deleteRapporto(cantiereId, rapporto.id_rapporto);
        onSuccess('Rapporto generale eliminato con successo');
        loadRapporti();
      } catch (error) {
        onError('Errore nell\'eliminazione del rapporto generale');
        console.error('Errore nell\'eliminazione:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleEdit = (rapporto) => {
    setSelectedRapporto(rapporto);
    setFormData({
      numero_rapporto: rapporto.numero_rapporto || '',
      data_rapporto: new Date(rapporto.data_rapporto),
      nome_progetto: rapporto.nome_progetto || '',
      codice_progetto: rapporto.codice_progetto || '',
      cliente_finale: rapporto.cliente_finale || '',
      localita_impianto: rapporto.localita_impianto || '',
      societa_installatrice: rapporto.societa_installatrice || '',
      societa_responsabile_prove: rapporto.societa_responsabile_prove || '',
      data_inizio_collaudo: rapporto.data_inizio_collaudo ? new Date(rapporto.data_inizio_collaudo) : null,
      data_fine_collaudo: rapporto.data_fine_collaudo ? new Date(rapporto.data_fine_collaudo) : null,
      scopo_rapporto: rapporto.scopo_rapporto || '',
      ambito_collaudo: rapporto.ambito_collaudo || '',
      temperatura_ambiente: rapporto.temperatura_ambiente || '',
      umidita_ambiente: rapporto.umidita_ambiente || '',
      responsabile_tecnico: rapporto.responsabile_tecnico || '',
      rappresentante_cliente: rapporto.rappresentante_cliente || '',
      stato_rapporto: rapporto.stato_rapporto || 'BOZZA',
      conclusioni: rapporto.conclusioni || '',
      dichiarazione_conformita: rapporto.dichiarazione_conformita || false
    });
    setDialogType('modificaRapporto');
    setOpenDialog(true);
  };

  const getStatoColor = (stato) => {
    switch (stato) {
      case 'BOZZA': return 'warning';
      case 'COMPLETATO': return 'info';
      case 'APPROVATO': return 'success';
      default: return 'default';
    }
  };

  const renderRapportiList = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Numero Rapporto</TableCell>
            <TableCell>Data</TableCell>
            <TableCell>Progetto</TableCell>
            <TableCell>Stato</TableCell>
            <TableCell>Cavi Totali</TableCell>
            <TableCell>Conformi</TableCell>
            <TableCell>Non Conformi</TableCell>
            <TableCell>Azioni</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {rapporti.map((rapporto) => (
            <TableRow key={rapporto.id_rapporto}>
              <TableCell>{rapporto.numero_rapporto}</TableCell>
              <TableCell>{new Date(rapporto.data_rapporto).toLocaleDateString('it-IT')}</TableCell>
              <TableCell>{rapporto.nome_progetto || '-'}</TableCell>
              <TableCell>
                <Chip 
                  label={rapporto.stato_rapporto} 
                  color={getStatoColor(rapporto.stato_rapporto)}
                  size="small"
                />
              </TableCell>
              <TableCell>{rapporto.numero_cavi_totali}</TableCell>
              <TableCell>{rapporto.numero_cavi_conformi}</TableCell>
              <TableCell>{rapporto.numero_cavi_non_conformi}</TableCell>
              <TableCell>
                <IconButton onClick={() => handleEdit(rapporto)} size="small">
                  <EditIcon />
                </IconButton>
                <IconButton onClick={() => handleDelete(rapporto)} size="small">
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={it}>
      <Box>
        {/* Lista rapporti */}
        {rapporti.length > 0 && renderRapportiList()}

        {/* Dialog per creazione/modifica */}
        <Dialog 
          open={openDialog && (dialogType === 'creaRapporto' || dialogType === 'modificaRapporto')} 
          onClose={() => setOpenDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            {dialogType === 'creaRapporto' ? 'Nuovo Rapporto Generale' : 'Modifica Rapporto Generale'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Numero Rapporto"
                  value={formData.numero_rapporto}
                  onChange={(e) => handleFormChange('numero_rapporto', e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Data Rapporto"
                  value={formData.data_rapporto}
                  onChange={(date) => handleFormChange('data_rapporto', date)}
                  renderInput={(params) => <TextField {...params} fullWidth required />}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Nome Progetto"
                  value={formData.nome_progetto}
                  onChange={(e) => handleFormChange('nome_progetto', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Codice Progetto"
                  value={formData.codice_progetto}
                  onChange={(e) => handleFormChange('codice_progetto', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Cliente Finale"
                  value={formData.cliente_finale}
                  onChange={(e) => handleFormChange('cliente_finale', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Località Impianto"
                  value={formData.localita_impianto}
                  onChange={(e) => handleFormChange('localita_impianto', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Società Installatrice"
                  value={formData.societa_installatrice}
                  onChange={(e) => handleFormChange('societa_installatrice', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Società Responsabile Prove"
                  value={formData.societa_responsabile_prove}
                  onChange={(e) => handleFormChange('societa_responsabile_prove', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Stato Rapporto</InputLabel>
                  <Select
                    value={formData.stato_rapporto}
                    onChange={(e) => handleFormChange('stato_rapporto', e.target.value)}
                  >
                    <MenuItem value="BOZZA">Bozza</MenuItem>
                    <MenuItem value="COMPLETATO">Completato</MenuItem>
                    <MenuItem value="APPROVATO">Approvato</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Scopo del Rapporto"
                  value={formData.scopo_rapporto}
                  onChange={(e) => handleFormChange('scopo_rapporto', e.target.value)}
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Ambito del Collaudo"
                  value={formData.ambito_collaudo}
                  onChange={(e) => handleFormChange('ambito_collaudo', e.target.value)}
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Responsabile Tecnico"
                  value={formData.responsabile_tecnico}
                  onChange={(e) => handleFormChange('responsabile_tecnico', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Rappresentante Cliente"
                  value={formData.rappresentante_cliente}
                  onChange={(e) => handleFormChange('rappresentante_cliente', e.target.value)}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>Annulla</Button>
            <Button onClick={handleSubmit} variant="contained" disabled={loading}>
              {loading ? 'Salvando...' : 'Salva'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
});

export default RapportiGenerali;
