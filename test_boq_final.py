#!/usr/bin/env python3
"""
Test finale per verificare che il BOQ funzioni correttamente dopo la correzione.
"""

import requests
import json
import sys

# Configurazione
API_URL = "http://localhost:8002/api"
USERNAME = "a"
PASSWORD = "a"

def login():
    """Effettua il login e restituisce il token."""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{API_URL}/auth/login", data=login_data)
    if response.status_code == 200:
        token = response.json()["access_token"]
        print(f"✅ Login effettuato con successo")
        return token
    else:
        print(f"❌ Errore durante il login: {response.status_code}")
        print(response.text)
        return None

def test_boq_report(token, cantiere_id=1):
    """Testa il report BOQ."""
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"\n🧪 Test BOQ Report per cantiere {cantiere_id}")
    print("-" * 50)
    
    response = requests.get(f"{API_URL}/reports/{cantiere_id}/boq?formato=video", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ BOQ Report generato con successo!")
        
        if 'content' in data and 'distinta_materiali' in data['content']:
            distinta = data['content']['distinta_materiali']
            print(f"\n📋 Distinta Materiali ({len(distinta)} righe):")
            
            # Cerca specificamente FG16OR16 240MM2
            fg16_found = []
            for i, item in enumerate(distinta, 1):
                tipologia = item.get('tipologia', 'N/A')
                formazione = item.get('formazione', 'N/A')
                
                print(f"\n{i}. {tipologia} | {formazione}")
                print(f"   N° Cavi: {item.get('num_cavi', 0)}")
                print(f"   N° Cavi Rimanenti: {item.get('num_cavi_rimanenti', 0)}")
                print(f"   Metri Teorici: {item.get('metri_teorici_totali', 0)}")
                print(f"   Metri Posati: {item.get('metri_reali_posati', 0)}")
                print(f"   Metri Rimanenti: {item.get('metri_da_posare', 0)}")
                print(f"   Metri Acquistati: {item.get('metri_acquistati', 0)}")
                print(f"   Metri Residui: {item.get('metri_residui', 0)}")
                print(f"   Metri Mancanti: {item.get('metri_mancanti', 0)}")
                print(f"   Necessita Acquisto: {item.get('necessita_acquisto', False)}")
                
                # Controlla se è FG16OR16 240MM2
                if "FG16OR16" in tipologia and "240MM2" in formazione:
                    fg16_found.append(item)
                    print(f"   🎯 TROVATO FG16OR16 240MM2!")
            
            print(f"\n🔍 VERIFICA DUPLICATI FG16OR16 240MM2:")
            print(f"   Righe trovate: {len(fg16_found)}")
            
            if len(fg16_found) == 0:
                print("   ❌ Nessuna riga FG16OR16 240MM2 trovata!")
            elif len(fg16_found) == 1:
                print("   ✅ Perfetto! Una sola riga FG16OR16 240MM2 trovata")
                item = fg16_found[0]
                print(f"   📊 Dettagli:")
                print(f"      Tipologia: {item.get('tipologia')}")
                print(f"      Formazione: {item.get('formazione')}")
                print(f"      N° Cavi: {item.get('num_cavi')}")
                print(f"      Metri da posare: {item.get('metri_da_posare')}")
                print(f"      Metri residui: {item.get('metri_residui')}")
                print(f"      Metri mancanti: {item.get('metri_mancanti')}")
            else:
                print(f"   ❌ PROBLEMA! Trovate {len(fg16_found)} righe duplicate:")
                for i, item in enumerate(fg16_found, 1):
                    print(f"      {i}. {item.get('tipologia')} | {item.get('formazione')}")
            
            # Verifica riepilogo
            if 'riepilogo' in data['content']:
                riepilogo = data['content']['riepilogo']
                print(f"\n📊 Riepilogo:")
                print(f"   Totale categorie: {riepilogo.get('totale_categorie', 0)}")
                print(f"   Totale metri teorici: {riepilogo.get('totale_metri_teorici', 0)}")
                print(f"   Totale metri posati: {riepilogo.get('totale_metri_posati', 0)}")
                print(f"   Totale metri da posare: {riepilogo.get('totale_metri_da_posare', 0)}")
                print(f"   Percentuale completamento: {riepilogo.get('percentuale_completamento', 0)}%")
            
            return len(fg16_found) == 1  # Successo se c'è esattamente una riga
        else:
            print("❌ Struttura dati BOQ non valida")
            return False
    else:
        print(f"❌ Errore BOQ Report: {response.status_code}")
        print(response.text)
        return False

def main():
    """Funzione principale del test."""
    print("🧪 TEST FINALE BOQ DOPO CORREZIONE")
    print("=" * 60)
    
    # 1. Login
    token = login()
    if not token:
        sys.exit(1)
    
    # 2. Test BOQ
    success = test_boq_report(token)
    
    if success:
        print(f"\n🎉 SUCCESSO! Il problema del BOQ è stato risolto!")
        print("✅ Ora c'è una sola riga per FG16OR16 240MM2")
        print("✅ I calcoli sono corretti")
        print("✅ Non ci sono più duplicazioni")
    else:
        print(f"\n❌ PROBLEMA PERSISTENTE!")
        print("Il BOQ presenta ancora problemi di duplicazione")
        sys.exit(1)

if __name__ == "__main__":
    main()
