{"ast": null, "code": "import axiosInstance from './axiosConfig';\nconst proveDettagliateService = {\n  // Ottiene la lista delle prove dettagliate di una certificazione\n  getProve: async (cantiereId, certificazioneId, tipoProva = null) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const params = {};\n      if (tipoProva) {\n        params.tipo_prova = tipoProva;\n      }\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove`, {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Get prove error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una nuova prova dettagliata\n  createProva: async (cantiereId, certificazioneId, provaData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove`, provaData);\n      return response.data;\n    } catch (error) {\n      console.error('Create prova error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i dettagli di una prova dettagliata\n  getProva: async (cantiereId, certificazioneId, provaId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove/${provaId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get prova error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna una prova dettagliata\n  updateProva: async (cantiereId, certificazioneId, provaId, provaData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove/${provaId}`, provaData);\n      return response.data;\n    } catch (error) {\n      console.error('Update prova error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina una prova dettagliata\n  deleteProva: async (cantiereId, certificazioneId, provaId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove/${provaId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete prova error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i tipi di prova disponibili\n  getTipiProva: async () => {\n    try {\n      const response = await axiosInstance.get('/cantieri/tipi-prova');\n      return response.data;\n    } catch (error) {\n      console.error('Get tipi prova error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default proveDettagliateService;", "map": {"version": 3, "names": ["axiosInstance", "proveDettagliateService", "getProve", "cantiereId", "certificazioneId", "tipoProva", "cantiereIdNum", "parseInt", "isNaN", "Error", "params", "tipo_prova", "response", "get", "data", "error", "console", "createProva", "provaData", "post", "<PERSON><PERSON><PERSON><PERSON>", "provaId", "updateProva", "put", "deleteProva", "delete", "getTipiProva"], "sources": ["C:/CMS/webapp/frontend/src/services/proveDettagliateService.js"], "sourcesContent": ["import axiosInstance from './axiosConfig';\n\nconst proveDettagliateService = {\n  // Ottiene la lista delle prove dettagliate di una certificazione\n  getProve: async (cantiereId, certificazioneId, tipoProva = null) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const params = {};\n      if (tipoProva) {\n        params.tipo_prova = tipoProva;\n      }\n\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove`, {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Get prove error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea una nuova prova dettagliata\n  createProva: async (cantiereId, certificazioneId, provaData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove`, provaData);\n      return response.data;\n    } catch (error) {\n      console.error('Create prova error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene i dettagli di una prova dettagliata\n  getProva: async (cantiereId, certificazioneId, provaId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove/${provaId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get prova error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna una prova dettagliata\n  updateProva: async (cantiereId, certificazioneId, provaId, provaData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove/${provaId}`, provaData);\n      return response.data;\n    } catch (error) {\n      console.error('Update prova error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina una prova dettagliata\n  deleteProva: async (cantiereId, certificazioneId, provaId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove/${provaId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete prova error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene i tipi di prova disponibili\n  getTipiProva: async () => {\n    try {\n      const response = await axiosInstance.get('/cantieri/tipi-prova');\n      return response.data;\n    } catch (error) {\n      console.error('Get tipi prova error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default proveDettagliateService;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,eAAe;AAEzC,MAAMC,uBAAuB,GAAG;EAC9B;EACAC,QAAQ,EAAE,MAAAA,CAAOC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,GAAG,IAAI,KAAK;IAClE,IAAI;MACF,MAAMC,aAAa,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIK,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,MAAM,GAAG,CAAC,CAAC;MACjB,IAAIL,SAAS,EAAE;QACbK,MAAM,CAACC,UAAU,GAAGN,SAAS;MAC/B;MAEA,MAAMO,QAAQ,GAAG,MAAMZ,aAAa,CAACa,GAAG,CAAC,aAAaP,aAAa,mBAAmBF,gBAAgB,QAAQ,EAAE;QAC9GM;MACF,CAAC,CAAC;MACF,OAAOE,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,WAAW,EAAE,MAAAA,CAAOd,UAAU,EAAEC,gBAAgB,EAAEc,SAAS,KAAK;IAC9D,IAAI;MACF,MAAMZ,aAAa,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIK,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAMZ,aAAa,CAACmB,IAAI,CAAC,aAAab,aAAa,mBAAmBF,gBAAgB,QAAQ,EAAEc,SAAS,CAAC;MAC3H,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAK,QAAQ,EAAE,MAAAA,CAAOjB,UAAU,EAAEC,gBAAgB,EAAEiB,OAAO,KAAK;IACzD,IAAI;MACF,MAAMf,aAAa,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIK,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAMZ,aAAa,CAACa,GAAG,CAAC,aAAaP,aAAa,mBAAmBF,gBAAgB,UAAUiB,OAAO,EAAE,CAAC;MAC1H,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAO,WAAW,EAAE,MAAAA,CAAOnB,UAAU,EAAEC,gBAAgB,EAAEiB,OAAO,EAAEH,SAAS,KAAK;IACvE,IAAI;MACF,MAAMZ,aAAa,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIK,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAMZ,aAAa,CAACuB,GAAG,CAAC,aAAajB,aAAa,mBAAmBF,gBAAgB,UAAUiB,OAAO,EAAE,EAAEH,SAAS,CAAC;MACrI,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAS,WAAW,EAAE,MAAAA,CAAOrB,UAAU,EAAEC,gBAAgB,EAAEiB,OAAO,KAAK;IAC5D,IAAI;MACF,MAAMf,aAAa,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIK,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAMZ,aAAa,CAACyB,MAAM,CAAC,aAAanB,aAAa,mBAAmBF,gBAAgB,UAAUiB,OAAO,EAAE,CAAC;MAC7H,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAW,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMZ,aAAa,CAACa,GAAG,CAAC,sBAAsB,CAAC;MAChE,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAed,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}