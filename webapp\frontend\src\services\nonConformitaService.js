import axiosInstance from './axiosConfig';

const nonConformitaService = {
  // Ottiene la lista delle non conformità di un cantiere
  getNonConformita: async (cantiereId, statoNc = null, tipoNc = null, skip = 0, limit = 100) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const params = { skip, limit };
      if (statoNc) {
        params.stato_nc = statoNc;
      }
      if (tipoNc) {
        params.tipo_nc = tipoNc;
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/non-conformita`, {
        params
      });
      return response.data;
    } catch (error) {
      console.error('Get non conformità error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea una nuova non conformità
  createNonConformita: async (cantiereId, ncData) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/non-conformita`, ncData);
      return response.data;
    } catch (error) {
      console.error('Create non conformità error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i dettagli di una non conformità
  getNonConformitaById: async (cantiereId, ncId) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/non-conformita/${ncId}`);
      return response.data;
    } catch (error) {
      console.error('Get non conformità error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna una non conformità
  updateNonConformita: async (cantiereId, ncId, ncData) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/non-conformita/${ncId}`, ncData);
      return response.data;
    } catch (error) {
      console.error('Update non conformità error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Elimina una non conformità
  deleteNonConformita: async (cantiereId, ncId) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/non-conformita/${ncId}`);
      return response.data;
    } catch (error) {
      console.error('Delete non conformità error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i tipi di non conformità disponibili
  getTipiNc: async () => {
    try {
      const response = await axiosInstance.get('/cantieri/tipi-nc');
      return response.data;
    } catch (error) {
      console.error('Get tipi NC error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default nonConformitaService;
