import axiosInstance from './axiosConfig';

const rapportiGeneraliService = {
  // Ottiene la lista dei rapporti generali di un cantiere
  getRapporti: async (cantiereId, skip = 0, limit = 100) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/rapporti`, {
        params: { skip, limit }
      });
      return response.data;
    } catch (error) {
      console.error('Get rapporti error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea un nuovo rapporto generale
  createRapporto: async (cantiereId, rapportoData) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/rapporti`, rapportoData);
      return response.data;
    } catch (error) {
      console.error('Create rapporto error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i dettagli di un rapporto generale
  getRapporto: async (cantiereId, rapportoId) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}`);
      return response.data;
    } catch (error) {
      console.error('Get rapporto error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna un rapporto generale
  updateRapporto: async (cantiereId, rapportoId, rapportoData) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}`, rapportoData);
      return response.data;
    } catch (error) {
      console.error('Update rapporto error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Elimina un rapporto generale
  deleteRapporto: async (cantiereId, rapportoId) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}`);
      return response.data;
    } catch (error) {
      console.error('Delete rapporto error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna le statistiche di un rapporto
  aggiornaStatistiche: async (cantiereId, rapportoId) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}/aggiorna-statistiche`);
      return response.data;
    } catch (error) {
      console.error('Aggiorna statistiche error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default rapportiGeneraliService;
