{"ast": null, "code": "import { formatDistance } from \"./fa-IR/_lib/formatDistance.js\";\nimport { formatLong } from \"./fa-IR/_lib/formatLong.js\";\nimport { formatRelative } from \"./fa-IR/_lib/formatRelative.js\";\nimport { localize } from \"./fa-IR/_lib/localize.js\";\nimport { match } from \"./fa-IR/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Persian/Farsi locale (Iran).\n * @language Persian\n * @iso-639-2 ira\n * <AUTHOR> [@mort3za](https://github.com/mort3za)\n */\nexport const faIR = {\n  code: \"fa-IR\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 6 /* Saturday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default faIR;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "faIR", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/fa-IR.js"], "sourcesContent": ["import { formatDistance } from \"./fa-IR/_lib/formatDistance.js\";\nimport { formatLong } from \"./fa-IR/_lib/formatLong.js\";\nimport { formatRelative } from \"./fa-IR/_lib/formatRelative.js\";\nimport { localize } from \"./fa-IR/_lib/localize.js\";\nimport { match } from \"./fa-IR/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Persian/Farsi locale (Iran).\n * @language Persian\n * @iso-639-2 ira\n * <AUTHOR> [@mort3za](https://github.com/mort3za)\n */\nexport const faIR = {\n  code: \"fa-IR\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 6 /* Saturday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default faIR;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,KAAK,QAAQ,uBAAuB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}