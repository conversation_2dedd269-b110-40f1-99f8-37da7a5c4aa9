{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"B\", \"คศ\"],\n  abbreviated: [\"BC\", \"ค.ศ.\"],\n  wide: [\"ปีก่อนคริสตกาล\", \"คริสต์ศักราช\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"ไตรมาสแรก\", \"ไตรมาสที่สอง\", \"ไตรมาสที่สาม\", \"ไตรมาสที่สี่\"]\n};\nconst dayValues = {\n  narrow: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  short: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  abbreviated: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  wide: [\"อาทิตย์\", \"จันทร์\", \"อังคาร\", \"พุธ\", \"พฤหัสบดี\", \"ศุกร์\", \"เสาร์\"]\n};\nconst monthValues = {\n  narrow: [\"ม.ค.\", \"ก.พ.\", \"มี.ค.\", \"เม.ย.\", \"พ.ค.\", \"มิ.ย.\", \"ก.ค.\", \"ส.ค.\", \"ก.ย.\", \"ต.ค.\", \"พ.ย.\", \"ธ.ค.\"],\n  abbreviated: [\"ม.ค.\", \"ก.พ.\", \"มี.ค.\", \"เม.ย.\", \"พ.ค.\", \"มิ.ย.\", \"ก.ค.\", \"ส.ค.\", \"ก.ย.\", \"ต.ค.\", \"พ.ย.\", \"ธ.ค.\"],\n  wide: [\"มกราคม\", \"กุมภาพันธ์\", \"มีนาคม\", \"เมษายน\", \"พฤษภาคม\", \"มิถุนายน\", \"กรกฎาคม\", \"สิงหาคม\", \"กันยายน\", \"ตุลาคม\", \"พฤศจิกายน\", \"ธันวาคม\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\"\n  },\n  abbreviated: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\"\n  },\n  wide: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\"\n  },\n  abbreviated: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\"\n  },\n  wide: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "dayV<PERSON><PERSON>", "short", "month<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/th/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"B\", \"คศ\"],\n  abbreviated: [\"BC\", \"ค.ศ.\"],\n  wide: [\"ปีก่อนคริสตกาล\", \"คริสต์ศักราช\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"ไตรมาสแรก\", \"ไตรมาสที่สอง\", \"ไตรมาสที่สาม\", \"ไตรมาสที่สี่\"],\n};\n\nconst dayValues = {\n  narrow: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  short: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  abbreviated: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  wide: [\"อาทิตย์\", \"จันทร์\", \"อังคาร\", \"พุธ\", \"พฤหัสบดี\", \"ศุกร์\", \"เสาร์\"],\n};\n\nconst monthValues = {\n  narrow: [\n    \"ม.ค.\",\n    \"ก.พ.\",\n    \"มี.ค.\",\n    \"เม.ย.\",\n    \"พ.ค.\",\n    \"มิ.ย.\",\n    \"ก.ค.\",\n    \"ส.ค.\",\n    \"ก.ย.\",\n    \"ต.ค.\",\n    \"พ.ย.\",\n    \"ธ.ค.\",\n  ],\n\n  abbreviated: [\n    \"ม.ค.\",\n    \"ก.พ.\",\n    \"มี.ค.\",\n    \"เม.ย.\",\n    \"พ.ค.\",\n    \"มิ.ย.\",\n    \"ก.ค.\",\n    \"ส.ค.\",\n    \"ก.ย.\",\n    \"ต.ค.\",\n    \"พ.ย.\",\n    \"ธ.ค.\",\n  ],\n\n  wide: [\n    \"มกราคม\",\n    \"กุมภาพันธ์\",\n    \"มีนาคม\",\n    \"เมษายน\",\n    \"พฤษภาคม\",\n    \"มิถุนายน\",\n    \"กรกฎาคม\",\n    \"สิงหาคม\",\n    \"กันยายน\",\n    \"ตุลาคม\",\n    \"พฤศจิกายน\",\n    \"ธันวาคม\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\",\n  },\n  abbreviated: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\",\n  },\n  wide: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\",\n  },\n  abbreviated: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\",\n  },\n  wide: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;EACnBC,WAAW,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;EAC3BC,IAAI,EAAE,CAAC,gBAAgB,EAAE,cAAc;AACzC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;AACpE,CAAC;AAED,MAAME,SAAS,GAAG;EAChBJ,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EACpDK,KAAK,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EACnDJ,WAAW,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EACzDC,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO;AAC3E,CAAC;AAED,MAAMI,WAAW,GAAG;EAClBN,MAAM,EAAE,CACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;EAEDC,WAAW,EAAE,CACX,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,UAAU,EACV,SAAS,EACT,SAAS,EACT,SAAS,EACT,QAAQ,EACR,WAAW,EACX,SAAS;AAEb,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,YAAY;IAChBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBJ,aAAa;EAEbK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEjB,WAAW;IACnBkB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}