{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { validateDate } from \"../validation/index.js\";\nimport { useLocalizationContext } from \"../internals/hooks/useUtils.js\";\nexport const useIsDateDisabled = ({\n  shouldDisableDate,\n  shouldDisableMonth,\n  shouldDisableYear,\n  minDate,\n  maxDate,\n  disableFuture,\n  disablePast,\n  timezone\n}) => {\n  const adapter = useLocalizationContext();\n  return React.useCallback(day => validateDate({\n    adapter,\n    value: day,\n    timezone,\n    props: {\n      shouldDisableDate,\n      shouldDisableMonth,\n      shouldDisableYear,\n      minDate,\n      maxDate,\n      disableFuture,\n      disablePast\n    }\n  }) !== null, [adapter, shouldDisableDate, shouldDisableMonth, shouldDisableYear, minDate, maxDate, disableFuture, disablePast, timezone]);\n};", "map": {"version": 3, "names": ["React", "validateDate", "useLocalizationContext", "useIsDateDisabled", "shouldDisableDate", "shouldDisableMonth", "shouldDisableYear", "minDate", "maxDate", "disableFuture", "disablePast", "timezone", "adapter", "useCallback", "day", "value", "props"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/DateCalendar/useIsDateDisabled.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { validateDate } from \"../validation/index.js\";\nimport { useLocalizationContext } from \"../internals/hooks/useUtils.js\";\nexport const useIsDateDisabled = ({\n  shouldDisableDate,\n  shouldDisableMonth,\n  shouldDisableYear,\n  minDate,\n  maxDate,\n  disableFuture,\n  disablePast,\n  timezone\n}) => {\n  const adapter = useLocalizationContext();\n  return React.useCallback(day => validateDate({\n    adapter,\n    value: day,\n    timezone,\n    props: {\n      shouldDisableDate,\n      shouldDisableMonth,\n      shouldDisableYear,\n      minDate,\n      maxDate,\n      disableFuture,\n      disablePast\n    }\n  }) !== null, [adapter, shouldDisableDate, shouldDisableMonth, shouldDisableYear, minDate, maxDate, disableFuture, disablePast, timezone]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAChCC,iBAAiB;EACjBC,kBAAkB;EAClBC,iBAAiB;EACjBC,OAAO;EACPC,OAAO;EACPC,aAAa;EACbC,WAAW;EACXC;AACF,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAGV,sBAAsB,CAAC,CAAC;EACxC,OAAOF,KAAK,CAACa,WAAW,CAACC,GAAG,IAAIb,YAAY,CAAC;IAC3CW,OAAO;IACPG,KAAK,EAAED,GAAG;IACVH,QAAQ;IACRK,KAAK,EAAE;MACLZ,iBAAiB;MACjBC,kBAAkB;MAClBC,iBAAiB;MACjBC,OAAO;MACPC,OAAO;MACPC,aAAa;MACbC;IACF;EACF,CAAC,CAAC,KAAK,IAAI,EAAE,CAACE,OAAO,EAAER,iBAAiB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,OAAO,EAAEC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,CAAC,CAAC;AAC3I,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}