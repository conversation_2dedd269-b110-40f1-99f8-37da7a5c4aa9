{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { PickerPrivateContext } from \"../components/PickerProvider.js\";\n\n/**\n * Returns the private context passed by the Picker wrapping the current component.\n */\nexport const usePickerPrivateContext = () => React.useContext(PickerPrivateContext);", "map": {"version": 3, "names": ["React", "PickerPrivateContext", "usePickerPrivateContext", "useContext"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/usePickerPrivateContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { PickerPrivateContext } from \"../components/PickerProvider.js\";\n\n/**\n * Returns the private context passed by the Picker wrapping the current component.\n */\nexport const usePickerPrivateContext = () => React.useContext(PickerPrivateContext);"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,oBAAoB,QAAQ,iCAAiC;;AAEtE;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAGA,CAAA,KAAMF,KAAK,CAACG,UAAU,CAACF,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}