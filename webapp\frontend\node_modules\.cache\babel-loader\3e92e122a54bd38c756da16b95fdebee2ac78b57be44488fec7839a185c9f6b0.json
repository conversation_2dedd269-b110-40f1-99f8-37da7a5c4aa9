{"ast": null, "code": "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\nconst matchOrdinalNumberPattern = /^\\d+/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n  narrow: /^([bB]|[aA]|คศ)/i,\n  abbreviated: /^([bB]\\.?\\s?[cC]\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?|ค\\.?ศ\\.?)/i,\n  wide: /^(ก่อนคริสตกาล|คริสต์ศักราช|คริสตกาล)/i\n};\nconst parseEraPatterns = {\n  any: [/^[bB]/i, /^(^[aA]|ค\\.?ศ\\.?|คริสตกาล|คริสต์ศักราช|)/i]\n};\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^ไตรมาส(ที่)? ?[1234]/i\n};\nconst parseQuarterPatterns = {\n  any: [/(1|แรก|หนึ่ง)/i, /(2|สอง)/i, /(3|สาม)/i, /(4|สี่)/i]\n};\nconst matchMonthPatterns = {\n  narrow: /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?)/i,\n  abbreviated: /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?')/i,\n  wide: /^(มกราคม|กุมภาพันธ์|มีนาคม|เมษายน|พฤษภาคม|มิถุนายน|กรกฎาคม|สิงหาคม|กันยายน|ตุลาคม|พฤศจิกายน|ธันวาคม)/i\n};\nconst parseMonthPatterns = {\n  wide: [/^มก/i, /^กุม/i, /^มี/i, /^เม/i, /^พฤษ/i, /^มิ/i, /^กรก/i, /^ส/i, /^กัน/i, /^ต/i, /^พฤศ/i, /^ธ/i],\n  any: [/^ม\\.?ค\\.?/i, /^ก\\.?พ\\.?/i, /^มี\\.?ค\\.?/i, /^เม\\.?ย\\.?/i, /^พ\\.?ค\\.?/i, /^มิ\\.?ย\\.?/i, /^ก\\.?ค\\.?/i, /^ส\\.?ค\\.?/i, /^ก\\.?ย\\.?/i, /^ต\\.?ค\\.?/i, /^พ\\.?ย\\.?/i, /^ธ\\.?ค\\.?/i]\n};\nconst matchDayPatterns = {\n  narrow: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  short: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  abbreviated: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  wide: /^(อาทิตย์|จันทร์|อังคาร|พุธ|พฤหัสบดี|ศุกร์|เสาร์)/i\n};\nconst parseDayPatterns = {\n  wide: [/^อา/i, /^จั/i, /^อั/i, /^พุธ/i, /^พฤ/i, /^ศ/i, /^เส/i],\n  any: [/^อา/i, /^จ/i, /^อ/i, /^พ(?!ฤ)/i, /^พฤ/i, /^ศ/i, /^ส/i]\n};\nconst matchDayPeriodPatterns = {\n  any: /^(ก่อนเที่ยง|หลังเที่ยง|เที่ยงคืน|เที่ยง|(ตอน.*?)?.*(เที่ยง|เช้า|บ่าย|เย็น|กลางคืน))/i\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^ก่อนเที่ยง/i,\n    pm: /^หลังเที่ยง/i,\n    midnight: /^เที่ยงคืน/i,\n    noon: /^เที่ยง/i,\n    morning: /เช้า/i,\n    afternoon: /บ่าย/i,\n    evening: /เย็น/i,\n    night: /กลางคืน/i\n  }\n};\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: value => parseInt(value, 10)\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: index => index + 1\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};", "map": {"version": 3, "names": ["buildMatchFn", "buildMatchPatternFn", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "narrow", "abbreviated", "wide", "parseEraPatterns", "any", "matchQuarterPatterns", "parseQuarterPatterns", "matchMonthPatterns", "parseMonthPatterns", "matchDayPatterns", "short", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "match", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "value", "parseInt", "era", "matchPatterns", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/th/_lib/match.js"], "sourcesContent": ["import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern = /^\\d+/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^([bB]|[aA]|คศ)/i,\n  abbreviated:\n    /^([bB]\\.?\\s?[cC]\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?|ค\\.?ศ\\.?)/i,\n  wide: /^(ก่อนคริสตกาล|คริสต์ศักราช|คริสตกาล)/i,\n};\nconst parseEraPatterns = {\n  any: [/^[bB]/i, /^(^[aA]|ค\\.?ศ\\.?|คริสตกาล|คริสต์ศักราช|)/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^ไตรมาส(ที่)? ?[1234]/i,\n};\nconst parseQuarterPatterns = {\n  any: [/(1|แรก|หนึ่ง)/i, /(2|สอง)/i, /(3|สาม)/i, /(4|สี่)/i],\n};\n\nconst matchMonthPatterns = {\n  narrow:\n    /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?)/i,\n  abbreviated:\n    /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?')/i,\n  wide: /^(มกราคม|กุมภาพันธ์|มีนาคม|เมษายน|พฤษภาคม|มิถุนายน|กรกฎาคม|สิงหาคม|กันยายน|ตุลาคม|พฤศจิกายน|ธันวาคม)/i,\n};\nconst parseMonthPatterns = {\n  wide: [\n    /^มก/i,\n    /^กุม/i,\n    /^มี/i,\n    /^เม/i,\n    /^พฤษ/i,\n    /^มิ/i,\n    /^กรก/i,\n    /^ส/i,\n    /^กัน/i,\n    /^ต/i,\n    /^พฤศ/i,\n    /^ธ/i,\n  ],\n\n  any: [\n    /^ม\\.?ค\\.?/i,\n    /^ก\\.?พ\\.?/i,\n    /^มี\\.?ค\\.?/i,\n    /^เม\\.?ย\\.?/i,\n    /^พ\\.?ค\\.?/i,\n    /^มิ\\.?ย\\.?/i,\n    /^ก\\.?ค\\.?/i,\n    /^ส\\.?ค\\.?/i,\n    /^ก\\.?ย\\.?/i,\n    /^ต\\.?ค\\.?/i,\n    /^พ\\.?ย\\.?/i,\n    /^ธ\\.?ค\\.?/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  short: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  abbreviated: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  wide: /^(อาทิตย์|จันทร์|อังคาร|พุธ|พฤหัสบดี|ศุกร์|เสาร์)/i,\n};\nconst parseDayPatterns = {\n  wide: [/^อา/i, /^จั/i, /^อั/i, /^พุธ/i, /^พฤ/i, /^ศ/i, /^เส/i],\n  any: [/^อา/i, /^จ/i, /^อ/i, /^พ(?!ฤ)/i, /^พฤ/i, /^ศ/i, /^ส/i],\n};\n\nconst matchDayPeriodPatterns = {\n  any: /^(ก่อนเที่ยง|หลังเที่ยง|เที่ยงคืน|เที่ยง|(ตอน.*?)?.*(เที่ยง|เช้า|บ่าย|เย็น|กลางคืน))/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^ก่อนเที่ยง/i,\n    pm: /^หลังเที่ยง/i,\n    midnight: /^เที่ยงคืน/i,\n    noon: /^เที่ยง/i,\n    morning: /เช้า/i,\n    afternoon: /บ่าย/i,\n    evening: /เย็น/i,\n    night: /กลางคืน/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,4BAA4B;AACzD,SAASC,mBAAmB,QAAQ,mCAAmC;AAEvE,MAAMC,yBAAyB,GAAG,OAAO;AACzC,MAAMC,yBAAyB,GAAG,MAAM;AAExC,MAAMC,gBAAgB,GAAG;EACvBC,MAAM,EAAE,kBAAkB;EAC1BC,WAAW,EACT,2EAA2E;EAC7EC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,gBAAgB,GAAG;EACvBC,GAAG,EAAE,CAAC,QAAQ,EAAE,2CAA2C;AAC7D,CAAC;AAED,MAAMC,oBAAoB,GAAG;EAC3BL,MAAM,EAAE,UAAU;EAClBC,WAAW,EAAE,WAAW;EACxBC,IAAI,EAAE;AACR,CAAC;AACD,MAAMI,oBAAoB,GAAG;EAC3BF,GAAG,EAAE,CAAC,gBAAgB,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAC5D,CAAC;AAED,MAAMG,kBAAkB,GAAG;EACzBP,MAAM,EACJ,oHAAoH;EACtHC,WAAW,EACT,qHAAqH;EACvHC,IAAI,EAAE;AACR,CAAC;AACD,MAAMM,kBAAkB,GAAG;EACzBN,IAAI,EAAE,CACJ,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,KAAK,EACL,OAAO,EACP,KAAK,EACL,OAAO,EACP,KAAK,CACN;EAEDE,GAAG,EAAE,CACH,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,aAAa,EACb,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY;AAEhB,CAAC;AAED,MAAMK,gBAAgB,GAAG;EACvBT,MAAM,EAAE,0CAA0C;EAClDU,KAAK,EAAE,0CAA0C;EACjDT,WAAW,EAAE,0CAA0C;EACvDC,IAAI,EAAE;AACR,CAAC;AACD,MAAMS,gBAAgB,GAAG;EACvBT,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;EAC9DE,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;AAC9D,CAAC;AAED,MAAMQ,sBAAsB,GAAG;EAC7BR,GAAG,EAAE;AACP,CAAC;AACD,MAAMS,sBAAsB,GAAG;EAC7BT,GAAG,EAAE;IACHU,EAAE,EAAE,cAAc;IAClBC,EAAE,EAAE,cAAc;IAClBC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMC,KAAK,GAAG;EACnBC,aAAa,EAAE3B,mBAAmB,CAAC;IACjC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,YAAY,EAAE3B,yBAAyB;IACvC4B,aAAa,EAAGC,KAAK,IAAKC,QAAQ,CAACD,KAAK,EAAE,EAAE;EAC9C,CAAC,CAAC;EAEFE,GAAG,EAAElC,YAAY,CAAC;IAChBmC,aAAa,EAAE/B,gBAAgB;IAC/BgC,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE7B,gBAAgB;IAC/B8B,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFC,OAAO,EAAEvC,YAAY,CAAC;IACpBmC,aAAa,EAAEzB,oBAAoB;IACnC0B,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAE1B,oBAAoB;IACnC2B,iBAAiB,EAAE,KAAK;IACxBP,aAAa,EAAGS,KAAK,IAAKA,KAAK,GAAG;EACpC,CAAC,CAAC;EAEFC,KAAK,EAAEzC,YAAY,CAAC;IAClBmC,aAAa,EAAEvB,kBAAkB;IACjCwB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAExB,kBAAkB;IACjCyB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFI,GAAG,EAAE1C,YAAY,CAAC;IAChBmC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE,MAAM;IACzBC,aAAa,EAAErB,gBAAgB;IAC/BsB,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFK,SAAS,EAAE3C,YAAY,CAAC;IACtBmC,aAAa,EAAElB,sBAAsB;IACrCmB,iBAAiB,EAAE,KAAK;IACxBC,aAAa,EAAEnB,sBAAsB;IACrCoB,iBAAiB,EAAE;EACrB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}