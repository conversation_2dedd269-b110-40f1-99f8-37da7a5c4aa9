{"ast": null, "code": "import { styled } from '@mui/material/styles';\nimport { DIALOG_WIDTH, VIEW_HEIGHT } from \"../../constants/dimensions.js\";\nexport const PickerViewRoot = styled('div')({\n  overflow: 'hidden',\n  width: DIALOG_WIDTH,\n  maxHeight: VIEW_HEIGHT,\n  display: 'flex',\n  flexDirection: 'column',\n  margin: '0 auto'\n});", "map": {"version": 3, "names": ["styled", "DIALOG_WIDTH", "VIEW_HEIGHT", "PickerViewRoot", "overflow", "width", "maxHeight", "display", "flexDirection", "margin"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/components/PickerViewRoot/PickerViewRoot.js"], "sourcesContent": ["import { styled } from '@mui/material/styles';\nimport { DIALOG_WIDTH, VIEW_HEIGHT } from \"../../constants/dimensions.js\";\nexport const PickerViewRoot = styled('div')({\n  overflow: 'hidden',\n  width: DIALOG_WIDTH,\n  maxHeight: VIEW_HEIGHT,\n  display: 'flex',\n  flexDirection: 'column',\n  margin: '0 auto'\n});"], "mappings": "AAAA,SAASA,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,YAAY,EAAEC,WAAW,QAAQ,+BAA+B;AACzE,OAAO,MAAMC,cAAc,GAAGH,MAAM,CAAC,KAAK,CAAC,CAAC;EAC1CI,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAEJ,YAAY;EACnBK,SAAS,EAAEJ,WAAW;EACtBK,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvBC,MAAM,EAAE;AACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}