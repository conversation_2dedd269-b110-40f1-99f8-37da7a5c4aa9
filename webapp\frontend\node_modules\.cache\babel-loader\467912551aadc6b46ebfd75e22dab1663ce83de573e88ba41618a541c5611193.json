{"ast": null, "code": "import { formatDistance } from \"./sr-Latn/_lib/formatDistance.js\";\nimport { formatLong } from \"./sr-Latn/_lib/formatLong.js\";\nimport { formatRelative } from \"./sr-Latn/_lib/formatRelative.js\";\nimport { localize } from \"./sr-Latn/_lib/localize.js\";\nimport { match } from \"./sr-Latn/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Serbian latin locale.\n * @language Serbian\n * @iso-639-2 srp\n * <AUTHOR> [@rogyvoje](https://github.com/rogyvoje)\n */\nexport const srLatn = {\n  code: \"sr-Latn\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default srLatn;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "srLatn", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/sr-Latn.js"], "sourcesContent": ["import { formatDistance } from \"./sr-Latn/_lib/formatDistance.js\";\nimport { formatLong } from \"./sr-Latn/_lib/formatLong.js\";\nimport { formatRelative } from \"./sr-Latn/_lib/formatRelative.js\";\nimport { localize } from \"./sr-Latn/_lib/localize.js\";\nimport { match } from \"./sr-Latn/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Serbian latin locale.\n * @language Serbian\n * @iso-639-2 srp\n * <AUTHOR> [@rogyvoje](https://github.com/rogyvoje)\n */\nexport const srLatn = {\n  code: \"sr-Latn\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default srLatn;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kCAAkC;AACjE,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,KAAK,QAAQ,yBAAyB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,IAAI,EAAE,SAAS;EACfN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}