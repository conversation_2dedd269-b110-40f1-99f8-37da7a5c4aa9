{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'पिछले' eeee p\",\n  yesterday: \"'कल' p\",\n  today: \"'आज' p\",\n  tomorrow: \"'कल' p\",\n  nextWeek: \"eeee 'को' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/hi/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'पिछले' eeee p\",\n  yesterday: \"'कल' p\",\n  today: \"'आज' p\",\n  tomorrow: \"'कल' p\",\n  nextWeek: \"eeee 'को' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,gBAAgB;EAC1BC,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}