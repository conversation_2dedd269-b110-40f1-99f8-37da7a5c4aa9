{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\LoginPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Container, Box, Typography, TextField, Button, Paper, Card, CardContent, CardActions, Grid, Alert, CircularProgress, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions } from '@mui/material';\nimport { AdminPanelSettings as AdminIcon, Person as UserIcon, Construction as ConstructionIcon, Password as PasswordIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPageNew = () => {\n  _s();\n  const [loginType, setLoginType] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPasswordRecoveryDialog, setShowPasswordRecoveryDialog] = useState(false);\n  const [recoveryEmail, setRecoveryEmail] = useState('');\n\n  // Credenziali per i diversi tipi di login\n  const [adminCredentials, setAdminCredentials] = useState({\n    username: '',\n    password: ''\n  });\n  const [userCredentials, setUserCredentials] = useState({\n    username: '',\n    password: ''\n  });\n  const [cantiereCredentials, setCantiereCredentials] = useState({\n    codice_univoco: '',\n    password: ''\n  });\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // Gestione dell'input per il login amministratore\n  const handleAdminInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setAdminCredentials({\n      ...adminCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il login utente standard\n  const handleUserInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setUserCredentials({\n      ...userCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il login cantiere\n  const handleCantiereInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setCantiereCredentials({\n      ...cantiereCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il recupero password\n  const handleRecoveryEmailChange = e => {\n    setRecoveryEmail(e.target.value);\n  };\n\n  // Gestione del login amministratore\n  const handleAdminLogin = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login amministratore con:', adminCredentials);\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!adminCredentials.username || !adminCredentials.password) {\n        console.log('Campi vuoti nel form di login admin');\n        setError('Username e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n      console.log('Chiamata alla funzione login per admin...');\n      try {\n        const userData = await login(adminCredentials, 'standard');\n        console.log('Login admin completato, dati utente:', userData);\n\n        // Verifica che l'utente sia un amministratore\n        if (userData.role !== 'owner') {\n          console.log('Utente non ha ruolo owner:', userData.role);\n          setError('Non hai i permessi di amministratore');\n          setLoading(false);\n          return;\n        }\n\n        // Reindirizza alla dashboard di amministrazione\n        console.log('Reindirizzamento a /dashboard/admin');\n        // Utilizza navigate invece di window.location per evitare refresh completo\n        navigate('/dashboard/admin');\n      } catch (loginError) {\n        console.error('Errore specifico durante login admin:', loginError);\n        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');\n        setLoading(false);\n      }\n    } catch (err) {\n      console.error('Errore generale durante login admin:', err);\n      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del login utente standard\n  const handleUserLogin = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login utente standard con:', userCredentials);\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!userCredentials.username || !userCredentials.password) {\n        console.log('Campi vuoti nel form di login utente standard');\n        setError('Username e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n      console.log('Chiamata alla funzione login per utente standard...');\n      try {\n        const userData = await login(userCredentials, 'standard');\n        console.log('Login utente standard completato, dati utente:', userData);\n\n        // Verifica che l'utente sia un utente standard\n        if (userData.role !== 'user') {\n          console.log('Utente non ha ruolo user:', userData.role);\n          setError('Non hai i permessi di utente standard');\n          setLoading(false);\n          return;\n        }\n\n        // Reindirizza alla dashboard utente\n        console.log('Reindirizzamento a /dashboard/cantieri');\n        // Utilizza navigate invece di window.location per evitare refresh completo\n        navigate('/dashboard/cantieri');\n      } catch (loginError) {\n        console.error('Errore specifico durante login utente standard:', loginError);\n        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');\n        setLoading(false);\n      }\n    } catch (err) {\n      console.error('Errore generale durante login utente standard:', err);\n      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del login cantiere\n  const handleCantiereLogin = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login cantiere con:', cantiereCredentials);\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!cantiereCredentials.codice_univoco || !cantiereCredentials.password) {\n        console.log('Campi vuoti nel form di login cantiere');\n        setError('Codice univoco e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n      console.log('Chiamata alla funzione login per cantiere...');\n      try {\n        const userData = await login(cantiereCredentials, 'cantiere');\n        console.log('Login cantiere completato, dati utente:', userData);\n\n        // Salva esplicitamente l'ID e il nome del cantiere nel localStorage\n        if (userData.cantiere_id) {\n          console.log('Salvando ID cantiere nel localStorage:', userData.cantiere_id);\n          localStorage.setItem('selectedCantiereId', userData.cantiere_id.toString());\n          localStorage.setItem('selectedCantiereName', userData.cantiere_name || `Cantiere ${userData.cantiere_id}`);\n        } else {\n          console.warn('Risposta login cantiere non contiene cantiere_id:', userData);\n        }\n\n        // Breve ritardo per assicurarsi che i dati siano salvati nel localStorage\n        setTimeout(() => {\n          // Reindirizza alla pagina di visualizzazione cavi\n          console.log('Reindirizzamento a /dashboard/cavi/visualizza');\n          // Utilizza navigate invece di window.location per evitare refresh completo\n          navigate('/dashboard/cavi/visualizza');\n        }, 500);\n      } catch (loginError) {\n        console.error('Errore specifico durante login cantiere:', loginError);\n        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');\n        setLoading(false);\n      }\n    } catch (err) {\n      console.error('Errore generale durante login cantiere:', err);\n      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del recupero password\n  const handlePasswordRecovery = () => {\n    // Qui implementeremo la logica per il recupero della password\n    alert('Funzionalità di recupero password non ancora implementata');\n    setShowPasswordRecoveryDialog(false);\n  };\n\n  // Torna al menu principale\n  const handleBackToMainMenu = () => {\n    setLoginType(null);\n    setError('');\n  };\n\n  // Renderizza il form di login appropriato in base al tipo selezionato\n  const renderLoginForm = () => {\n    switch (loginType) {\n      case 'admin':\n        return /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          onSubmit: handleAdminLogin,\n          noValidate: true,\n          sx: {\n            maxWidth: '400px',\n            margin: '0 auto',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            sx: {\n              mb: 3,\n              fontWeight: 600\n            },\n            children: \"Login Amministratore\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            id: \"admin-username\",\n            label: \"Username\",\n            name: \"username\",\n            autoComplete: \"username\",\n            autoFocus: true,\n            value: adminCredentials.username,\n            onChange: handleAdminInputChange,\n            sx: {\n              width: '100%',\n              maxWidth: '350px'\n            },\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            name: \"password\",\n            label: \"Password\",\n            type: \"password\",\n            id: \"admin-password\",\n            autoComplete: \"current-password\",\n            value: adminCredentials.password,\n            onChange: handleAdminInputChange,\n            sx: {\n              width: '100%',\n              maxWidth: '350px'\n            },\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"contained\",\n            sx: {\n              mt: 3,\n              mb: 2,\n              width: '100%',\n              maxWidth: '350px',\n              py: 1.5,\n              fontSize: '1rem',\n              fontWeight: 600\n            },\n            disabled: loading,\n            children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 26\n            }, this) : 'Accedi'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: handleBackToMainMenu,\n            sx: {\n              mb: 2,\n              width: '100%',\n              maxWidth: '350px',\n              py: 1.5\n            },\n            children: \"Torna al Menu Principale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this);\n      case 'user':\n        return /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          onSubmit: handleUserLogin,\n          noValidate: true,\n          sx: {\n            maxWidth: '400px',\n            margin: '0 auto',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            sx: {\n              mb: 3,\n              fontWeight: 600\n            },\n            children: \"Login Utente Standard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            id: \"user-username\",\n            label: \"Username\",\n            name: \"username\",\n            autoComplete: \"username\",\n            autoFocus: true,\n            value: userCredentials.username,\n            onChange: handleUserInputChange,\n            sx: {\n              width: '100%',\n              maxWidth: '350px'\n            },\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            name: \"password\",\n            label: \"Password\",\n            type: \"password\",\n            id: \"user-password\",\n            autoComplete: \"current-password\",\n            value: userCredentials.password,\n            onChange: handleUserInputChange,\n            sx: {\n              width: '100%',\n              maxWidth: '350px'\n            },\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"contained\",\n            sx: {\n              mt: 3,\n              mb: 2,\n              width: '100%',\n              maxWidth: '350px',\n              py: 1.5,\n              fontSize: '1rem',\n              fontWeight: 600\n            },\n            disabled: loading,\n            children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 26\n            }, this) : 'Accedi'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: handleBackToMainMenu,\n            sx: {\n              mb: 2,\n              width: '100%',\n              maxWidth: '350px',\n              py: 1.5\n            },\n            children: \"Torna al Menu Principale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this);\n      case 'cantiere':\n        return /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          onSubmit: handleCantiereLogin,\n          noValidate: true,\n          sx: {\n            maxWidth: '400px',\n            margin: '0 auto',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            sx: {\n              mb: 3,\n              fontWeight: 600\n            },\n            children: \"Login Utente Cantiere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            id: \"codice_univoco\",\n            label: \"Codice Univoco del Cantiere\",\n            name: \"codice_univoco\",\n            autoComplete: \"off\",\n            autoFocus: true,\n            value: cantiereCredentials.codice_univoco,\n            onChange: handleCantiereInputChange,\n            sx: {\n              width: '100%',\n              maxWidth: '350px'\n            },\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            name: \"password\",\n            label: \"Password\",\n            type: \"password\",\n            id: \"cantiere-password\",\n            autoComplete: \"current-password\",\n            value: cantiereCredentials.password,\n            onChange: handleCantiereInputChange,\n            sx: {\n              width: '100%',\n              maxWidth: '350px'\n            },\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"contained\",\n            sx: {\n              mt: 3,\n              mb: 2,\n              width: '100%',\n              maxWidth: '350px',\n              py: 1.5,\n              fontSize: '1rem',\n              fontWeight: 600\n            },\n            disabled: loading,\n            children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 26\n            }, this) : 'Accedi'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: handleBackToMainMenu,\n            sx: {\n              mb: 2,\n              width: '100%',\n              maxWidth: '350px',\n              py: 1.5\n            },\n            children: \"Torna al Menu Principale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n\n  // Renderizza il menu principale di login\n  const renderMainMenu = () => {\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        maxWidth: '900px',\n        margin: '0 auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            transition: 'all 0.3s ease',\n            cursor: 'pointer',\n            '&:hover': {\n              transform: 'translateY(-4px)',\n              boxShadow: 4\n            },\n            background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',\n            color: 'white'\n          },\n          onClick: () => setLoginType('admin'),\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              textAlign: 'center',\n              py: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(AdminIcon, {\n              sx: {\n                fontSize: 64,\n                mb: 2,\n                opacity: 0.9\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              gutterBottom: true,\n              fontWeight: \"600\",\n              children: \"Amministratore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                opacity: 0.9,\n                lineHeight: 1.6\n              },\n              children: \"Gestione completa del sistema, utenti e configurazioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            transition: 'all 0.3s ease',\n            cursor: 'pointer',\n            '&:hover': {\n              transform: 'translateY(-4px)',\n              boxShadow: 4\n            },\n            background: 'linear-gradient(135deg, #dc004e 0%, #c51162 100%)',\n            color: 'white'\n          },\n          onClick: () => setLoginType('user'),\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              textAlign: 'center',\n              py: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n              sx: {\n                fontSize: 64,\n                mb: 2,\n                opacity: 0.9\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              gutterBottom: true,\n              fontWeight: \"600\",\n              children: \"Utente Standard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                opacity: 0.9,\n                lineHeight: 1.6\n              },\n              children: \"Gestione cantieri e operazioni quotidiane\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            transition: 'all 0.3s ease',\n            cursor: 'pointer',\n            '&:hover': {\n              transform: 'translateY(-4px)',\n              boxShadow: 4\n            },\n            background: 'linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%)',\n            color: 'white'\n          },\n          onClick: () => setLoginType('cantiere'),\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              textAlign: 'center',\n              py: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(ConstructionIcon, {\n              sx: {\n                fontSize: 64,\n                mb: 2,\n                opacity: 0.9\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              gutterBottom: true,\n              fontWeight: \"600\",\n              children: \"Utente Cantiere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                opacity: 0.9,\n                lineHeight: 1.6\n              },\n              children: \"Accesso diretto alle operazioni di cantiere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            transition: 'all 0.3s ease',\n            cursor: 'pointer',\n            '&:hover': {\n              transform: 'translateY(-4px)',\n              boxShadow: 4\n            },\n            background: 'linear-gradient(135deg, #ed6c02 0%, #e65100 100%)',\n            color: 'white'\n          },\n          onClick: () => setShowPasswordRecoveryDialog(true),\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              textAlign: 'center',\n              py: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(PasswordIcon, {\n              sx: {\n                fontSize: 64,\n                mb: 2,\n                opacity: 0.9\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              gutterBottom: true,\n              fontWeight: \"600\",\n              children: \"Recupero Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                opacity: 0.9,\n                lineHeight: 1.6\n              },\n              children: \"Ripristina l'accesso amministratore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    component: \"main\",\n    maxWidth: \"md\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        marginTop: 8,\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        component: \"h1\",\n        variant: \"h4\",\n        sx: {\n          mb: 4\n        },\n        children: \"Sistema di Gestione Cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 3,\n        sx: {\n          width: '100%',\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          sx: {\n            textAlign: 'center',\n            mb: 3\n          },\n          children: loginType ? 'Login' : 'Seleziona Tipo di Accesso'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 3\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 13\n        }, this), loginType ? renderLoginForm() : renderMainMenu()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showPasswordRecoveryDialog,\n      onClose: () => setShowPasswordRecoveryDialog(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Recupero Password Amministratore\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"Inserisci l'indirizzo email associato all'account amministratore per ricevere le istruzioni per il recupero della password.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"email\",\n          label: \"Indirizzo Email\",\n          type: \"email\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: recoveryEmail,\n          onChange: handleRecoveryEmailChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowPasswordRecoveryDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePasswordRecovery,\n          variant: \"contained\",\n          children: \"Invia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 583,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPageNew, \"FN0PsZOhsEyM6WODOirrdcQ3vPc=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = LoginPageNew;\nexport default LoginPageNew;\nvar _c;\n$RefreshReg$(_c, \"LoginPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Container", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Paper", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Grid", "<PERSON><PERSON>", "CircularProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "AdminPanelSettings", "AdminIcon", "Person", "UserIcon", "Construction", "ConstructionIcon", "Password", "PasswordIcon", "useAuth", "jsxDEV", "_jsxDEV", "LoginPageNew", "_s", "loginType", "setLoginType", "loading", "setLoading", "error", "setError", "showPasswordRecoveryDialog", "setShowPasswordRecoveryDialog", "recoveryEmail", "setRecoveryEmail", "adminCredentials", "setAdminCredentials", "username", "password", "userCredentials", "setUserCredentials", "cantiereCredentials", "setCantiereCredentials", "codice_univoco", "login", "navigate", "handleAdminInputChange", "e", "name", "value", "target", "handleUserInputChange", "handleCantiereInputChange", "handleRecoveryEmailChange", "handleAdminLogin", "preventDefault", "console", "log", "userData", "role", "loginError", "detail", "err", "handleUserLogin", "handleCantiereLogin", "cantiere_id", "localStorage", "setItem", "toString", "cantiere_name", "warn", "setTimeout", "handlePasswordRecovery", "alert", "handleBackToMainMenu", "renderLoginForm", "component", "onSubmit", "noValidate", "sx", "max<PERSON><PERSON><PERSON>", "margin", "display", "flexDirection", "alignItems", "children", "variant", "gutterBottom", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "required", "id", "label", "autoComplete", "autoFocus", "onChange", "width", "type", "mt", "py", "fontSize", "disabled", "size", "onClick", "renderMainMenu", "container", "spacing", "item", "xs", "sm", "md", "height", "transition", "cursor", "transform", "boxShadow", "background", "color", "textAlign", "opacity", "lineHeight", "marginTop", "elevation", "p", "severity", "open", "onClose", "fullWidth", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/LoginPageNew.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Container,\n  Box,\n  Typography,\n  TextField,\n  Button,\n  Paper,\n  Card,\n  CardContent,\n  CardActions,\n  Grid,\n  Alert,\n  CircularProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions\n} from '@mui/material';\nimport {\n  AdminPanelSettings as AdminIcon,\n  Person as UserIcon,\n  Construction as ConstructionIcon,\n  Password as PasswordIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst LoginPageNew = () => {\n  const [loginType, setLoginType] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPasswordRecoveryDialog, setShowPasswordRecoveryDialog] = useState(false);\n  const [recoveryEmail, setRecoveryEmail] = useState('');\n\n  // Credenziali per i diversi tipi di login\n  const [adminCredentials, setAdminCredentials] = useState({\n    username: '',\n    password: ''\n  });\n\n  const [userCredentials, setUserCredentials] = useState({\n    username: '',\n    password: ''\n  });\n\n  const [cantiereCredentials, setCantiereCredentials] = useState({\n    codice_univoco: '',\n    password: ''\n  });\n\n  const { login } = useAuth();\n  const navigate = useNavigate();\n\n  // Gestione dell'input per il login amministratore\n  const handleAdminInputChange = (e) => {\n    const { name, value } = e.target;\n    setAdminCredentials({\n      ...adminCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il login utente standard\n  const handleUserInputChange = (e) => {\n    const { name, value } = e.target;\n    setUserCredentials({\n      ...userCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il login cantiere\n  const handleCantiereInputChange = (e) => {\n    const { name, value } = e.target;\n    setCantiereCredentials({\n      ...cantiereCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il recupero password\n  const handleRecoveryEmailChange = (e) => {\n    setRecoveryEmail(e.target.value);\n  };\n\n  // Gestione del login amministratore\n  const handleAdminLogin = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login amministratore con:', adminCredentials);\n\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!adminCredentials.username || !adminCredentials.password) {\n        console.log('Campi vuoti nel form di login admin');\n        setError('Username e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n\n      console.log('Chiamata alla funzione login per admin...');\n      try {\n        const userData = await login(adminCredentials, 'standard');\n        console.log('Login admin completato, dati utente:', userData);\n\n        // Verifica che l'utente sia un amministratore\n        if (userData.role !== 'owner') {\n          console.log('Utente non ha ruolo owner:', userData.role);\n          setError('Non hai i permessi di amministratore');\n          setLoading(false);\n          return;\n        }\n\n        // Reindirizza alla dashboard di amministrazione\n        console.log('Reindirizzamento a /dashboard/admin');\n        // Utilizza navigate invece di window.location per evitare refresh completo\n        navigate('/dashboard/admin');\n      } catch (loginError) {\n        console.error('Errore specifico durante login admin:', loginError);\n        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');\n        setLoading(false);\n      }\n    } catch (err) {\n      console.error('Errore generale durante login admin:', err);\n      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del login utente standard\n  const handleUserLogin = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login utente standard con:', userCredentials);\n\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!userCredentials.username || !userCredentials.password) {\n        console.log('Campi vuoti nel form di login utente standard');\n        setError('Username e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n\n      console.log('Chiamata alla funzione login per utente standard...');\n      try {\n        const userData = await login(userCredentials, 'standard');\n        console.log('Login utente standard completato, dati utente:', userData);\n\n        // Verifica che l'utente sia un utente standard\n        if (userData.role !== 'user') {\n          console.log('Utente non ha ruolo user:', userData.role);\n          setError('Non hai i permessi di utente standard');\n          setLoading(false);\n          return;\n        }\n\n        // Reindirizza alla dashboard utente\n        console.log('Reindirizzamento a /dashboard/cantieri');\n        // Utilizza navigate invece di window.location per evitare refresh completo\n        navigate('/dashboard/cantieri');\n      } catch (loginError) {\n        console.error('Errore specifico durante login utente standard:', loginError);\n        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');\n        setLoading(false);\n      }\n    } catch (err) {\n      console.error('Errore generale durante login utente standard:', err);\n      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del login cantiere\n  const handleCantiereLogin = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login cantiere con:', cantiereCredentials);\n\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!cantiereCredentials.codice_univoco || !cantiereCredentials.password) {\n        console.log('Campi vuoti nel form di login cantiere');\n        setError('Codice univoco e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n\n      console.log('Chiamata alla funzione login per cantiere...');\n      try {\n        const userData = await login(cantiereCredentials, 'cantiere');\n        console.log('Login cantiere completato, dati utente:', userData);\n\n        // Salva esplicitamente l'ID e il nome del cantiere nel localStorage\n        if (userData.cantiere_id) {\n          console.log('Salvando ID cantiere nel localStorage:', userData.cantiere_id);\n          localStorage.setItem('selectedCantiereId', userData.cantiere_id.toString());\n          localStorage.setItem('selectedCantiereName', userData.cantiere_name || `Cantiere ${userData.cantiere_id}`);\n        } else {\n          console.warn('Risposta login cantiere non contiene cantiere_id:', userData);\n        }\n\n        // Breve ritardo per assicurarsi che i dati siano salvati nel localStorage\n        setTimeout(() => {\n          // Reindirizza alla pagina di visualizzazione cavi\n          console.log('Reindirizzamento a /dashboard/cavi/visualizza');\n          // Utilizza navigate invece di window.location per evitare refresh completo\n          navigate('/dashboard/cavi/visualizza');\n        }, 500);\n      } catch (loginError) {\n        console.error('Errore specifico durante login cantiere:', loginError);\n        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');\n        setLoading(false);\n      }\n    } catch (err) {\n      console.error('Errore generale durante login cantiere:', err);\n      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del recupero password\n  const handlePasswordRecovery = () => {\n    // Qui implementeremo la logica per il recupero della password\n    alert('Funzionalità di recupero password non ancora implementata');\n    setShowPasswordRecoveryDialog(false);\n  };\n\n  // Torna al menu principale\n  const handleBackToMainMenu = () => {\n    setLoginType(null);\n    setError('');\n  };\n\n  // Renderizza il form di login appropriato in base al tipo selezionato\n  const renderLoginForm = () => {\n    switch (loginType) {\n      case 'admin':\n        return (\n          <Box\n            component=\"form\"\n            onSubmit={handleAdminLogin}\n            noValidate\n            sx={{\n              maxWidth: '400px',\n              margin: '0 auto',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center'\n            }}\n          >\n            <Typography variant=\"h5\" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>\n              Login Amministratore\n            </Typography>\n            <TextField\n              margin=\"normal\"\n              required\n              id=\"admin-username\"\n              label=\"Username\"\n              name=\"username\"\n              autoComplete=\"username\"\n              autoFocus\n              value={adminCredentials.username}\n              onChange={handleAdminInputChange}\n              sx={{ width: '100%', maxWidth: '350px' }}\n              variant=\"outlined\"\n            />\n            <TextField\n              margin=\"normal\"\n              required\n              name=\"password\"\n              label=\"Password\"\n              type=\"password\"\n              id=\"admin-password\"\n              autoComplete=\"current-password\"\n              value={adminCredentials.password}\n              onChange={handleAdminInputChange}\n              sx={{ width: '100%', maxWidth: '350px' }}\n              variant=\"outlined\"\n            />\n            <Button\n              type=\"submit\"\n              variant=\"contained\"\n              sx={{\n                mt: 3,\n                mb: 2,\n                width: '100%',\n                maxWidth: '350px',\n                py: 1.5,\n                fontSize: '1rem',\n                fontWeight: 600\n              }}\n              disabled={loading}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Accedi'}\n            </Button>\n            <Button\n              variant=\"outlined\"\n              onClick={handleBackToMainMenu}\n              sx={{\n                mb: 2,\n                width: '100%',\n                maxWidth: '350px',\n                py: 1.5\n              }}\n            >\n              Torna al Menu Principale\n            </Button>\n          </Box>\n        );\n      case 'user':\n        return (\n          <Box\n            component=\"form\"\n            onSubmit={handleUserLogin}\n            noValidate\n            sx={{\n              maxWidth: '400px',\n              margin: '0 auto',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center'\n            }}\n          >\n            <Typography variant=\"h5\" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>\n              Login Utente Standard\n            </Typography>\n            <TextField\n              margin=\"normal\"\n              required\n              id=\"user-username\"\n              label=\"Username\"\n              name=\"username\"\n              autoComplete=\"username\"\n              autoFocus\n              value={userCredentials.username}\n              onChange={handleUserInputChange}\n              sx={{ width: '100%', maxWidth: '350px' }}\n              variant=\"outlined\"\n            />\n            <TextField\n              margin=\"normal\"\n              required\n              name=\"password\"\n              label=\"Password\"\n              type=\"password\"\n              id=\"user-password\"\n              autoComplete=\"current-password\"\n              value={userCredentials.password}\n              onChange={handleUserInputChange}\n              sx={{ width: '100%', maxWidth: '350px' }}\n              variant=\"outlined\"\n            />\n            <Button\n              type=\"submit\"\n              variant=\"contained\"\n              sx={{\n                mt: 3,\n                mb: 2,\n                width: '100%',\n                maxWidth: '350px',\n                py: 1.5,\n                fontSize: '1rem',\n                fontWeight: 600\n              }}\n              disabled={loading}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Accedi'}\n            </Button>\n            <Button\n              variant=\"outlined\"\n              onClick={handleBackToMainMenu}\n              sx={{\n                mb: 2,\n                width: '100%',\n                maxWidth: '350px',\n                py: 1.5\n              }}\n            >\n              Torna al Menu Principale\n            </Button>\n          </Box>\n        );\n      case 'cantiere':\n        return (\n          <Box\n            component=\"form\"\n            onSubmit={handleCantiereLogin}\n            noValidate\n            sx={{\n              maxWidth: '400px',\n              margin: '0 auto',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center'\n            }}\n          >\n            <Typography variant=\"h5\" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>\n              Login Utente Cantiere\n            </Typography>\n            <TextField\n              margin=\"normal\"\n              required\n              id=\"codice_univoco\"\n              label=\"Codice Univoco del Cantiere\"\n              name=\"codice_univoco\"\n              autoComplete=\"off\"\n              autoFocus\n              value={cantiereCredentials.codice_univoco}\n              onChange={handleCantiereInputChange}\n              sx={{ width: '100%', maxWidth: '350px' }}\n              variant=\"outlined\"\n            />\n            <TextField\n              margin=\"normal\"\n              required\n              name=\"password\"\n              label=\"Password\"\n              type=\"password\"\n              id=\"cantiere-password\"\n              autoComplete=\"current-password\"\n              value={cantiereCredentials.password}\n              onChange={handleCantiereInputChange}\n              sx={{ width: '100%', maxWidth: '350px' }}\n              variant=\"outlined\"\n            />\n            <Button\n              type=\"submit\"\n              variant=\"contained\"\n              sx={{\n                mt: 3,\n                mb: 2,\n                width: '100%',\n                maxWidth: '350px',\n                py: 1.5,\n                fontSize: '1rem',\n                fontWeight: 600\n              }}\n              disabled={loading}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Accedi'}\n            </Button>\n            <Button\n              variant=\"outlined\"\n              onClick={handleBackToMainMenu}\n              sx={{\n                mb: 2,\n                width: '100%',\n                maxWidth: '350px',\n                py: 1.5\n              }}\n            >\n              Torna al Menu Principale\n            </Button>\n          </Box>\n        );\n      default:\n        return null;\n    }\n  };\n\n  // Renderizza il menu principale di login\n  const renderMainMenu = () => {\n    return (\n      <Grid container spacing={3} sx={{ maxWidth: '900px', margin: '0 auto' }}>\n        <Grid item xs={12} sm={6} md={6}>\n          <Card\n            sx={{\n              height: '100%',\n              transition: 'all 0.3s ease',\n              cursor: 'pointer',\n              '&:hover': {\n                transform: 'translateY(-4px)',\n                boxShadow: 4\n              },\n              background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',\n              color: 'white'\n            }}\n            onClick={() => setLoginType('admin')}\n          >\n            <CardContent sx={{ textAlign: 'center', py: 4 }}>\n              <AdminIcon sx={{ fontSize: 64, mb: 2, opacity: 0.9 }} />\n              <Typography variant=\"h5\" component=\"div\" gutterBottom fontWeight=\"600\">\n                Amministratore\n              </Typography>\n              <Typography variant=\"body2\" sx={{ opacity: 0.9, lineHeight: 1.6 }}>\n                Gestione completa del sistema, utenti e configurazioni\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={6}>\n          <Card\n            sx={{\n              height: '100%',\n              transition: 'all 0.3s ease',\n              cursor: 'pointer',\n              '&:hover': {\n                transform: 'translateY(-4px)',\n                boxShadow: 4\n              },\n              background: 'linear-gradient(135deg, #dc004e 0%, #c51162 100%)',\n              color: 'white'\n            }}\n            onClick={() => setLoginType('user')}\n          >\n            <CardContent sx={{ textAlign: 'center', py: 4 }}>\n              <UserIcon sx={{ fontSize: 64, mb: 2, opacity: 0.9 }} />\n              <Typography variant=\"h5\" component=\"div\" gutterBottom fontWeight=\"600\">\n                Utente Standard\n              </Typography>\n              <Typography variant=\"body2\" sx={{ opacity: 0.9, lineHeight: 1.6 }}>\n                Gestione cantieri e operazioni quotidiane\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={6}>\n          <Card\n            sx={{\n              height: '100%',\n              transition: 'all 0.3s ease',\n              cursor: 'pointer',\n              '&:hover': {\n                transform: 'translateY(-4px)',\n                boxShadow: 4\n              },\n              background: 'linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%)',\n              color: 'white'\n            }}\n            onClick={() => setLoginType('cantiere')}\n          >\n            <CardContent sx={{ textAlign: 'center', py: 4 }}>\n              <ConstructionIcon sx={{ fontSize: 64, mb: 2, opacity: 0.9 }} />\n              <Typography variant=\"h5\" component=\"div\" gutterBottom fontWeight=\"600\">\n                Utente Cantiere\n              </Typography>\n              <Typography variant=\"body2\" sx={{ opacity: 0.9, lineHeight: 1.6 }}>\n                Accesso diretto alle operazioni di cantiere\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={6}>\n          <Card\n            sx={{\n              height: '100%',\n              transition: 'all 0.3s ease',\n              cursor: 'pointer',\n              '&:hover': {\n                transform: 'translateY(-4px)',\n                boxShadow: 4\n              },\n              background: 'linear-gradient(135deg, #ed6c02 0%, #e65100 100%)',\n              color: 'white'\n            }}\n            onClick={() => setShowPasswordRecoveryDialog(true)}\n          >\n            <CardContent sx={{ textAlign: 'center', py: 4 }}>\n              <PasswordIcon sx={{ fontSize: 64, mb: 2, opacity: 0.9 }} />\n              <Typography variant=\"h5\" component=\"div\" gutterBottom fontWeight=\"600\">\n                Recupero Password\n              </Typography>\n              <Typography variant=\"body2\" sx={{ opacity: 0.9, lineHeight: 1.6 }}>\n                Ripristina l'accesso amministratore\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    );\n  };\n\n  return (\n    <Container component=\"main\" maxWidth=\"md\">\n      <Box\n        sx={{\n          marginTop: 8,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n        }}\n      >\n        <Typography component=\"h1\" variant=\"h4\" sx={{ mb: 4 }}>\n          Sistema di Gestione Cantieri\n        </Typography>\n\n        <Paper elevation={3} sx={{ width: '100%', p: 3 }}>\n          <Typography variant=\"h5\" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>\n            {loginType ? 'Login' : 'Seleziona Tipo di Accesso'}\n          </Typography>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 3 }}>\n              {error}\n            </Alert>\n          )}\n\n          {loginType ? renderLoginForm() : renderMainMenu()}\n        </Paper>\n      </Box>\n\n      {/* Dialog per il recupero password */}\n      <Dialog open={showPasswordRecoveryDialog} onClose={() => setShowPasswordRecoveryDialog(false)}>\n        <DialogTitle>Recupero Password Amministratore</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Inserisci l'indirizzo email associato all'account amministratore per ricevere le istruzioni per il recupero della password.\n          </DialogContentText>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"email\"\n            label=\"Indirizzo Email\"\n            type=\"email\"\n            fullWidth\n            variant=\"outlined\"\n            value={recoveryEmail}\n            onChange={handleRecoveryEmailChange}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowPasswordRecoveryDialog(false)}>Annulla</Button>\n          <Button onClick={handlePasswordRecovery} variant=\"contained\">Invia</Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default LoginPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,QACR,eAAe;AACtB,SACEC,kBAAkB,IAAIC,SAAS,EAC/BC,MAAM,IAAIC,QAAQ,EAClBC,YAAY,IAAIC,gBAAgB,EAChCC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC;IACvD4C,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC;IACrD4C,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjD,QAAQ,CAAC;IAC7DkD,cAAc,EAAE,EAAE;IAClBL,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM;IAAEM;EAAM,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAC3B,MAAMyB,QAAQ,GAAGnD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMoD,sBAAsB,GAAIC,CAAC,IAAK;IACpC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCd,mBAAmB,CAAC;MAClB,GAAGD,gBAAgB;MACnB,CAACa,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAIJ,CAAC,IAAK;IACnC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCV,kBAAkB,CAAC;MACjB,GAAGD,eAAe;MAClB,CAACS,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMG,yBAAyB,GAAIL,CAAC,IAAK;IACvC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCR,sBAAsB,CAAC;MACrB,GAAGD,mBAAmB;MACtB,CAACO,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMI,yBAAyB,GAAIN,CAAC,IAAK;IACvCb,gBAAgB,CAACa,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC;EAClC,CAAC;;EAED;EACA,MAAMK,gBAAgB,GAAG,MAAOP,CAAC,IAAK;IACpCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB3B,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ0B,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEtB,gBAAgB,CAAC;IAEvE,IAAI;MACF;MACA,IAAI,CAACA,gBAAgB,CAACE,QAAQ,IAAI,CAACF,gBAAgB,CAACG,QAAQ,EAAE;QAC5DkB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD3B,QAAQ,CAAC,8CAA8C,CAAC;QACxDF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA4B,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMd,KAAK,CAACT,gBAAgB,EAAE,UAAU,CAAC;QAC1DqB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEC,QAAQ,CAAC;;QAE7D;QACA,IAAIA,QAAQ,CAACC,IAAI,KAAK,OAAO,EAAE;UAC7BH,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,QAAQ,CAACC,IAAI,CAAC;UACxD7B,QAAQ,CAAC,sCAAsC,CAAC;UAChDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA4B,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD;QACAZ,QAAQ,CAAC,kBAAkB,CAAC;MAC9B,CAAC,CAAC,OAAOe,UAAU,EAAE;QACnBJ,OAAO,CAAC3B,KAAK,CAAC,uCAAuC,EAAE+B,UAAU,CAAC;QAClE9B,QAAQ,CAAC8B,UAAU,CAACC,MAAM,IAAI,mDAAmD,CAAC;QAClFjC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,OAAOkC,GAAG,EAAE;MACZN,OAAO,CAAC3B,KAAK,CAAC,sCAAsC,EAAEiC,GAAG,CAAC;MAC1DhC,QAAQ,CAACgC,GAAG,CAACD,MAAM,IAAI,wDAAwD,CAAC;MAChFjC,UAAU,CAAC,KAAK,CAAC;IACnB;IACA;EACF,CAAC;;EAED;EACA,MAAMmC,eAAe,GAAG,MAAOhB,CAAC,IAAK;IACnCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB3B,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ0B,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAElB,eAAe,CAAC;IAEvE,IAAI;MACF;MACA,IAAI,CAACA,eAAe,CAACF,QAAQ,IAAI,CAACE,eAAe,CAACD,QAAQ,EAAE;QAC1DkB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D3B,QAAQ,CAAC,8CAA8C,CAAC;QACxDF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA4B,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMd,KAAK,CAACL,eAAe,EAAE,UAAU,CAAC;QACzDiB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEC,QAAQ,CAAC;;QAEvE;QACA,IAAIA,QAAQ,CAACC,IAAI,KAAK,MAAM,EAAE;UAC5BH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,QAAQ,CAACC,IAAI,CAAC;UACvD7B,QAAQ,CAAC,uCAAuC,CAAC;UACjDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA4B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD;QACAZ,QAAQ,CAAC,qBAAqB,CAAC;MACjC,CAAC,CAAC,OAAOe,UAAU,EAAE;QACnBJ,OAAO,CAAC3B,KAAK,CAAC,iDAAiD,EAAE+B,UAAU,CAAC;QAC5E9B,QAAQ,CAAC8B,UAAU,CAACC,MAAM,IAAI,mDAAmD,CAAC;QAClFjC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,OAAOkC,GAAG,EAAE;MACZN,OAAO,CAAC3B,KAAK,CAAC,gDAAgD,EAAEiC,GAAG,CAAC;MACpEhC,QAAQ,CAACgC,GAAG,CAACD,MAAM,IAAI,wDAAwD,CAAC;MAChFjC,UAAU,CAAC,KAAK,CAAC;IACnB;IACA;EACF,CAAC;;EAED;EACA,MAAMoC,mBAAmB,GAAG,MAAOjB,CAAC,IAAK;IACvCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB3B,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ0B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEhB,mBAAmB,CAAC;IAEpE,IAAI;MACF;MACA,IAAI,CAACA,mBAAmB,CAACE,cAAc,IAAI,CAACF,mBAAmB,CAACH,QAAQ,EAAE;QACxEkB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD3B,QAAQ,CAAC,oDAAoD,CAAC;QAC9DF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA4B,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMd,KAAK,CAACH,mBAAmB,EAAE,UAAU,CAAC;QAC7De,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,QAAQ,CAAC;;QAEhE;QACA,IAAIA,QAAQ,CAACO,WAAW,EAAE;UACxBT,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,QAAQ,CAACO,WAAW,CAAC;UAC3EC,YAAY,CAACC,OAAO,CAAC,oBAAoB,EAAET,QAAQ,CAACO,WAAW,CAACG,QAAQ,CAAC,CAAC,CAAC;UAC3EF,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAET,QAAQ,CAACW,aAAa,IAAI,YAAYX,QAAQ,CAACO,WAAW,EAAE,CAAC;QAC5G,CAAC,MAAM;UACLT,OAAO,CAACc,IAAI,CAAC,mDAAmD,EAAEZ,QAAQ,CAAC;QAC7E;;QAEA;QACAa,UAAU,CAAC,MAAM;UACf;UACAf,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D;UACAZ,QAAQ,CAAC,4BAA4B,CAAC;QACxC,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,CAAC,OAAOe,UAAU,EAAE;QACnBJ,OAAO,CAAC3B,KAAK,CAAC,0CAA0C,EAAE+B,UAAU,CAAC;QACrE9B,QAAQ,CAAC8B,UAAU,CAACC,MAAM,IAAI,mDAAmD,CAAC;QAClFjC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,OAAOkC,GAAG,EAAE;MACZN,OAAO,CAAC3B,KAAK,CAAC,yCAAyC,EAAEiC,GAAG,CAAC;MAC7DhC,QAAQ,CAACgC,GAAG,CAACD,MAAM,IAAI,wDAAwD,CAAC;MAChFjC,UAAU,CAAC,KAAK,CAAC;IACnB;IACA;EACF,CAAC;;EAED;EACA,MAAM4C,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,KAAK,CAAC,2DAA2D,CAAC;IAClEzC,6BAA6B,CAAC,KAAK,CAAC;EACtC,CAAC;;EAED;EACA,MAAM0C,oBAAoB,GAAGA,CAAA,KAAM;IACjChD,YAAY,CAAC,IAAI,CAAC;IAClBI,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;;EAED;EACA,MAAM6C,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQlD,SAAS;MACf,KAAK,OAAO;QACV,oBACEH,OAAA,CAAC1B,GAAG;UACFgF,SAAS,EAAC,MAAM;UAChBC,QAAQ,EAAEvB,gBAAiB;UAC3BwB,UAAU;UACVC,EAAE,EAAE;YACFC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE,QAAQ;YAChBC,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE;UACd,CAAE;UAAAC,QAAA,gBAEF/D,OAAA,CAACzB,UAAU;YAACyF,OAAO,EAAC,IAAI;YAACC,YAAY;YAACR,EAAE,EAAE;cAAES,EAAE,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAI,CAAE;YAAAJ,QAAA,EAAC;UAEtE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvE,OAAA,CAACxB,SAAS;YACRmF,MAAM,EAAC,QAAQ;YACfa,QAAQ;YACRC,EAAE,EAAC,gBAAgB;YACnBC,KAAK,EAAC,UAAU;YAChBhD,IAAI,EAAC,UAAU;YACfiD,YAAY,EAAC,UAAU;YACvBC,SAAS;YACTjD,KAAK,EAAEd,gBAAgB,CAACE,QAAS;YACjC8D,QAAQ,EAAErD,sBAAuB;YACjCiC,EAAE,EAAE;cAAEqB,KAAK,EAAE,MAAM;cAAEpB,QAAQ,EAAE;YAAQ,CAAE;YACzCM,OAAO,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACFvE,OAAA,CAACxB,SAAS;YACRmF,MAAM,EAAC,QAAQ;YACfa,QAAQ;YACR9C,IAAI,EAAC,UAAU;YACfgD,KAAK,EAAC,UAAU;YAChBK,IAAI,EAAC,UAAU;YACfN,EAAE,EAAC,gBAAgB;YACnBE,YAAY,EAAC,kBAAkB;YAC/BhD,KAAK,EAAEd,gBAAgB,CAACG,QAAS;YACjC6D,QAAQ,EAAErD,sBAAuB;YACjCiC,EAAE,EAAE;cAAEqB,KAAK,EAAE,MAAM;cAAEpB,QAAQ,EAAE;YAAQ,CAAE;YACzCM,OAAO,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACFvE,OAAA,CAACvB,MAAM;YACLsG,IAAI,EAAC,QAAQ;YACbf,OAAO,EAAC,WAAW;YACnBP,EAAE,EAAE;cACFuB,EAAE,EAAE,CAAC;cACLd,EAAE,EAAE,CAAC;cACLY,KAAK,EAAE,MAAM;cACbpB,QAAQ,EAAE,OAAO;cACjBuB,EAAE,EAAE,GAAG;cACPC,QAAQ,EAAE,MAAM;cAChBf,UAAU,EAAE;YACd,CAAE;YACFgB,QAAQ,EAAE9E,OAAQ;YAAA0D,QAAA,EAEjB1D,OAAO,gBAAGL,OAAA,CAAChB,gBAAgB;cAACoG,IAAI,EAAE;YAAG;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTvE,OAAA,CAACvB,MAAM;YACLuF,OAAO,EAAC,UAAU;YAClBqB,OAAO,EAAEjC,oBAAqB;YAC9BK,EAAE,EAAE;cACFS,EAAE,EAAE,CAAC;cACLY,KAAK,EAAE,MAAM;cACbpB,QAAQ,EAAE,OAAO;cACjBuB,EAAE,EAAE;YACN,CAAE;YAAAlB,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEV,KAAK,MAAM;QACT,oBACEvE,OAAA,CAAC1B,GAAG;UACFgF,SAAS,EAAC,MAAM;UAChBC,QAAQ,EAAEd,eAAgB;UAC1Be,UAAU;UACVC,EAAE,EAAE;YACFC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE,QAAQ;YAChBC,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE;UACd,CAAE;UAAAC,QAAA,gBAEF/D,OAAA,CAACzB,UAAU;YAACyF,OAAO,EAAC,IAAI;YAACC,YAAY;YAACR,EAAE,EAAE;cAAES,EAAE,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAI,CAAE;YAAAJ,QAAA,EAAC;UAEtE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvE,OAAA,CAACxB,SAAS;YACRmF,MAAM,EAAC,QAAQ;YACfa,QAAQ;YACRC,EAAE,EAAC,eAAe;YAClBC,KAAK,EAAC,UAAU;YAChBhD,IAAI,EAAC,UAAU;YACfiD,YAAY,EAAC,UAAU;YACvBC,SAAS;YACTjD,KAAK,EAAEV,eAAe,CAACF,QAAS;YAChC8D,QAAQ,EAAEhD,qBAAsB;YAChC4B,EAAE,EAAE;cAAEqB,KAAK,EAAE,MAAM;cAAEpB,QAAQ,EAAE;YAAQ,CAAE;YACzCM,OAAO,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACFvE,OAAA,CAACxB,SAAS;YACRmF,MAAM,EAAC,QAAQ;YACfa,QAAQ;YACR9C,IAAI,EAAC,UAAU;YACfgD,KAAK,EAAC,UAAU;YAChBK,IAAI,EAAC,UAAU;YACfN,EAAE,EAAC,eAAe;YAClBE,YAAY,EAAC,kBAAkB;YAC/BhD,KAAK,EAAEV,eAAe,CAACD,QAAS;YAChC6D,QAAQ,EAAEhD,qBAAsB;YAChC4B,EAAE,EAAE;cAAEqB,KAAK,EAAE,MAAM;cAAEpB,QAAQ,EAAE;YAAQ,CAAE;YACzCM,OAAO,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACFvE,OAAA,CAACvB,MAAM;YACLsG,IAAI,EAAC,QAAQ;YACbf,OAAO,EAAC,WAAW;YACnBP,EAAE,EAAE;cACFuB,EAAE,EAAE,CAAC;cACLd,EAAE,EAAE,CAAC;cACLY,KAAK,EAAE,MAAM;cACbpB,QAAQ,EAAE,OAAO;cACjBuB,EAAE,EAAE,GAAG;cACPC,QAAQ,EAAE,MAAM;cAChBf,UAAU,EAAE;YACd,CAAE;YACFgB,QAAQ,EAAE9E,OAAQ;YAAA0D,QAAA,EAEjB1D,OAAO,gBAAGL,OAAA,CAAChB,gBAAgB;cAACoG,IAAI,EAAE;YAAG;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTvE,OAAA,CAACvB,MAAM;YACLuF,OAAO,EAAC,UAAU;YAClBqB,OAAO,EAAEjC,oBAAqB;YAC9BK,EAAE,EAAE;cACFS,EAAE,EAAE,CAAC;cACLY,KAAK,EAAE,MAAM;cACbpB,QAAQ,EAAE,OAAO;cACjBuB,EAAE,EAAE;YACN,CAAE;YAAAlB,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEV,KAAK,UAAU;QACb,oBACEvE,OAAA,CAAC1B,GAAG;UACFgF,SAAS,EAAC,MAAM;UAChBC,QAAQ,EAAEb,mBAAoB;UAC9Bc,UAAU;UACVC,EAAE,EAAE;YACFC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE,QAAQ;YAChBC,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE;UACd,CAAE;UAAAC,QAAA,gBAEF/D,OAAA,CAACzB,UAAU;YAACyF,OAAO,EAAC,IAAI;YAACC,YAAY;YAACR,EAAE,EAAE;cAAES,EAAE,EAAE,CAAC;cAAEC,UAAU,EAAE;YAAI,CAAE;YAAAJ,QAAA,EAAC;UAEtE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvE,OAAA,CAACxB,SAAS;YACRmF,MAAM,EAAC,QAAQ;YACfa,QAAQ;YACRC,EAAE,EAAC,gBAAgB;YACnBC,KAAK,EAAC,6BAA6B;YACnChD,IAAI,EAAC,gBAAgB;YACrBiD,YAAY,EAAC,KAAK;YAClBC,SAAS;YACTjD,KAAK,EAAER,mBAAmB,CAACE,cAAe;YAC1CwD,QAAQ,EAAE/C,yBAA0B;YACpC2B,EAAE,EAAE;cAAEqB,KAAK,EAAE,MAAM;cAAEpB,QAAQ,EAAE;YAAQ,CAAE;YACzCM,OAAO,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACFvE,OAAA,CAACxB,SAAS;YACRmF,MAAM,EAAC,QAAQ;YACfa,QAAQ;YACR9C,IAAI,EAAC,UAAU;YACfgD,KAAK,EAAC,UAAU;YAChBK,IAAI,EAAC,UAAU;YACfN,EAAE,EAAC,mBAAmB;YACtBE,YAAY,EAAC,kBAAkB;YAC/BhD,KAAK,EAAER,mBAAmB,CAACH,QAAS;YACpC6D,QAAQ,EAAE/C,yBAA0B;YACpC2B,EAAE,EAAE;cAAEqB,KAAK,EAAE,MAAM;cAAEpB,QAAQ,EAAE;YAAQ,CAAE;YACzCM,OAAO,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACFvE,OAAA,CAACvB,MAAM;YACLsG,IAAI,EAAC,QAAQ;YACbf,OAAO,EAAC,WAAW;YACnBP,EAAE,EAAE;cACFuB,EAAE,EAAE,CAAC;cACLd,EAAE,EAAE,CAAC;cACLY,KAAK,EAAE,MAAM;cACbpB,QAAQ,EAAE,OAAO;cACjBuB,EAAE,EAAE,GAAG;cACPC,QAAQ,EAAE,MAAM;cAChBf,UAAU,EAAE;YACd,CAAE;YACFgB,QAAQ,EAAE9E,OAAQ;YAAA0D,QAAA,EAEjB1D,OAAO,gBAAGL,OAAA,CAAChB,gBAAgB;cAACoG,IAAI,EAAE;YAAG;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTvE,OAAA,CAACvB,MAAM;YACLuF,OAAO,EAAC,UAAU;YAClBqB,OAAO,EAAEjC,oBAAqB;YAC9BK,EAAE,EAAE;cACFS,EAAE,EAAE,CAAC;cACLY,KAAK,EAAE,MAAM;cACbpB,QAAQ,EAAE,OAAO;cACjBuB,EAAE,EAAE;YACN,CAAE;YAAAlB,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEV;QACE,OAAO,IAAI;IACf;EACF,CAAC;;EAED;EACA,MAAMe,cAAc,GAAGA,CAAA,KAAM;IAC3B,oBACEtF,OAAA,CAAClB,IAAI;MAACyG,SAAS;MAACC,OAAO,EAAE,CAAE;MAAC/B,EAAE,EAAE;QAAEC,QAAQ,EAAE,OAAO;QAAEC,MAAM,EAAE;MAAS,CAAE;MAAAI,QAAA,gBACtE/D,OAAA,CAAClB,IAAI;QAAC2G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9B/D,OAAA,CAACrB,IAAI;UACH8E,EAAE,EAAE;YACFoC,MAAM,EAAE,MAAM;YACdC,UAAU,EAAE,eAAe;YAC3BC,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb,CAAC;YACDC,UAAU,EAAE,mDAAmD;YAC/DC,KAAK,EAAE;UACT,CAAE;UACFd,OAAO,EAAEA,CAAA,KAAMjF,YAAY,CAAC,OAAO,CAAE;UAAA2D,QAAA,eAErC/D,OAAA,CAACpB,WAAW;YAAC6E,EAAE,EAAE;cAAE2C,SAAS,EAAE,QAAQ;cAAEnB,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,gBAC9C/D,OAAA,CAACT,SAAS;cAACkE,EAAE,EAAE;gBAAEyB,QAAQ,EAAE,EAAE;gBAAEhB,EAAE,EAAE,CAAC;gBAAEmC,OAAO,EAAE;cAAI;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDvE,OAAA,CAACzB,UAAU;cAACyF,OAAO,EAAC,IAAI;cAACV,SAAS,EAAC,KAAK;cAACW,YAAY;cAACE,UAAU,EAAC,KAAK;cAAAJ,QAAA,EAAC;YAEvE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvE,OAAA,CAACzB,UAAU;cAACyF,OAAO,EAAC,OAAO;cAACP,EAAE,EAAE;gBAAE4C,OAAO,EAAE,GAAG;gBAAEC,UAAU,EAAE;cAAI,CAAE;cAAAvC,QAAA,EAAC;YAEnE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPvE,OAAA,CAAClB,IAAI;QAAC2G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9B/D,OAAA,CAACrB,IAAI;UACH8E,EAAE,EAAE;YACFoC,MAAM,EAAE,MAAM;YACdC,UAAU,EAAE,eAAe;YAC3BC,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb,CAAC;YACDC,UAAU,EAAE,mDAAmD;YAC/DC,KAAK,EAAE;UACT,CAAE;UACFd,OAAO,EAAEA,CAAA,KAAMjF,YAAY,CAAC,MAAM,CAAE;UAAA2D,QAAA,eAEpC/D,OAAA,CAACpB,WAAW;YAAC6E,EAAE,EAAE;cAAE2C,SAAS,EAAE,QAAQ;cAAEnB,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,gBAC9C/D,OAAA,CAACP,QAAQ;cAACgE,EAAE,EAAE;gBAAEyB,QAAQ,EAAE,EAAE;gBAAEhB,EAAE,EAAE,CAAC;gBAAEmC,OAAO,EAAE;cAAI;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDvE,OAAA,CAACzB,UAAU;cAACyF,OAAO,EAAC,IAAI;cAACV,SAAS,EAAC,KAAK;cAACW,YAAY;cAACE,UAAU,EAAC,KAAK;cAAAJ,QAAA,EAAC;YAEvE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvE,OAAA,CAACzB,UAAU;cAACyF,OAAO,EAAC,OAAO;cAACP,EAAE,EAAE;gBAAE4C,OAAO,EAAE,GAAG;gBAAEC,UAAU,EAAE;cAAI,CAAE;cAAAvC,QAAA,EAAC;YAEnE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPvE,OAAA,CAAClB,IAAI;QAAC2G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9B/D,OAAA,CAACrB,IAAI;UACH8E,EAAE,EAAE;YACFoC,MAAM,EAAE,MAAM;YACdC,UAAU,EAAE,eAAe;YAC3BC,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb,CAAC;YACDC,UAAU,EAAE,mDAAmD;YAC/DC,KAAK,EAAE;UACT,CAAE;UACFd,OAAO,EAAEA,CAAA,KAAMjF,YAAY,CAAC,UAAU,CAAE;UAAA2D,QAAA,eAExC/D,OAAA,CAACpB,WAAW;YAAC6E,EAAE,EAAE;cAAE2C,SAAS,EAAE,QAAQ;cAAEnB,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,gBAC9C/D,OAAA,CAACL,gBAAgB;cAAC8D,EAAE,EAAE;gBAAEyB,QAAQ,EAAE,EAAE;gBAAEhB,EAAE,EAAE,CAAC;gBAAEmC,OAAO,EAAE;cAAI;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DvE,OAAA,CAACzB,UAAU;cAACyF,OAAO,EAAC,IAAI;cAACV,SAAS,EAAC,KAAK;cAACW,YAAY;cAACE,UAAU,EAAC,KAAK;cAAAJ,QAAA,EAAC;YAEvE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvE,OAAA,CAACzB,UAAU;cAACyF,OAAO,EAAC,OAAO;cAACP,EAAE,EAAE;gBAAE4C,OAAO,EAAE,GAAG;gBAAEC,UAAU,EAAE;cAAI,CAAE;cAAAvC,QAAA,EAAC;YAEnE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPvE,OAAA,CAAClB,IAAI;QAAC2G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9B/D,OAAA,CAACrB,IAAI;UACH8E,EAAE,EAAE;YACFoC,MAAM,EAAE,MAAM;YACdC,UAAU,EAAE,eAAe;YAC3BC,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb,CAAC;YACDC,UAAU,EAAE,mDAAmD;YAC/DC,KAAK,EAAE;UACT,CAAE;UACFd,OAAO,EAAEA,CAAA,KAAM3E,6BAA6B,CAAC,IAAI,CAAE;UAAAqD,QAAA,eAEnD/D,OAAA,CAACpB,WAAW;YAAC6E,EAAE,EAAE;cAAE2C,SAAS,EAAE,QAAQ;cAAEnB,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,gBAC9C/D,OAAA,CAACH,YAAY;cAAC4D,EAAE,EAAE;gBAAEyB,QAAQ,EAAE,EAAE;gBAAEhB,EAAE,EAAE,CAAC;gBAAEmC,OAAO,EAAE;cAAI;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DvE,OAAA,CAACzB,UAAU;cAACyF,OAAO,EAAC,IAAI;cAACV,SAAS,EAAC,KAAK;cAACW,YAAY;cAACE,UAAU,EAAC,KAAK;cAAAJ,QAAA,EAAC;YAEvE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvE,OAAA,CAACzB,UAAU;cAACyF,OAAO,EAAC,OAAO;cAACP,EAAE,EAAE;gBAAE4C,OAAO,EAAE,GAAG;gBAAEC,UAAU,EAAE;cAAI,CAAE;cAAAvC,QAAA,EAAC;YAEnE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEX,CAAC;EAED,oBACEvE,OAAA,CAAC3B,SAAS;IAACiF,SAAS,EAAC,MAAM;IAACI,QAAQ,EAAC,IAAI;IAAAK,QAAA,gBACvC/D,OAAA,CAAC1B,GAAG;MACFmF,EAAE,EAAE;QACF8C,SAAS,EAAE,CAAC;QACZ3C,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE;MACd,CAAE;MAAAC,QAAA,gBAEF/D,OAAA,CAACzB,UAAU;QAAC+E,SAAS,EAAC,IAAI;QAACU,OAAO,EAAC,IAAI;QAACP,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAEvD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbvE,OAAA,CAACtB,KAAK;QAAC8H,SAAS,EAAE,CAAE;QAAC/C,EAAE,EAAE;UAAEqB,KAAK,EAAE,MAAM;UAAE2B,CAAC,EAAE;QAAE,CAAE;QAAA1C,QAAA,gBAC/C/D,OAAA,CAACzB,UAAU;UAACyF,OAAO,EAAC,IAAI;UAACC,YAAY;UAACR,EAAE,EAAE;YAAE2C,SAAS,EAAE,QAAQ;YAAElC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EACtE5D,SAAS,GAAG,OAAO,GAAG;QAA2B;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,EAEZhE,KAAK,iBACJP,OAAA,CAACjB,KAAK;UAAC2H,QAAQ,EAAC,OAAO;UAACjD,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EACnCxD;QAAK;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEApE,SAAS,GAAGkD,eAAe,CAAC,CAAC,GAAGiC,cAAc,CAAC,CAAC;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNvE,OAAA,CAACf,MAAM;MAAC0H,IAAI,EAAElG,0BAA2B;MAACmG,OAAO,EAAEA,CAAA,KAAMlG,6BAA6B,CAAC,KAAK,CAAE;MAAAqD,QAAA,gBAC5F/D,OAAA,CAACd,WAAW;QAAA6E,QAAA,EAAC;MAAgC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3DvE,OAAA,CAACb,aAAa;QAAA4E,QAAA,gBACZ/D,OAAA,CAACZ,iBAAiB;UAAA2E,QAAA,EAAC;QAEnB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBvE,OAAA,CAACxB,SAAS;UACRoG,SAAS;UACTjB,MAAM,EAAC,OAAO;UACdc,EAAE,EAAC,OAAO;UACVC,KAAK,EAAC,iBAAiB;UACvBK,IAAI,EAAC,OAAO;UACZ8B,SAAS;UACT7C,OAAO,EAAC,UAAU;UAClBrC,KAAK,EAAEhB,aAAc;UACrBkE,QAAQ,EAAE9C;QAA0B;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBvE,OAAA,CAACX,aAAa;QAAA0E,QAAA,gBACZ/D,OAAA,CAACvB,MAAM;UAAC4G,OAAO,EAAEA,CAAA,KAAM3E,6BAA6B,CAAC,KAAK,CAAE;UAAAqD,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7EvE,OAAA,CAACvB,MAAM;UAAC4G,OAAO,EAAEnC,sBAAuB;UAACc,OAAO,EAAC,WAAW;UAAAD,QAAA,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAACrE,EAAA,CA/lBID,YAAY;EAAA,QAuBEH,OAAO,EACR1B,WAAW;AAAA;AAAA0I,EAAA,GAxBxB7G,YAAY;AAimBlB,eAAeA,YAAY;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}