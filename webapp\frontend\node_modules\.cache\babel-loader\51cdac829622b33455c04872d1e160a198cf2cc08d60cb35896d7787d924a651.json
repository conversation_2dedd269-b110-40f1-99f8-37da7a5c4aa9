{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"f.Kr.\", \"e.Kr.\"],\n  abbreviated: [\"f.Kr.\", \"e.Kr.\"],\n  wide: [\"fyrir <PERSON>\", \"eftir <PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1F\", \"2F\", \"3F\", \"4F\"],\n  wide: [\"1. fjórðungur\", \"2. fjórðungur\", \"3. fjórðungur\", \"4. fjórðungur\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"Á\", \"S\", \"Ó\", \"N\", \"D\"],\n  abbreviated: [\"jan.\", \"feb.\", \"mars\", \"apríl\", \"maí\", \"jún<PERSON>\", \"júl<PERSON>\", \"ágúst\", \"sept.\", \"okt.\", \"nóv.\", \"des.\"],\n  wide: [\"jan<PERSON>ar\", \"febrúar\", \"mars\", \"apríl\", \"maí\", \"j<PERSON><PERSON>\", \"júlí\", \"ágúst\", \"september\", \"október\", \"nóvember\", \"desember\"]\n};\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"Þ\", \"M\", \"F\", \"F\", \"L\"],\n  short: [\"Su\", \"Má\", \"Þr\", \"Mi\", \"Fi\", \"Fö\", \"La\"],\n  abbreviated: [\"sun.\", \"mán.\", \"þri.\", \"mið.\", \"fim.\", \"fös.\", \"lau.\"],\n  wide: [\"sunnudagur\", \"mánudagur\", \"þriðjudagur\", \"miðvikudagur\", \"fimmtudagur\", \"föstudagur\", \"laugardagur\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"f\",\n    pm: \"e\",\n    midnight: \"miðnætti\",\n    noon: \"hádegi\",\n    morning: \"morgunn\",\n    afternoon: \"síðdegi\",\n    evening: \"kvöld\",\n    night: \"nótt\"\n  },\n  abbreviated: {\n    am: \"f.h.\",\n    pm: \"e.h.\",\n    midnight: \"miðnætti\",\n    noon: \"hádegi\",\n    morning: \"morgunn\",\n    afternoon: \"síðdegi\",\n    evening: \"kvöld\",\n    night: \"nótt\"\n  },\n  wide: {\n    am: \"fyrir hádegi\",\n    pm: \"eftir hádegi\",\n    midnight: \"miðnætti\",\n    noon: \"hádegi\",\n    morning: \"morgunn\",\n    afternoon: \"síðdegi\",\n    evening: \"kvöld\",\n    night: \"nótt\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"f\",\n    pm: \"e\",\n    midnight: \"á miðnætti\",\n    noon: \"á hádegi\",\n    morning: \"að morgni\",\n    afternoon: \"síðdegis\",\n    evening: \"um kvöld\",\n    night: \"um nótt\"\n  },\n  abbreviated: {\n    am: \"f.h.\",\n    pm: \"e.h.\",\n    midnight: \"á miðnætti\",\n    noon: \"á hádegi\",\n    morning: \"að morgni\",\n    afternoon: \"síðdegis\",\n    evening: \"um kvöld\",\n    night: \"um nótt\"\n  },\n  wide: {\n    am: \"fyrir hádegi\",\n    pm: \"eftir hádegi\",\n    midnight: \"á miðnætti\",\n    noon: \"á hádegi\",\n    morning: \"að morgni\",\n    afternoon: \"síðdegis\",\n    evening: \"um kvöld\",\n    night: \"um nótt\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/is/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"f.Kr.\", \"e.Kr.\"],\n  abbreviated: [\"f.Kr.\", \"e.Kr.\"],\n  wide: [\"fyrir <PERSON>\", \"eftir <PERSON>\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1F\", \"2F\", \"3F\", \"4F\"],\n  wide: [\"1. fjórðungur\", \"2. fjórðungur\", \"3. fjórðungur\", \"4. fjórðungur\"],\n};\n\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"Á\", \"S\", \"Ó\", \"N\", \"D\"],\n  abbreviated: [\n    \"jan.\",\n    \"feb.\",\n    \"mars\",\n    \"apríl\",\n    \"maí\",\n    \"jún<PERSON>\",\n    \"júl<PERSON>\",\n    \"ágúst\",\n    \"sept.\",\n    \"okt.\",\n    \"nóv.\",\n    \"des.\",\n  ],\n\n  wide: [\n    \"jan<PERSON>ar\",\n    \"febrúar\",\n    \"mars\",\n    \"apríl\",\n    \"maí\",\n    \"jún<PERSON>\",\n    \"júlí\",\n    \"ágúst\",\n    \"september\",\n    \"október\",\n    \"nóvember\",\n    \"desember\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"Þ\", \"M\", \"F\", \"F\", \"L\"],\n  short: [\"Su\", \"Má\", \"Þr\", \"Mi\", \"Fi\", \"Fö\", \"La\"],\n  abbreviated: [\"sun.\", \"mán.\", \"þri.\", \"mið.\", \"fim.\", \"fös.\", \"lau.\"],\n\n  wide: [\n    \"sunnudagur\",\n    \"mánudagur\",\n    \"þriðjudagur\",\n    \"miðvikudagur\",\n    \"fimmtudagur\",\n    \"föstudagur\",\n    \"laugardagur\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"f\",\n    pm: \"e\",\n    midnight: \"miðnætti\",\n    noon: \"hádegi\",\n    morning: \"morgunn\",\n    afternoon: \"síðdegi\",\n    evening: \"kvöld\",\n    night: \"nótt\",\n  },\n  abbreviated: {\n    am: \"f.h.\",\n    pm: \"e.h.\",\n    midnight: \"miðnætti\",\n    noon: \"hádegi\",\n    morning: \"morgunn\",\n    afternoon: \"síðdegi\",\n    evening: \"kvöld\",\n    night: \"nótt\",\n  },\n  wide: {\n    am: \"fyrir hádegi\",\n    pm: \"eftir hádegi\",\n    midnight: \"miðnætti\",\n    noon: \"hádegi\",\n    morning: \"morgunn\",\n    afternoon: \"síðdegi\",\n    evening: \"kvöld\",\n    night: \"nótt\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"f\",\n    pm: \"e\",\n    midnight: \"á miðnætti\",\n    noon: \"á hádegi\",\n    morning: \"að morgni\",\n    afternoon: \"síðdegis\",\n    evening: \"um kvöld\",\n    night: \"um nótt\",\n  },\n  abbreviated: {\n    am: \"f.h.\",\n    pm: \"e.h.\",\n    midnight: \"á miðnætti\",\n    noon: \"á hádegi\",\n    morning: \"að morgni\",\n    afternoon: \"síðdegis\",\n    evening: \"um kvöld\",\n    night: \"um nótt\",\n  },\n  wide: {\n    am: \"fyrir hádegi\",\n    pm: \"eftir hádegi\",\n    midnight: \"á miðnætti\",\n    noon: \"á hádegi\",\n    morning: \"að morgni\",\n    afternoon: \"síðdegis\",\n    evening: \"um kvöld\",\n    night: \"um nótt\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n\n  return number + \".\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EAC1BC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EAC/BC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa;AACrC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe;AAC3E,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU;AAEd,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAErEC,IAAI,EAAE,CACJ,YAAY,EACZ,WAAW,EACX,aAAa,EACb,cAAc,EACd,aAAa,EACb,YAAY,EACZ,aAAa;AAEjB,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,cAAc;IAClBC,EAAE,EAAE,cAAc;IAClBC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,cAAc;IAClBC,EAAE,EAAE,cAAc;IAClBC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAElC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBL,aAAa;EAEbM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}