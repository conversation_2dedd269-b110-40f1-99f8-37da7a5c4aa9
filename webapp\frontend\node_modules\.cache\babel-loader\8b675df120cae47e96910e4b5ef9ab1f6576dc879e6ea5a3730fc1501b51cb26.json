{"ast": null, "code": "import { formatDistance } from \"./ar-TN/_lib/formatDistance.js\";\nimport { formatLong } from \"./ar-TN/_lib/formatLong.js\";\nimport { formatRelative } from \"./ar-TN/_lib/formatRelative.js\";\nimport { localize } from \"./ar-TN/_lib/localize.js\";\nimport { match } from \"./ar-TN/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Tunisian Arabic).\n * @language Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@essana3](https://github.com/essana3)\n */\nexport const arTN = {\n  code: \"ar-TN\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default arTN;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "arTN", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ar-TN.js"], "sourcesContent": ["import { formatDistance } from \"./ar-TN/_lib/formatDistance.js\";\nimport { formatLong } from \"./ar-TN/_lib/formatLong.js\";\nimport { formatRelative } from \"./ar-TN/_lib/formatRelative.js\";\nimport { localize } from \"./ar-TN/_lib/localize.js\";\nimport { match } from \"./ar-TN/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Tunisian Arabic).\n * @language Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@essana3](https://github.com/essana3)\n */\nexport const arTN = {\n  code: \"ar-TN\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default arTN;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,KAAK,QAAQ,uBAAuB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}