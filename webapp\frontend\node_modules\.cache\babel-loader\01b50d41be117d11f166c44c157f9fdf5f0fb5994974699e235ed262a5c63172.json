{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"views\", \"format\"];\nimport { resolveTimeFormat, isTimeView, isInternalTimeView } from \"./time-utils.js\";\nimport { isDatePickerView, resolveDateFormat } from \"./date-utils.js\";\nexport const resolveDateTimeFormat = (utils, _ref, ignoreDateResolving) => {\n  let {\n      views,\n      format\n    } = _ref,\n    other = _objectWithoutPropertiesLoose(_ref, _excluded);\n  if (format) {\n    return format;\n  }\n  const dateViews = [];\n  const timeViews = [];\n  views.forEach(view => {\n    if (isTimeView(view)) {\n      timeViews.push(view);\n    } else if (isDatePickerView(view)) {\n      dateViews.push(view);\n    }\n  });\n  if (timeViews.length === 0) {\n    return resolveDateFormat(utils, _extends({\n      views: dateViews\n    }, other), false);\n  }\n  if (dateViews.length === 0) {\n    return resolveTimeFormat(utils, _extends({\n      views: timeViews\n    }, other));\n  }\n  const timeFormat = resolveTimeFormat(utils, _extends({\n    views: timeViews\n  }, other));\n  const dateFormat = ignoreDateResolving ? utils.formats.keyboardDate : resolveDateFormat(utils, _extends({\n    views: dateViews\n  }, other), false);\n  return `${dateFormat} ${timeFormat}`;\n};\nconst resolveViews = (ampm, views, shouldUseSingleColumn) => {\n  if (shouldUseSingleColumn) {\n    return views.filter(view => !isInternalTimeView(view) || view === 'hours');\n  }\n  return ampm ? [...views, 'meridiem'] : views;\n};\nconst resolveShouldRenderTimeInASingleColumn = (timeSteps, threshold) => 24 * 60 / ((timeSteps.hours ?? 1) * (timeSteps.minutes ?? 5)) <= threshold;\nexport function resolveTimeViewsResponse({\n  thresholdToRenderTimeInASingleColumn: inThreshold,\n  ampm,\n  timeSteps: inTimeSteps,\n  views\n}) {\n  const thresholdToRenderTimeInASingleColumn = inThreshold ?? 24;\n  const timeSteps = _extends({\n    hours: 1,\n    minutes: 5,\n    seconds: 5\n  }, inTimeSteps);\n  const shouldRenderTimeInASingleColumn = resolveShouldRenderTimeInASingleColumn(timeSteps, thresholdToRenderTimeInASingleColumn);\n  return {\n    thresholdToRenderTimeInASingleColumn,\n    timeSteps,\n    shouldRenderTimeInASingleColumn,\n    views: resolveViews(ampm, views, shouldRenderTimeInASingleColumn)\n  };\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "resolveTimeFormat", "isTimeView", "isInternalTimeView", "isDatePickerView", "resolveDateFormat", "resolveDateTimeFormat", "utils", "_ref", "ignoreDateResolving", "views", "format", "other", "dateViews", "timeViews", "for<PERSON>ach", "view", "push", "length", "timeFormat", "dateFormat", "formats", "keyboardDate", "resolveViews", "ampm", "shouldUseSingleColumn", "filter", "resolveShouldRenderTimeInASingleColumn", "timeSteps", "threshold", "hours", "minutes", "resolveTimeViewsResponse", "thresholdToRenderTimeInASingleColumn", "inThreshold", "inTimeSteps", "seconds", "shouldRenderTimeInASingleColumn"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/utils/date-time-utils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"views\", \"format\"];\nimport { resolveTimeFormat, isTimeView, isInternalTimeView } from \"./time-utils.js\";\nimport { isDatePickerView, resolveDateFormat } from \"./date-utils.js\";\nexport const resolveDateTimeFormat = (utils, _ref, ignoreDateResolving) => {\n  let {\n      views,\n      format\n    } = _ref,\n    other = _objectWithoutPropertiesLoose(_ref, _excluded);\n  if (format) {\n    return format;\n  }\n  const dateViews = [];\n  const timeViews = [];\n  views.forEach(view => {\n    if (isTimeView(view)) {\n      timeViews.push(view);\n    } else if (isDatePickerView(view)) {\n      dateViews.push(view);\n    }\n  });\n  if (timeViews.length === 0) {\n    return resolveDateFormat(utils, _extends({\n      views: dateViews\n    }, other), false);\n  }\n  if (dateViews.length === 0) {\n    return resolveTimeFormat(utils, _extends({\n      views: timeViews\n    }, other));\n  }\n  const timeFormat = resolveTimeFormat(utils, _extends({\n    views: timeViews\n  }, other));\n  const dateFormat = ignoreDateResolving ? utils.formats.keyboardDate : resolveDateFormat(utils, _extends({\n    views: dateViews\n  }, other), false);\n  return `${dateFormat} ${timeFormat}`;\n};\nconst resolveViews = (ampm, views, shouldUseSingleColumn) => {\n  if (shouldUseSingleColumn) {\n    return views.filter(view => !isInternalTimeView(view) || view === 'hours');\n  }\n  return ampm ? [...views, 'meridiem'] : views;\n};\nconst resolveShouldRenderTimeInASingleColumn = (timeSteps, threshold) => 24 * 60 / ((timeSteps.hours ?? 1) * (timeSteps.minutes ?? 5)) <= threshold;\nexport function resolveTimeViewsResponse({\n  thresholdToRenderTimeInASingleColumn: inThreshold,\n  ampm,\n  timeSteps: inTimeSteps,\n  views\n}) {\n  const thresholdToRenderTimeInASingleColumn = inThreshold ?? 24;\n  const timeSteps = _extends({\n    hours: 1,\n    minutes: 5,\n    seconds: 5\n  }, inTimeSteps);\n  const shouldRenderTimeInASingleColumn = resolveShouldRenderTimeInASingleColumn(timeSteps, thresholdToRenderTimeInASingleColumn);\n  return {\n    thresholdToRenderTimeInASingleColumn,\n    timeSteps,\n    shouldRenderTimeInASingleColumn,\n    views: resolveViews(ampm, views, shouldRenderTimeInASingleColumn)\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;AACrC,SAASC,iBAAiB,EAAEC,UAAU,EAAEC,kBAAkB,QAAQ,iBAAiB;AACnF,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,iBAAiB;AACrE,OAAO,MAAMC,qBAAqB,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAEC,mBAAmB,KAAK;EACzE,IAAI;MACAC,KAAK;MACLC;IACF,CAAC,GAAGH,IAAI;IACRI,KAAK,GAAGb,6BAA6B,CAACS,IAAI,EAAER,SAAS,CAAC;EACxD,IAAIW,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,MAAME,SAAS,GAAG,EAAE;EACpB,MAAMC,SAAS,GAAG,EAAE;EACpBJ,KAAK,CAACK,OAAO,CAACC,IAAI,IAAI;IACpB,IAAId,UAAU,CAACc,IAAI,CAAC,EAAE;MACpBF,SAAS,CAACG,IAAI,CAACD,IAAI,CAAC;IACtB,CAAC,MAAM,IAAIZ,gBAAgB,CAACY,IAAI,CAAC,EAAE;MACjCH,SAAS,CAACI,IAAI,CAACD,IAAI,CAAC;IACtB;EACF,CAAC,CAAC;EACF,IAAIF,SAAS,CAACI,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOb,iBAAiB,CAACE,KAAK,EAAET,QAAQ,CAAC;MACvCY,KAAK,EAAEG;IACT,CAAC,EAAED,KAAK,CAAC,EAAE,KAAK,CAAC;EACnB;EACA,IAAIC,SAAS,CAACK,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOjB,iBAAiB,CAACM,KAAK,EAAET,QAAQ,CAAC;MACvCY,KAAK,EAAEI;IACT,CAAC,EAAEF,KAAK,CAAC,CAAC;EACZ;EACA,MAAMO,UAAU,GAAGlB,iBAAiB,CAACM,KAAK,EAAET,QAAQ,CAAC;IACnDY,KAAK,EAAEI;EACT,CAAC,EAAEF,KAAK,CAAC,CAAC;EACV,MAAMQ,UAAU,GAAGX,mBAAmB,GAAGF,KAAK,CAACc,OAAO,CAACC,YAAY,GAAGjB,iBAAiB,CAACE,KAAK,EAAET,QAAQ,CAAC;IACtGY,KAAK,EAAEG;EACT,CAAC,EAAED,KAAK,CAAC,EAAE,KAAK,CAAC;EACjB,OAAO,GAAGQ,UAAU,IAAID,UAAU,EAAE;AACtC,CAAC;AACD,MAAMI,YAAY,GAAGA,CAACC,IAAI,EAAEd,KAAK,EAAEe,qBAAqB,KAAK;EAC3D,IAAIA,qBAAqB,EAAE;IACzB,OAAOf,KAAK,CAACgB,MAAM,CAACV,IAAI,IAAI,CAACb,kBAAkB,CAACa,IAAI,CAAC,IAAIA,IAAI,KAAK,OAAO,CAAC;EAC5E;EACA,OAAOQ,IAAI,GAAG,CAAC,GAAGd,KAAK,EAAE,UAAU,CAAC,GAAGA,KAAK;AAC9C,CAAC;AACD,MAAMiB,sCAAsC,GAAGA,CAACC,SAAS,EAAEC,SAAS,KAAK,EAAE,GAAG,EAAE,IAAI,CAACD,SAAS,CAACE,KAAK,IAAI,CAAC,KAAKF,SAAS,CAACG,OAAO,IAAI,CAAC,CAAC,CAAC,IAAIF,SAAS;AACnJ,OAAO,SAASG,wBAAwBA,CAAC;EACvCC,oCAAoC,EAAEC,WAAW;EACjDV,IAAI;EACJI,SAAS,EAAEO,WAAW;EACtBzB;AACF,CAAC,EAAE;EACD,MAAMuB,oCAAoC,GAAGC,WAAW,IAAI,EAAE;EAC9D,MAAMN,SAAS,GAAG9B,QAAQ,CAAC;IACzBgC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE,CAAC;IACVK,OAAO,EAAE;EACX,CAAC,EAAED,WAAW,CAAC;EACf,MAAME,+BAA+B,GAAGV,sCAAsC,CAACC,SAAS,EAAEK,oCAAoC,CAAC;EAC/H,OAAO;IACLA,oCAAoC;IACpCL,SAAS;IACTS,+BAA+B;IAC/B3B,KAAK,EAAEa,YAAY,CAACC,IAAI,EAAEd,KAAK,EAAE2B,+BAA+B;EAClE,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}