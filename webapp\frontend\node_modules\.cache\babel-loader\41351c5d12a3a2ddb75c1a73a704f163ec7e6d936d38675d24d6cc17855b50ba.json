{"ast": null, "code": "const translations = {\n  about: \"kö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  over: \"több mint\",\n  almost: \"majdnem\",\n  lessthan: \"kevesebb mint\"\n};\nconst withoutSuffixes = {\n  xseconds: \" másodperc\",\n  halfaminute: \"fél perc\",\n  xminutes: \" perc\",\n  xhours: \" óra\",\n  xdays: \" nap\",\n  xweeks: \" hét\",\n  xmonths: \" hónap\",\n  xyears: \" év\"\n};\nconst withSuffixes = {\n  xseconds: {\n    \"-1\": \" másodperccel ezelőtt\",\n    1: \" másodperc múlva\",\n    0: \" másodperce\"\n  },\n  halfaminute: {\n    \"-1\": \"fél perccel ezelőtt\",\n    1: \"fél perc múlva\",\n    0: \"fél perce\"\n  },\n  xminutes: {\n    \"-1\": \" perccel ezelőtt\",\n    1: \" perc múlva\",\n    0: \" perce\"\n  },\n  xhours: {\n    \"-1\": \" ór<PERSON><PERSON> ezel<PERSON>tt\",\n    1: \" óra múlva\",\n    0: \" órája\"\n  },\n  xdays: {\n    \"-1\": \" nappal ezelőtt\",\n    1: \" nap múlva\",\n    0: \" napja\"\n  },\n  xweeks: {\n    \"-1\": \" héttel ezelőtt\",\n    1: \" hét múlva\",\n    0: \" hete\"\n  },\n  xmonths: {\n    \"-1\": \" hónappal ezelőtt\",\n    1: \" hónap múlva\",\n    0: \" hónapja\"\n  },\n  xyears: {\n    \"-1\": \" évvel ezelőtt\",\n    1: \" év múlva\",\n    0: \" éve\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  const adverb = token.match(/about|over|almost|lessthan/i);\n  const unit = adverb ? token.replace(adverb[0], \"\") : token;\n  const addSuffix = options?.addSuffix === true;\n  const key = unit.toLowerCase();\n  const comparison = options?.comparison || 0;\n  const translated = addSuffix ? withSuffixes[key][comparison] : withoutSuffixes[key];\n  let result = key === \"halfaminute\" ? translated : count + translated;\n  if (adverb) {\n    const adv = adverb[0].toLowerCase();\n    result = translations[adv] + \" \" + result;\n  }\n  return result;\n};", "map": {"version": 3, "names": ["translations", "about", "over", "almost", "lessthan", "withoutSuffixes", "xseconds", "halfaminute", "xminutes", "xhours", "xdays", "xweeks", "xmonths", "xyears", "withSuffixes", "formatDistance", "token", "count", "options", "adverb", "match", "unit", "replace", "addSuffix", "key", "toLowerCase", "comparison", "translated", "result", "adv"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/hu/_lib/formatDistance.js"], "sourcesContent": ["const translations = {\n  about: \"kör<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  over: \"több mint\",\n  almost: \"majdnem\",\n  lessthan: \"kevesebb mint\",\n};\n\nconst withoutSuffixes = {\n  xseconds: \" másodperc\",\n  halfaminute: \"fél perc\",\n  xminutes: \" perc\",\n  xhours: \" óra\",\n  xdays: \" nap\",\n  xweeks: \" hét\",\n  xmonths: \" hónap\",\n  xyears: \" év\",\n};\n\nconst withSuffixes = {\n  xseconds: {\n    \"-1\": \" másodperccel ezelőtt\",\n    1: \" másodperc múlva\",\n    0: \" másodperce\",\n  },\n  halfaminute: {\n    \"-1\": \"fél perccel ezelőtt\",\n    1: \"fél perc múlva\",\n    0: \"fél perce\",\n  },\n  xminutes: {\n    \"-1\": \" perccel ezelőtt\",\n    1: \" perc múlva\",\n    0: \" perce\",\n  },\n  xhours: {\n    \"-1\": \" ór<PERSON><PERSON> ezel<PERSON>\",\n    1: \" óra múlva\",\n    0: \" órája\",\n  },\n  xdays: {\n    \"-1\": \" nappal ezelőtt\",\n    1: \" nap múlva\",\n    0: \" napja\",\n  },\n  xweeks: {\n    \"-1\": \" héttel ezelőtt\",\n    1: \" hét múlva\",\n    0: \" hete\",\n  },\n  xmonths: {\n    \"-1\": \" hónappal ezelőtt\",\n    1: \" hónap múlva\",\n    0: \" hónapja\",\n  },\n  xyears: {\n    \"-1\": \" évvel ezelőtt\",\n    1: \" év múlva\",\n    0: \" éve\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  const adverb = token.match(/about|over|almost|lessthan/i);\n  const unit = adverb ? token.replace(adverb[0], \"\") : token;\n\n  const addSuffix = options?.addSuffix === true;\n  const key = unit.toLowerCase();\n  const comparison = options?.comparison || 0;\n\n  const translated = addSuffix\n    ? withSuffixes[key][comparison]\n    : withoutSuffixes[key];\n\n  let result = key === \"halfaminute\" ? translated : count + translated;\n\n  if (adverb) {\n    const adv = adverb[0].toLowerCase();\n    result = translations[adv] + \" \" + result;\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACnBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE;AACZ,CAAC;AAED,MAAMC,eAAe,GAAG;EACtBC,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE,UAAU;EACvBC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAE,QAAQ;EACjBC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,YAAY,GAAG;EACnBR,QAAQ,EAAE;IACR,IAAI,EAAE,uBAAuB;IAC7B,CAAC,EAAE,kBAAkB;IACrB,CAAC,EAAE;EACL,CAAC;EACDC,WAAW,EAAE;IACX,IAAI,EAAE,qBAAqB;IAC3B,CAAC,EAAE,gBAAgB;IACnB,CAAC,EAAE;EACL,CAAC;EACDC,QAAQ,EAAE;IACR,IAAI,EAAE,kBAAkB;IACxB,CAAC,EAAE,aAAa;IAChB,CAAC,EAAE;EACL,CAAC;EACDC,MAAM,EAAE;IACN,IAAI,EAAE,iBAAiB;IACvB,CAAC,EAAE,YAAY;IACf,CAAC,EAAE;EACL,CAAC;EACDC,KAAK,EAAE;IACL,IAAI,EAAE,iBAAiB;IACvB,CAAC,EAAE,YAAY;IACf,CAAC,EAAE;EACL,CAAC;EACDC,MAAM,EAAE;IACN,IAAI,EAAE,iBAAiB;IACvB,CAAC,EAAE,YAAY;IACf,CAAC,EAAE;EACL,CAAC;EACDC,OAAO,EAAE;IACP,IAAI,EAAE,mBAAmB;IACzB,CAAC,EAAE,cAAc;IACjB,CAAC,EAAE;EACL,CAAC;EACDC,MAAM,EAAE;IACN,IAAI,EAAE,gBAAgB;IACtB,CAAC,EAAE,WAAW;IACd,CAAC,EAAE;EACL;AACF,CAAC;AAED,OAAO,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,MAAMC,MAAM,GAAGH,KAAK,CAACI,KAAK,CAAC,6BAA6B,CAAC;EACzD,MAAMC,IAAI,GAAGF,MAAM,GAAGH,KAAK,CAACM,OAAO,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGH,KAAK;EAE1D,MAAMO,SAAS,GAAGL,OAAO,EAAEK,SAAS,KAAK,IAAI;EAC7C,MAAMC,GAAG,GAAGH,IAAI,CAACI,WAAW,CAAC,CAAC;EAC9B,MAAMC,UAAU,GAAGR,OAAO,EAAEQ,UAAU,IAAI,CAAC;EAE3C,MAAMC,UAAU,GAAGJ,SAAS,GACxBT,YAAY,CAACU,GAAG,CAAC,CAACE,UAAU,CAAC,GAC7BrB,eAAe,CAACmB,GAAG,CAAC;EAExB,IAAII,MAAM,GAAGJ,GAAG,KAAK,aAAa,GAAGG,UAAU,GAAGV,KAAK,GAAGU,UAAU;EAEpE,IAAIR,MAAM,EAAE;IACV,MAAMU,GAAG,GAAGV,MAAM,CAAC,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;IACnCG,MAAM,GAAG5B,YAAY,CAAC6B,GAAG,CAAC,GAAG,GAAG,GAAGD,MAAM;EAC3C;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}