{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ampm\", \"timeSteps\", \"autoFocus\", \"slots\", \"slotProps\", \"value\", \"defaultValue\", \"referenceDate\", \"disableIgnoringDatePartForTimeValidation\", \"maxTime\", \"minTime\", \"disableFuture\", \"disablePast\", \"minutesStep\", \"shouldDisableTime\", \"onChange\", \"view\", \"views\", \"openTo\", \"onViewChange\", \"focusedView\", \"onFocusedViewChange\", \"className\", \"classes\", \"disabled\", \"readOnly\", \"skipDisabled\", \"timezone\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nimport { useUtils, useNow } from \"../internals/hooks/useUtils.js\";\nimport { convertValueToMeridiem, createIsAfterIgnoreDatePart } from \"../internals/utils/time-utils.js\";\nimport { useViews } from \"../internals/hooks/useViews.js\";\nimport { useMeridiemMode } from \"../internals/hooks/date-helpers-hooks.js\";\nimport { PickerViewRoot } from \"../internals/components/PickerViewRoot/index.js\";\nimport { getMultiSectionDigitalClockUtilityClass } from \"./multiSectionDigitalClockClasses.js\";\nimport { MultiSectionDigitalClockSection } from \"./MultiSectionDigitalClockSection.js\";\nimport { getHourSectionOptions, getTimeSectionOptions } from \"./MultiSectionDigitalClock.utils.js\";\nimport { useControlledValue } from \"../internals/hooks/useControlledValue.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { useClockReferenceDate } from \"../internals/hooks/useClockReferenceDate.js\";\nimport { formatMeridiem } from \"../internals/utils/date-utils.js\";\nimport { usePickerPrivateContext } from \"../internals/hooks/usePickerPrivateContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getMultiSectionDigitalClockUtilityClass, classes);\n};\nconst MultiSectionDigitalClockRoot = styled(PickerViewRoot, {\n  name: 'MuiMultiSectionDigitalClock',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  flexDirection: 'row',\n  width: '100%',\n  borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`\n}));\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [DigitalClock](https://mui.com/x/react-date-pickers/digital-clock/)\n *\n * API:\n *\n * - [MultiSectionDigitalClock API](https://mui.com/x/api/date-pickers/multi-section-digital-clock/)\n */\nexport const MultiSectionDigitalClock = /*#__PURE__*/React.forwardRef(function MultiSectionDigitalClock(inProps, ref) {\n  const utils = useUtils();\n  const isRtl = useRtl();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiMultiSectionDigitalClock'\n  });\n  const {\n      ampm = utils.is12HourCycleInCurrentLocale(),\n      timeSteps: inTimeSteps,\n      autoFocus,\n      slots,\n      slotProps,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disableIgnoringDatePartForTimeValidation = false,\n      maxTime,\n      minTime,\n      disableFuture,\n      disablePast,\n      minutesStep = 1,\n      shouldDisableTime,\n      onChange,\n      view: inView,\n      views: inViews = ['hours', 'minutes'],\n      openTo,\n      onViewChange,\n      focusedView: inFocusedView,\n      onFocusedViewChange,\n      className,\n      classes: classesProp,\n      disabled,\n      readOnly,\n      skipDisabled = false,\n      timezone: timezoneProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange: handleRawValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'MultiSectionDigitalClock',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const translations = usePickerTranslations();\n  const now = useNow(timezone);\n  const timeSteps = React.useMemo(() => _extends({\n    hours: 1,\n    minutes: 5,\n    seconds: 5\n  }, inTimeSteps), [inTimeSteps]);\n  const valueOrReferenceDate = useClockReferenceDate({\n    value,\n    referenceDate: referenceDateProp,\n    utils,\n    props,\n    timezone\n  });\n  const handleValueChange = useEventCallback((newValue, selectionState, selectedView) => handleRawValueChange(newValue, selectionState, selectedView));\n  const views = React.useMemo(() => {\n    if (!ampm || !inViews.includes('hours')) {\n      return inViews;\n    }\n    return inViews.includes('meridiem') ? inViews : [...inViews, 'meridiem'];\n  }, [ampm, inViews]);\n  const {\n    view,\n    setValueAndGoToNextView,\n    focusedView\n  } = useViews({\n    view: inView,\n    views,\n    openTo,\n    onViewChange,\n    onChange: handleValueChange,\n    focusedView: inFocusedView,\n    onFocusedViewChange\n  });\n  const handleMeridiemValueChange = useEventCallback(newValue => {\n    setValueAndGoToNextView(newValue, 'finish', 'meridiem');\n  });\n  const {\n    meridiemMode,\n    handleMeridiemChange\n  } = useMeridiemMode(valueOrReferenceDate, ampm, handleMeridiemValueChange, 'finish');\n  const isTimeDisabled = React.useCallback((rawValue, viewType) => {\n    const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, utils);\n    const shouldCheckPastEnd = viewType === 'hours' || viewType === 'minutes' && views.includes('seconds');\n    const containsValidTime = ({\n      start,\n      end\n    }) => {\n      if (minTime && isAfter(minTime, end)) {\n        return false;\n      }\n      if (maxTime && isAfter(start, maxTime)) {\n        return false;\n      }\n      if (disableFuture && isAfter(start, now)) {\n        return false;\n      }\n      if (disablePast && isAfter(now, shouldCheckPastEnd ? end : start)) {\n        return false;\n      }\n      return true;\n    };\n    const isValidValue = (timeValue, step = 1) => {\n      if (timeValue % step !== 0) {\n        return false;\n      }\n      if (shouldDisableTime) {\n        switch (viewType) {\n          case 'hours':\n            return !shouldDisableTime(utils.setHours(valueOrReferenceDate, timeValue), 'hours');\n          case 'minutes':\n            return !shouldDisableTime(utils.setMinutes(valueOrReferenceDate, timeValue), 'minutes');\n          case 'seconds':\n            return !shouldDisableTime(utils.setSeconds(valueOrReferenceDate, timeValue), 'seconds');\n          default:\n            return false;\n        }\n      }\n      return true;\n    };\n    switch (viewType) {\n      case 'hours':\n        {\n          const valueWithMeridiem = convertValueToMeridiem(rawValue, meridiemMode, ampm);\n          const dateWithNewHours = utils.setHours(valueOrReferenceDate, valueWithMeridiem);\n          if (utils.getHours(dateWithNewHours) !== valueWithMeridiem) {\n            return true;\n          }\n          const start = utils.setSeconds(utils.setMinutes(dateWithNewHours, 0), 0);\n          const end = utils.setSeconds(utils.setMinutes(dateWithNewHours, 59), 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(valueWithMeridiem);\n        }\n      case 'minutes':\n        {\n          const dateWithNewMinutes = utils.setMinutes(valueOrReferenceDate, rawValue);\n          const start = utils.setSeconds(dateWithNewMinutes, 0);\n          const end = utils.setSeconds(dateWithNewMinutes, 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue, minutesStep);\n        }\n      case 'seconds':\n        {\n          const dateWithNewSeconds = utils.setSeconds(valueOrReferenceDate, rawValue);\n          const start = dateWithNewSeconds;\n          const end = dateWithNewSeconds;\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue);\n        }\n      default:\n        throw new Error('not supported');\n    }\n  }, [ampm, valueOrReferenceDate, disableIgnoringDatePartForTimeValidation, maxTime, meridiemMode, minTime, minutesStep, shouldDisableTime, utils, disableFuture, disablePast, now, views]);\n  const buildViewProps = React.useCallback(viewToBuild => {\n    switch (viewToBuild) {\n      case 'hours':\n        {\n          return {\n            onChange: hours => {\n              const valueWithMeridiem = convertValueToMeridiem(hours, meridiemMode, ampm);\n              setValueAndGoToNextView(utils.setHours(valueOrReferenceDate, valueWithMeridiem), 'finish', 'hours');\n            },\n            items: getHourSectionOptions({\n              now,\n              value,\n              ampm,\n              utils,\n              isDisabled: hours => isTimeDisabled(hours, 'hours'),\n              timeStep: timeSteps.hours,\n              resolveAriaLabel: translations.hoursClockNumberText,\n              valueOrReferenceDate\n            })\n          };\n        }\n      case 'minutes':\n        {\n          return {\n            onChange: minutes => {\n              setValueAndGoToNextView(utils.setMinutes(valueOrReferenceDate, minutes), 'finish', 'minutes');\n            },\n            items: getTimeSectionOptions({\n              value: utils.getMinutes(valueOrReferenceDate),\n              utils,\n              isDisabled: minutes => isTimeDisabled(minutes, 'minutes'),\n              resolveLabel: minutes => utils.format(utils.setMinutes(now, minutes), 'minutes'),\n              timeStep: timeSteps.minutes,\n              hasValue: !!value,\n              resolveAriaLabel: translations.minutesClockNumberText\n            })\n          };\n        }\n      case 'seconds':\n        {\n          return {\n            onChange: seconds => {\n              setValueAndGoToNextView(utils.setSeconds(valueOrReferenceDate, seconds), 'finish', 'seconds');\n            },\n            items: getTimeSectionOptions({\n              value: utils.getSeconds(valueOrReferenceDate),\n              utils,\n              isDisabled: seconds => isTimeDisabled(seconds, 'seconds'),\n              resolveLabel: seconds => utils.format(utils.setSeconds(now, seconds), 'seconds'),\n              timeStep: timeSteps.seconds,\n              hasValue: !!value,\n              resolveAriaLabel: translations.secondsClockNumberText\n            })\n          };\n        }\n      case 'meridiem':\n        {\n          const amLabel = formatMeridiem(utils, 'am');\n          const pmLabel = formatMeridiem(utils, 'pm');\n          return {\n            onChange: handleMeridiemChange,\n            items: [{\n              value: 'am',\n              label: amLabel,\n              isSelected: () => !!value && meridiemMode === 'am',\n              isFocused: () => !!valueOrReferenceDate && meridiemMode === 'am',\n              ariaLabel: amLabel\n            }, {\n              value: 'pm',\n              label: pmLabel,\n              isSelected: () => !!value && meridiemMode === 'pm',\n              isFocused: () => !!valueOrReferenceDate && meridiemMode === 'pm',\n              ariaLabel: pmLabel\n            }]\n          };\n        }\n      default:\n        throw new Error(`Unknown view: ${viewToBuild} found.`);\n    }\n  }, [now, value, ampm, utils, timeSteps.hours, timeSteps.minutes, timeSteps.seconds, translations.hoursClockNumberText, translations.minutesClockNumberText, translations.secondsClockNumberText, meridiemMode, setValueAndGoToNextView, valueOrReferenceDate, isTimeDisabled, handleMeridiemChange]);\n  const viewsToRender = React.useMemo(() => {\n    if (!isRtl) {\n      return views;\n    }\n    const digitViews = views.filter(v => v !== 'meridiem');\n    digitViews.reverse();\n    if (views.includes('meridiem')) {\n      digitViews.push('meridiem');\n    }\n    return digitViews;\n  }, [isRtl, views]);\n  const viewTimeOptions = React.useMemo(() => {\n    return views.reduce((result, currentView) => {\n      return _extends({}, result, {\n        [currentView]: buildViewProps(currentView)\n      });\n    }, {});\n  }, [views, buildViewProps]);\n  const {\n    ownerState\n  } = usePickerPrivateContext();\n  const classes = useUtilityClasses(classesProp);\n  return /*#__PURE__*/_jsx(MultiSectionDigitalClockRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    role: \"group\"\n  }, other, {\n    children: viewsToRender.map(timeView => /*#__PURE__*/_jsx(MultiSectionDigitalClockSection, {\n      items: viewTimeOptions[timeView].items,\n      onChange: viewTimeOptions[timeView].onChange,\n      active: view === timeView,\n      autoFocus: autoFocus ?? focusedView === timeView,\n      disabled: disabled,\n      readOnly: readOnly,\n      slots: slots,\n      slotProps: slotProps,\n      skipDisabled: skipDisabled,\n      \"aria-label\": translations.selectViewText(timeView)\n    }, timeView))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MultiSectionDigitalClock.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the component is disabled.\n   * When disabled, the value cannot be changed and no interaction is possible.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * Controlled focused view.\n   */\n  focusedView: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TView The view type. Will be one of date or time views.\n   * @param {TValue} value The new value.\n   * @param {PickerSelectionState | undefined} selectionState Indicates if the date selection is complete.\n   * @param {TView | undefined} selectedView Indicates the view in which the selection has been made.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired on focused view change.\n   * @template TView\n   * @param {TView} view The new view to focus or not.\n   * @param {boolean} hasFocus `true` if the view should be focused.\n   */\n  onFocusedViewChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * If `true`, the component is read-only.\n   * When read-only, the value cannot be changed but the user can interact with the interface.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid time using the validation props, except callbacks such as `shouldDisableTime`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific time.\n   * @param {PickerValidDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * If `true`, disabled digital clock items will not be rendered.\n   * @default false\n   */\n  skipDisabled: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overrideable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The time steps between two time unit options.\n   * For example, if `timeStep.minutes = 8`, then the available minute options will be `[0, 8, 16, 24, 32, 40, 48, 56]`.\n   * @default{ hours: 1, minutes: 5, seconds: 5 }\n   */\n  timeSteps: PropTypes.shape({\n    hours: PropTypes.number,\n    minutes: PropTypes.number,\n    seconds: PropTypes.number\n  }),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Available views.\n   * @default ['hours', 'minutes']\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']).isRequired)\n} : void 0;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "PropTypes", "useRtl", "styled", "useThemeProps", "useEventCallback", "composeClasses", "usePickerTranslations", "useUtils", "useNow", "convertValueToMeridiem", "createIsAfterIgnoreDatePart", "useViews", "useMeridiemMode", "PickerViewRoot", "getMultiSectionDigitalClockUtilityClass", "MultiSectionDigitalClockSection", "getHourSectionOptions", "getTimeSectionOptions", "useControlledValue", "singleItemValueManager", "useClockReferenceDate", "formatMeridiem", "usePickerPrivateContext", "jsx", "_jsx", "useUtilityClasses", "classes", "slots", "root", "MultiSectionDigitalClockRoot", "name", "slot", "theme", "flexDirection", "width", "borderBottom", "vars", "palette", "divider", "MultiSectionDigitalClock", "forwardRef", "inProps", "ref", "utils", "isRtl", "props", "ampm", "is12HourCycleInCurrentLocale", "timeSteps", "inTimeSteps", "autoFocus", "slotProps", "value", "valueProp", "defaultValue", "referenceDate", "referenceDateProp", "disableIgnoringDatePartForTimeValidation", "maxTime", "minTime", "disableFuture", "disablePast", "minutesStep", "shouldDisableTime", "onChange", "view", "inView", "views", "inViews", "openTo", "onViewChange", "focused<PERSON>iew", "inFocusedView", "onFocusedViewChange", "className", "classesProp", "disabled", "readOnly", "skipDisabled", "timezone", "timezoneProp", "other", "handleValueChange", "handleRawValueChange", "valueManager", "translations", "now", "useMemo", "hours", "minutes", "seconds", "valueOrReferenceDate", "newValue", "selectionState", "<PERSON><PERSON><PERSON><PERSON>", "includes", "setValueAndGoToNextView", "handleMeridiemValueChange", "meridiemMode", "handleMeridiemChange", "isTimeDisabled", "useCallback", "rawValue", "viewType", "isAfter", "shouldCheckPastEnd", "containsValidTime", "start", "end", "isValidValue", "timeValue", "step", "setHours", "setMinutes", "setSeconds", "valueWithMeridiem", "dateWithNewHours", "getHours", "dateWithNewMinutes", "dateWithNewSeconds", "Error", "buildViewProps", "viewToBuild", "items", "isDisabled", "timeStep", "resolveAriaLabel", "hoursClockNumberText", "getMinutes", "resolve<PERSON>abel", "format", "hasValue", "minutesClockNumberText", "getSeconds", "secondsClockNumberText", "amLabel", "pmLabel", "label", "isSelected", "isFocused", "aria<PERSON><PERSON><PERSON>", "viewsToRender", "digitViews", "filter", "v", "reverse", "push", "viewTimeOptions", "reduce", "result", "current<PERSON>iew", "ownerState", "role", "children", "map", "timeView", "active", "selectViewText", "process", "env", "NODE_ENV", "propTypes", "bool", "object", "string", "oneOf", "number", "func", "sx", "oneOfType", "arrayOf", "shape", "isRequired"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/MultiSectionDigitalClock/MultiSectionDigitalClock.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ampm\", \"timeSteps\", \"autoFocus\", \"slots\", \"slotProps\", \"value\", \"defaultValue\", \"referenceDate\", \"disableIgnoringDatePartForTimeValidation\", \"maxTime\", \"minTime\", \"disableFuture\", \"disablePast\", \"minutesStep\", \"shouldDisableTime\", \"onChange\", \"view\", \"views\", \"openTo\", \"onViewChange\", \"focusedView\", \"onFocusedViewChange\", \"className\", \"classes\", \"disabled\", \"readOnly\", \"skipDisabled\", \"timezone\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nimport { useUtils, useNow } from \"../internals/hooks/useUtils.js\";\nimport { convertValueToMeridiem, createIsAfterIgnoreDatePart } from \"../internals/utils/time-utils.js\";\nimport { useViews } from \"../internals/hooks/useViews.js\";\nimport { useMeridiemMode } from \"../internals/hooks/date-helpers-hooks.js\";\nimport { PickerViewRoot } from \"../internals/components/PickerViewRoot/index.js\";\nimport { getMultiSectionDigitalClockUtilityClass } from \"./multiSectionDigitalClockClasses.js\";\nimport { MultiSectionDigitalClockSection } from \"./MultiSectionDigitalClockSection.js\";\nimport { getHourSectionOptions, getTimeSectionOptions } from \"./MultiSectionDigitalClock.utils.js\";\nimport { useControlledValue } from \"../internals/hooks/useControlledValue.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { useClockReferenceDate } from \"../internals/hooks/useClockReferenceDate.js\";\nimport { formatMeridiem } from \"../internals/utils/date-utils.js\";\nimport { usePickerPrivateContext } from \"../internals/hooks/usePickerPrivateContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getMultiSectionDigitalClockUtilityClass, classes);\n};\nconst MultiSectionDigitalClockRoot = styled(PickerViewRoot, {\n  name: 'MuiMultiSectionDigitalClock',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  flexDirection: 'row',\n  width: '100%',\n  borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`\n}));\n/**\n * Demos:\n *\n * - [TimePicker](https://mui.com/x/react-date-pickers/time-picker/)\n * - [DigitalClock](https://mui.com/x/react-date-pickers/digital-clock/)\n *\n * API:\n *\n * - [MultiSectionDigitalClock API](https://mui.com/x/api/date-pickers/multi-section-digital-clock/)\n */\nexport const MultiSectionDigitalClock = /*#__PURE__*/React.forwardRef(function MultiSectionDigitalClock(inProps, ref) {\n  const utils = useUtils();\n  const isRtl = useRtl();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiMultiSectionDigitalClock'\n  });\n  const {\n      ampm = utils.is12HourCycleInCurrentLocale(),\n      timeSteps: inTimeSteps,\n      autoFocus,\n      slots,\n      slotProps,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disableIgnoringDatePartForTimeValidation = false,\n      maxTime,\n      minTime,\n      disableFuture,\n      disablePast,\n      minutesStep = 1,\n      shouldDisableTime,\n      onChange,\n      view: inView,\n      views: inViews = ['hours', 'minutes'],\n      openTo,\n      onViewChange,\n      focusedView: inFocusedView,\n      onFocusedViewChange,\n      className,\n      classes: classesProp,\n      disabled,\n      readOnly,\n      skipDisabled = false,\n      timezone: timezoneProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange: handleRawValueChange,\n    timezone\n  } = useControlledValue({\n    name: 'MultiSectionDigitalClock',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const translations = usePickerTranslations();\n  const now = useNow(timezone);\n  const timeSteps = React.useMemo(() => _extends({\n    hours: 1,\n    minutes: 5,\n    seconds: 5\n  }, inTimeSteps), [inTimeSteps]);\n  const valueOrReferenceDate = useClockReferenceDate({\n    value,\n    referenceDate: referenceDateProp,\n    utils,\n    props,\n    timezone\n  });\n  const handleValueChange = useEventCallback((newValue, selectionState, selectedView) => handleRawValueChange(newValue, selectionState, selectedView));\n  const views = React.useMemo(() => {\n    if (!ampm || !inViews.includes('hours')) {\n      return inViews;\n    }\n    return inViews.includes('meridiem') ? inViews : [...inViews, 'meridiem'];\n  }, [ampm, inViews]);\n  const {\n    view,\n    setValueAndGoToNextView,\n    focusedView\n  } = useViews({\n    view: inView,\n    views,\n    openTo,\n    onViewChange,\n    onChange: handleValueChange,\n    focusedView: inFocusedView,\n    onFocusedViewChange\n  });\n  const handleMeridiemValueChange = useEventCallback(newValue => {\n    setValueAndGoToNextView(newValue, 'finish', 'meridiem');\n  });\n  const {\n    meridiemMode,\n    handleMeridiemChange\n  } = useMeridiemMode(valueOrReferenceDate, ampm, handleMeridiemValueChange, 'finish');\n  const isTimeDisabled = React.useCallback((rawValue, viewType) => {\n    const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, utils);\n    const shouldCheckPastEnd = viewType === 'hours' || viewType === 'minutes' && views.includes('seconds');\n    const containsValidTime = ({\n      start,\n      end\n    }) => {\n      if (minTime && isAfter(minTime, end)) {\n        return false;\n      }\n      if (maxTime && isAfter(start, maxTime)) {\n        return false;\n      }\n      if (disableFuture && isAfter(start, now)) {\n        return false;\n      }\n      if (disablePast && isAfter(now, shouldCheckPastEnd ? end : start)) {\n        return false;\n      }\n      return true;\n    };\n    const isValidValue = (timeValue, step = 1) => {\n      if (timeValue % step !== 0) {\n        return false;\n      }\n      if (shouldDisableTime) {\n        switch (viewType) {\n          case 'hours':\n            return !shouldDisableTime(utils.setHours(valueOrReferenceDate, timeValue), 'hours');\n          case 'minutes':\n            return !shouldDisableTime(utils.setMinutes(valueOrReferenceDate, timeValue), 'minutes');\n          case 'seconds':\n            return !shouldDisableTime(utils.setSeconds(valueOrReferenceDate, timeValue), 'seconds');\n          default:\n            return false;\n        }\n      }\n      return true;\n    };\n    switch (viewType) {\n      case 'hours':\n        {\n          const valueWithMeridiem = convertValueToMeridiem(rawValue, meridiemMode, ampm);\n          const dateWithNewHours = utils.setHours(valueOrReferenceDate, valueWithMeridiem);\n          if (utils.getHours(dateWithNewHours) !== valueWithMeridiem) {\n            return true;\n          }\n          const start = utils.setSeconds(utils.setMinutes(dateWithNewHours, 0), 0);\n          const end = utils.setSeconds(utils.setMinutes(dateWithNewHours, 59), 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(valueWithMeridiem);\n        }\n      case 'minutes':\n        {\n          const dateWithNewMinutes = utils.setMinutes(valueOrReferenceDate, rawValue);\n          const start = utils.setSeconds(dateWithNewMinutes, 0);\n          const end = utils.setSeconds(dateWithNewMinutes, 59);\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue, minutesStep);\n        }\n      case 'seconds':\n        {\n          const dateWithNewSeconds = utils.setSeconds(valueOrReferenceDate, rawValue);\n          const start = dateWithNewSeconds;\n          const end = dateWithNewSeconds;\n          return !containsValidTime({\n            start,\n            end\n          }) || !isValidValue(rawValue);\n        }\n      default:\n        throw new Error('not supported');\n    }\n  }, [ampm, valueOrReferenceDate, disableIgnoringDatePartForTimeValidation, maxTime, meridiemMode, minTime, minutesStep, shouldDisableTime, utils, disableFuture, disablePast, now, views]);\n  const buildViewProps = React.useCallback(viewToBuild => {\n    switch (viewToBuild) {\n      case 'hours':\n        {\n          return {\n            onChange: hours => {\n              const valueWithMeridiem = convertValueToMeridiem(hours, meridiemMode, ampm);\n              setValueAndGoToNextView(utils.setHours(valueOrReferenceDate, valueWithMeridiem), 'finish', 'hours');\n            },\n            items: getHourSectionOptions({\n              now,\n              value,\n              ampm,\n              utils,\n              isDisabled: hours => isTimeDisabled(hours, 'hours'),\n              timeStep: timeSteps.hours,\n              resolveAriaLabel: translations.hoursClockNumberText,\n              valueOrReferenceDate\n            })\n          };\n        }\n      case 'minutes':\n        {\n          return {\n            onChange: minutes => {\n              setValueAndGoToNextView(utils.setMinutes(valueOrReferenceDate, minutes), 'finish', 'minutes');\n            },\n            items: getTimeSectionOptions({\n              value: utils.getMinutes(valueOrReferenceDate),\n              utils,\n              isDisabled: minutes => isTimeDisabled(minutes, 'minutes'),\n              resolveLabel: minutes => utils.format(utils.setMinutes(now, minutes), 'minutes'),\n              timeStep: timeSteps.minutes,\n              hasValue: !!value,\n              resolveAriaLabel: translations.minutesClockNumberText\n            })\n          };\n        }\n      case 'seconds':\n        {\n          return {\n            onChange: seconds => {\n              setValueAndGoToNextView(utils.setSeconds(valueOrReferenceDate, seconds), 'finish', 'seconds');\n            },\n            items: getTimeSectionOptions({\n              value: utils.getSeconds(valueOrReferenceDate),\n              utils,\n              isDisabled: seconds => isTimeDisabled(seconds, 'seconds'),\n              resolveLabel: seconds => utils.format(utils.setSeconds(now, seconds), 'seconds'),\n              timeStep: timeSteps.seconds,\n              hasValue: !!value,\n              resolveAriaLabel: translations.secondsClockNumberText\n            })\n          };\n        }\n      case 'meridiem':\n        {\n          const amLabel = formatMeridiem(utils, 'am');\n          const pmLabel = formatMeridiem(utils, 'pm');\n          return {\n            onChange: handleMeridiemChange,\n            items: [{\n              value: 'am',\n              label: amLabel,\n              isSelected: () => !!value && meridiemMode === 'am',\n              isFocused: () => !!valueOrReferenceDate && meridiemMode === 'am',\n              ariaLabel: amLabel\n            }, {\n              value: 'pm',\n              label: pmLabel,\n              isSelected: () => !!value && meridiemMode === 'pm',\n              isFocused: () => !!valueOrReferenceDate && meridiemMode === 'pm',\n              ariaLabel: pmLabel\n            }]\n          };\n        }\n      default:\n        throw new Error(`Unknown view: ${viewToBuild} found.`);\n    }\n  }, [now, value, ampm, utils, timeSteps.hours, timeSteps.minutes, timeSteps.seconds, translations.hoursClockNumberText, translations.minutesClockNumberText, translations.secondsClockNumberText, meridiemMode, setValueAndGoToNextView, valueOrReferenceDate, isTimeDisabled, handleMeridiemChange]);\n  const viewsToRender = React.useMemo(() => {\n    if (!isRtl) {\n      return views;\n    }\n    const digitViews = views.filter(v => v !== 'meridiem');\n    digitViews.reverse();\n    if (views.includes('meridiem')) {\n      digitViews.push('meridiem');\n    }\n    return digitViews;\n  }, [isRtl, views]);\n  const viewTimeOptions = React.useMemo(() => {\n    return views.reduce((result, currentView) => {\n      return _extends({}, result, {\n        [currentView]: buildViewProps(currentView)\n      });\n    }, {});\n  }, [views, buildViewProps]);\n  const {\n    ownerState\n  } = usePickerPrivateContext();\n  const classes = useUtilityClasses(classesProp);\n  return /*#__PURE__*/_jsx(MultiSectionDigitalClockRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    role: \"group\"\n  }, other, {\n    children: viewsToRender.map(timeView => /*#__PURE__*/_jsx(MultiSectionDigitalClockSection, {\n      items: viewTimeOptions[timeView].items,\n      onChange: viewTimeOptions[timeView].onChange,\n      active: view === timeView,\n      autoFocus: autoFocus ?? focusedView === timeView,\n      disabled: disabled,\n      readOnly: readOnly,\n      slots: slots,\n      slotProps: slotProps,\n      skipDisabled: skipDisabled,\n      \"aria-label\": translations.selectViewText(timeView)\n    }, timeView))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MultiSectionDigitalClock.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default utils.is12HourCycleInCurrentLocale()\n   */\n  ampm: PropTypes.bool,\n  /**\n   * If `true`, the main element is focused during the first mount.\n   * This main element is:\n   * - the element chosen by the visible view if any (i.e: the selected day on the `day` view).\n   * - the `input` element if there is a field rendered.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true`, the component is disabled.\n   * When disabled, the value cannot be changed and no interaction is possible.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * Controlled focused view.\n   */\n  focusedView: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Maximal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  maxTime: PropTypes.object,\n  /**\n   * Minimal selectable time.\n   * The date part of the object will be ignored unless `props.disableIgnoringDatePartForTimeValidation === true`.\n   */\n  minTime: PropTypes.object,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Callback fired when the value changes.\n   * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n   * @template TView The view type. Will be one of date or time views.\n   * @param {TValue} value The new value.\n   * @param {PickerSelectionState | undefined} selectionState Indicates if the date selection is complete.\n   * @param {TView | undefined} selectedView Indicates the view in which the selection has been made.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired on focused view change.\n   * @template TView\n   * @param {TView} view The new view to focus or not.\n   * @param {boolean} hasFocus `true` if the view should be focused.\n   */\n  onFocusedViewChange: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @template TView\n   * @param {TView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * The default visible view.\n   * Used when the component view is not controlled.\n   * Must be a valid option from `views` list.\n   */\n  openTo: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * If `true`, the component is read-only.\n   * When read-only, the value cannot be changed but the user can interact with the interface.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid time using the validation props, except callbacks such as `shouldDisableTime`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific time.\n   * @param {PickerValidDate} value The value to check.\n   * @param {TimeView} view The clock type of the timeValue.\n   * @returns {boolean} If `true` the time will be disabled.\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * If `true`, disabled digital clock items will not be rendered.\n   * @default false\n   */\n  skipDisabled: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overrideable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The time steps between two time unit options.\n   * For example, if `timeStep.minutes = 8`, then the available minute options will be `[0, 8, 16, 24, 32, 40, 48, 56]`.\n   * @default{ hours: 1, minutes: 5, seconds: 5 }\n   */\n  timeSteps: PropTypes.shape({\n    hours: PropTypes.number,\n    minutes: PropTypes.number,\n    seconds: PropTypes.number\n  }),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * The visible view.\n   * Used when the component view is controlled.\n   * Must be a valid option from `views` list.\n   */\n  view: PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']),\n  /**\n   * Available views.\n   * @default ['hours', 'minutes']\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['hours', 'meridiem', 'minutes', 'seconds']).isRequired)\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,0CAA0C,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,EAAE,mBAAmB,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,qBAAqB,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;AACna,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,QAAQ,EAAEC,MAAM,QAAQ,gCAAgC;AACjE,SAASC,sBAAsB,EAAEC,2BAA2B,QAAQ,kCAAkC;AACtG,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,eAAe,QAAQ,0CAA0C;AAC1E,SAASC,cAAc,QAAQ,iDAAiD;AAChF,SAASC,uCAAuC,QAAQ,sCAAsC;AAC9F,SAASC,+BAA+B,QAAQ,sCAAsC;AACtF,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,qCAAqC;AAClG,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,OAAO,IAAI;EACnC,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOvB,cAAc,CAACsB,KAAK,EAAEb,uCAAuC,EAAEY,OAAO,CAAC;AAChF,CAAC;AACD,MAAMG,4BAA4B,GAAG3B,MAAM,CAACW,cAAc,EAAE;EAC1DiB,IAAI,EAAE,6BAA6B;EACnCC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC;EACFC;AACF,CAAC,MAAM;EACLC,aAAa,EAAE,KAAK;EACpBC,KAAK,EAAE,MAAM;EACbC,YAAY,EAAE,aAAa,CAACH,KAAK,CAACI,IAAI,IAAIJ,KAAK,EAAEK,OAAO,CAACC,OAAO;AAClE,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAG,aAAazC,KAAK,CAAC0C,UAAU,CAAC,SAASD,wBAAwBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACpH,MAAMC,KAAK,GAAGpC,QAAQ,CAAC,CAAC;EACxB,MAAMqC,KAAK,GAAG3C,MAAM,CAAC,CAAC;EACtB,MAAM4C,KAAK,GAAG1C,aAAa,CAAC;IAC1B0C,KAAK,EAAEJ,OAAO;IACdX,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFgB,IAAI,GAAGH,KAAK,CAACI,4BAA4B,CAAC,CAAC;MAC3CC,SAAS,EAAEC,WAAW;MACtBC,SAAS;MACTvB,KAAK;MACLwB,SAAS;MACTC,KAAK,EAAEC,SAAS;MAChBC,YAAY;MACZC,aAAa,EAAEC,iBAAiB;MAChCC,wCAAwC,GAAG,KAAK;MAChDC,OAAO;MACPC,OAAO;MACPC,aAAa;MACbC,WAAW;MACXC,WAAW,GAAG,CAAC;MACfC,iBAAiB;MACjBC,QAAQ;MACRC,IAAI,EAAEC,MAAM;MACZC,KAAK,EAAEC,OAAO,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;MACrCC,MAAM;MACNC,YAAY;MACZC,WAAW,EAAEC,aAAa;MAC1BC,mBAAmB;MACnBC,SAAS;MACThD,OAAO,EAAEiD,WAAW;MACpBC,QAAQ;MACRC,QAAQ;MACRC,YAAY,GAAG,KAAK;MACpBC,QAAQ,EAAEC;IACZ,CAAC,GAAGnC,KAAK;IACToC,KAAK,GAAGrF,6BAA6B,CAACiD,KAAK,EAAEhD,SAAS,CAAC;EACzD,MAAM;IACJuD,KAAK;IACL8B,iBAAiB,EAAEC,oBAAoB;IACvCJ;EACF,CAAC,GAAG7D,kBAAkB,CAAC;IACrBY,IAAI,EAAE,0BAA0B;IAChCiD,QAAQ,EAAEC,YAAY;IACtB5B,KAAK,EAAEC,SAAS;IAChBC,YAAY;IACZC,aAAa,EAAEC,iBAAiB;IAChCQ,QAAQ;IACRoB,YAAY,EAAEjE;EAChB,CAAC,CAAC;EACF,MAAMkE,YAAY,GAAG/E,qBAAqB,CAAC,CAAC;EAC5C,MAAMgF,GAAG,GAAG9E,MAAM,CAACuE,QAAQ,CAAC;EAC5B,MAAM/B,SAAS,GAAGlD,KAAK,CAACyF,OAAO,CAAC,MAAM5F,QAAQ,CAAC;IAC7C6F,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE;EACX,CAAC,EAAEzC,WAAW,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAC/B,MAAM0C,oBAAoB,GAAGvE,qBAAqB,CAAC;IACjDgC,KAAK;IACLG,aAAa,EAAEC,iBAAiB;IAChCb,KAAK;IACLE,KAAK;IACLkC;EACF,CAAC,CAAC;EACF,MAAMG,iBAAiB,GAAG9E,gBAAgB,CAAC,CAACwF,QAAQ,EAAEC,cAAc,EAAEC,YAAY,KAAKX,oBAAoB,CAACS,QAAQ,EAAEC,cAAc,EAAEC,YAAY,CAAC,CAAC;EACpJ,MAAM3B,KAAK,GAAGrE,KAAK,CAACyF,OAAO,CAAC,MAAM;IAChC,IAAI,CAACzC,IAAI,IAAI,CAACsB,OAAO,CAAC2B,QAAQ,CAAC,OAAO,CAAC,EAAE;MACvC,OAAO3B,OAAO;IAChB;IACA,OAAOA,OAAO,CAAC2B,QAAQ,CAAC,UAAU,CAAC,GAAG3B,OAAO,GAAG,CAAC,GAAGA,OAAO,EAAE,UAAU,CAAC;EAC1E,CAAC,EAAE,CAACtB,IAAI,EAAEsB,OAAO,CAAC,CAAC;EACnB,MAAM;IACJH,IAAI;IACJ+B,uBAAuB;IACvBzB;EACF,CAAC,GAAG5D,QAAQ,CAAC;IACXsD,IAAI,EAAEC,MAAM;IACZC,KAAK;IACLE,MAAM;IACNC,YAAY;IACZN,QAAQ,EAAEkB,iBAAiB;IAC3BX,WAAW,EAAEC,aAAa;IAC1BC;EACF,CAAC,CAAC;EACF,MAAMwB,yBAAyB,GAAG7F,gBAAgB,CAACwF,QAAQ,IAAI;IAC7DI,uBAAuB,CAACJ,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;EACzD,CAAC,CAAC;EACF,MAAM;IACJM,YAAY;IACZC;EACF,CAAC,GAAGvF,eAAe,CAAC+E,oBAAoB,EAAE7C,IAAI,EAAEmD,yBAAyB,EAAE,QAAQ,CAAC;EACpF,MAAMG,cAAc,GAAGtG,KAAK,CAACuG,WAAW,CAAC,CAACC,QAAQ,EAAEC,QAAQ,KAAK;IAC/D,MAAMC,OAAO,GAAG9F,2BAA2B,CAAC+C,wCAAwC,EAAEd,KAAK,CAAC;IAC5F,MAAM8D,kBAAkB,GAAGF,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,SAAS,IAAIpC,KAAK,CAAC4B,QAAQ,CAAC,SAAS,CAAC;IACtG,MAAMW,iBAAiB,GAAGA,CAAC;MACzBC,KAAK;MACLC;IACF,CAAC,KAAK;MACJ,IAAIjD,OAAO,IAAI6C,OAAO,CAAC7C,OAAO,EAAEiD,GAAG,CAAC,EAAE;QACpC,OAAO,KAAK;MACd;MACA,IAAIlD,OAAO,IAAI8C,OAAO,CAACG,KAAK,EAAEjD,OAAO,CAAC,EAAE;QACtC,OAAO,KAAK;MACd;MACA,IAAIE,aAAa,IAAI4C,OAAO,CAACG,KAAK,EAAErB,GAAG,CAAC,EAAE;QACxC,OAAO,KAAK;MACd;MACA,IAAIzB,WAAW,IAAI2C,OAAO,CAAClB,GAAG,EAAEmB,kBAAkB,GAAGG,GAAG,GAAGD,KAAK,CAAC,EAAE;QACjE,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;IACD,MAAME,YAAY,GAAGA,CAACC,SAAS,EAAEC,IAAI,GAAG,CAAC,KAAK;MAC5C,IAAID,SAAS,GAAGC,IAAI,KAAK,CAAC,EAAE;QAC1B,OAAO,KAAK;MACd;MACA,IAAIhD,iBAAiB,EAAE;QACrB,QAAQwC,QAAQ;UACd,KAAK,OAAO;YACV,OAAO,CAACxC,iBAAiB,CAACpB,KAAK,CAACqE,QAAQ,CAACrB,oBAAoB,EAAEmB,SAAS,CAAC,EAAE,OAAO,CAAC;UACrF,KAAK,SAAS;YACZ,OAAO,CAAC/C,iBAAiB,CAACpB,KAAK,CAACsE,UAAU,CAACtB,oBAAoB,EAAEmB,SAAS,CAAC,EAAE,SAAS,CAAC;UACzF,KAAK,SAAS;YACZ,OAAO,CAAC/C,iBAAiB,CAACpB,KAAK,CAACuE,UAAU,CAACvB,oBAAoB,EAAEmB,SAAS,CAAC,EAAE,SAAS,CAAC;UACzF;YACE,OAAO,KAAK;QAChB;MACF;MACA,OAAO,IAAI;IACb,CAAC;IACD,QAAQP,QAAQ;MACd,KAAK,OAAO;QACV;UACE,MAAMY,iBAAiB,GAAG1G,sBAAsB,CAAC6F,QAAQ,EAAEJ,YAAY,EAAEpD,IAAI,CAAC;UAC9E,MAAMsE,gBAAgB,GAAGzE,KAAK,CAACqE,QAAQ,CAACrB,oBAAoB,EAAEwB,iBAAiB,CAAC;UAChF,IAAIxE,KAAK,CAAC0E,QAAQ,CAACD,gBAAgB,CAAC,KAAKD,iBAAiB,EAAE;YAC1D,OAAO,IAAI;UACb;UACA,MAAMR,KAAK,GAAGhE,KAAK,CAACuE,UAAU,CAACvE,KAAK,CAACsE,UAAU,CAACG,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UACxE,MAAMR,GAAG,GAAGjE,KAAK,CAACuE,UAAU,CAACvE,KAAK,CAACsE,UAAU,CAACG,gBAAgB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;UACxE,OAAO,CAACV,iBAAiB,CAAC;YACxBC,KAAK;YACLC;UACF,CAAC,CAAC,IAAI,CAACC,YAAY,CAACM,iBAAiB,CAAC;QACxC;MACF,KAAK,SAAS;QACZ;UACE,MAAMG,kBAAkB,GAAG3E,KAAK,CAACsE,UAAU,CAACtB,oBAAoB,EAAEW,QAAQ,CAAC;UAC3E,MAAMK,KAAK,GAAGhE,KAAK,CAACuE,UAAU,CAACI,kBAAkB,EAAE,CAAC,CAAC;UACrD,MAAMV,GAAG,GAAGjE,KAAK,CAACuE,UAAU,CAACI,kBAAkB,EAAE,EAAE,CAAC;UACpD,OAAO,CAACZ,iBAAiB,CAAC;YACxBC,KAAK;YACLC;UACF,CAAC,CAAC,IAAI,CAACC,YAAY,CAACP,QAAQ,EAAExC,WAAW,CAAC;QAC5C;MACF,KAAK,SAAS;QACZ;UACE,MAAMyD,kBAAkB,GAAG5E,KAAK,CAACuE,UAAU,CAACvB,oBAAoB,EAAEW,QAAQ,CAAC;UAC3E,MAAMK,KAAK,GAAGY,kBAAkB;UAChC,MAAMX,GAAG,GAAGW,kBAAkB;UAC9B,OAAO,CAACb,iBAAiB,CAAC;YACxBC,KAAK;YACLC;UACF,CAAC,CAAC,IAAI,CAACC,YAAY,CAACP,QAAQ,CAAC;QAC/B;MACF;QACE,MAAM,IAAIkB,KAAK,CAAC,eAAe,CAAC;IACpC;EACF,CAAC,EAAE,CAAC1E,IAAI,EAAE6C,oBAAoB,EAAElC,wCAAwC,EAAEC,OAAO,EAAEwC,YAAY,EAAEvC,OAAO,EAAEG,WAAW,EAAEC,iBAAiB,EAAEpB,KAAK,EAAEiB,aAAa,EAAEC,WAAW,EAAEyB,GAAG,EAAEnB,KAAK,CAAC,CAAC;EACzL,MAAMsD,cAAc,GAAG3H,KAAK,CAACuG,WAAW,CAACqB,WAAW,IAAI;IACtD,QAAQA,WAAW;MACjB,KAAK,OAAO;QACV;UACE,OAAO;YACL1D,QAAQ,EAAEwB,KAAK,IAAI;cACjB,MAAM2B,iBAAiB,GAAG1G,sBAAsB,CAAC+E,KAAK,EAAEU,YAAY,EAAEpD,IAAI,CAAC;cAC3EkD,uBAAuB,CAACrD,KAAK,CAACqE,QAAQ,CAACrB,oBAAoB,EAAEwB,iBAAiB,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC;YACrG,CAAC;YACDQ,KAAK,EAAE3G,qBAAqB,CAAC;cAC3BsE,GAAG;cACHlC,KAAK;cACLN,IAAI;cACJH,KAAK;cACLiF,UAAU,EAAEpC,KAAK,IAAIY,cAAc,CAACZ,KAAK,EAAE,OAAO,CAAC;cACnDqC,QAAQ,EAAE7E,SAAS,CAACwC,KAAK;cACzBsC,gBAAgB,EAAEzC,YAAY,CAAC0C,oBAAoB;cACnDpC;YACF,CAAC;UACH,CAAC;QACH;MACF,KAAK,SAAS;QACZ;UACE,OAAO;YACL3B,QAAQ,EAAEyB,OAAO,IAAI;cACnBO,uBAAuB,CAACrD,KAAK,CAACsE,UAAU,CAACtB,oBAAoB,EAAEF,OAAO,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC;YAC/F,CAAC;YACDkC,KAAK,EAAE1G,qBAAqB,CAAC;cAC3BmC,KAAK,EAAET,KAAK,CAACqF,UAAU,CAACrC,oBAAoB,CAAC;cAC7ChD,KAAK;cACLiF,UAAU,EAAEnC,OAAO,IAAIW,cAAc,CAACX,OAAO,EAAE,SAAS,CAAC;cACzDwC,YAAY,EAAExC,OAAO,IAAI9C,KAAK,CAACuF,MAAM,CAACvF,KAAK,CAACsE,UAAU,CAAC3B,GAAG,EAAEG,OAAO,CAAC,EAAE,SAAS,CAAC;cAChFoC,QAAQ,EAAE7E,SAAS,CAACyC,OAAO;cAC3B0C,QAAQ,EAAE,CAAC,CAAC/E,KAAK;cACjB0E,gBAAgB,EAAEzC,YAAY,CAAC+C;YACjC,CAAC;UACH,CAAC;QACH;MACF,KAAK,SAAS;QACZ;UACE,OAAO;YACLpE,QAAQ,EAAE0B,OAAO,IAAI;cACnBM,uBAAuB,CAACrD,KAAK,CAACuE,UAAU,CAACvB,oBAAoB,EAAED,OAAO,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC;YAC/F,CAAC;YACDiC,KAAK,EAAE1G,qBAAqB,CAAC;cAC3BmC,KAAK,EAAET,KAAK,CAAC0F,UAAU,CAAC1C,oBAAoB,CAAC;cAC7ChD,KAAK;cACLiF,UAAU,EAAElC,OAAO,IAAIU,cAAc,CAACV,OAAO,EAAE,SAAS,CAAC;cACzDuC,YAAY,EAAEvC,OAAO,IAAI/C,KAAK,CAACuF,MAAM,CAACvF,KAAK,CAACuE,UAAU,CAAC5B,GAAG,EAAEI,OAAO,CAAC,EAAE,SAAS,CAAC;cAChFmC,QAAQ,EAAE7E,SAAS,CAAC0C,OAAO;cAC3ByC,QAAQ,EAAE,CAAC,CAAC/E,KAAK;cACjB0E,gBAAgB,EAAEzC,YAAY,CAACiD;YACjC,CAAC;UACH,CAAC;QACH;MACF,KAAK,UAAU;QACb;UACE,MAAMC,OAAO,GAAGlH,cAAc,CAACsB,KAAK,EAAE,IAAI,CAAC;UAC3C,MAAM6F,OAAO,GAAGnH,cAAc,CAACsB,KAAK,EAAE,IAAI,CAAC;UAC3C,OAAO;YACLqB,QAAQ,EAAEmC,oBAAoB;YAC9BwB,KAAK,EAAE,CAAC;cACNvE,KAAK,EAAE,IAAI;cACXqF,KAAK,EAAEF,OAAO;cACdG,UAAU,EAAEA,CAAA,KAAM,CAAC,CAACtF,KAAK,IAAI8C,YAAY,KAAK,IAAI;cAClDyC,SAAS,EAAEA,CAAA,KAAM,CAAC,CAAChD,oBAAoB,IAAIO,YAAY,KAAK,IAAI;cAChE0C,SAAS,EAAEL;YACb,CAAC,EAAE;cACDnF,KAAK,EAAE,IAAI;cACXqF,KAAK,EAAED,OAAO;cACdE,UAAU,EAAEA,CAAA,KAAM,CAAC,CAACtF,KAAK,IAAI8C,YAAY,KAAK,IAAI;cAClDyC,SAAS,EAAEA,CAAA,KAAM,CAAC,CAAChD,oBAAoB,IAAIO,YAAY,KAAK,IAAI;cAChE0C,SAAS,EAAEJ;YACb,CAAC;UACH,CAAC;QACH;MACF;QACE,MAAM,IAAIhB,KAAK,CAAC,iBAAiBE,WAAW,SAAS,CAAC;IAC1D;EACF,CAAC,EAAE,CAACpC,GAAG,EAAElC,KAAK,EAAEN,IAAI,EAAEH,KAAK,EAAEK,SAAS,CAACwC,KAAK,EAAExC,SAAS,CAACyC,OAAO,EAAEzC,SAAS,CAAC0C,OAAO,EAAEL,YAAY,CAAC0C,oBAAoB,EAAE1C,YAAY,CAAC+C,sBAAsB,EAAE/C,YAAY,CAACiD,sBAAsB,EAAEpC,YAAY,EAAEF,uBAAuB,EAAEL,oBAAoB,EAAES,cAAc,EAAED,oBAAoB,CAAC,CAAC;EACpS,MAAM0C,aAAa,GAAG/I,KAAK,CAACyF,OAAO,CAAC,MAAM;IACxC,IAAI,CAAC3C,KAAK,EAAE;MACV,OAAOuB,KAAK;IACd;IACA,MAAM2E,UAAU,GAAG3E,KAAK,CAAC4E,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK,UAAU,CAAC;IACtDF,UAAU,CAACG,OAAO,CAAC,CAAC;IACpB,IAAI9E,KAAK,CAAC4B,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC9B+C,UAAU,CAACI,IAAI,CAAC,UAAU,CAAC;IAC7B;IACA,OAAOJ,UAAU;EACnB,CAAC,EAAE,CAAClG,KAAK,EAAEuB,KAAK,CAAC,CAAC;EAClB,MAAMgF,eAAe,GAAGrJ,KAAK,CAACyF,OAAO,CAAC,MAAM;IAC1C,OAAOpB,KAAK,CAACiF,MAAM,CAAC,CAACC,MAAM,EAAEC,WAAW,KAAK;MAC3C,OAAO3J,QAAQ,CAAC,CAAC,CAAC,EAAE0J,MAAM,EAAE;QAC1B,CAACC,WAAW,GAAG7B,cAAc,CAAC6B,WAAW;MAC3C,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,EAAE,CAACnF,KAAK,EAAEsD,cAAc,CAAC,CAAC;EAC3B,MAAM;IACJ8B;EACF,CAAC,GAAGjI,uBAAuB,CAAC,CAAC;EAC7B,MAAMI,OAAO,GAAGD,iBAAiB,CAACkD,WAAW,CAAC;EAC9C,OAAO,aAAanD,IAAI,CAACK,4BAA4B,EAAElC,QAAQ,CAAC;IAC9D+C,GAAG,EAAEA,GAAG;IACRgC,SAAS,EAAE3E,IAAI,CAAC2B,OAAO,CAACE,IAAI,EAAE8C,SAAS,CAAC;IACxC6E,UAAU,EAAEA,UAAU;IACtBC,IAAI,EAAE;EACR,CAAC,EAAEvE,KAAK,EAAE;IACRwE,QAAQ,EAAEZ,aAAa,CAACa,GAAG,CAACC,QAAQ,IAAI,aAAanI,IAAI,CAACT,+BAA+B,EAAE;MACzF4G,KAAK,EAAEwB,eAAe,CAACQ,QAAQ,CAAC,CAAChC,KAAK;MACtC3D,QAAQ,EAAEmF,eAAe,CAACQ,QAAQ,CAAC,CAAC3F,QAAQ;MAC5C4F,MAAM,EAAE3F,IAAI,KAAK0F,QAAQ;MACzBzG,SAAS,EAAEA,SAAS,IAAIqB,WAAW,KAAKoF,QAAQ;MAChD/E,QAAQ,EAAEA,QAAQ;MAClBC,QAAQ,EAAEA,QAAQ;MAClBlD,KAAK,EAAEA,KAAK;MACZwB,SAAS,EAAEA,SAAS;MACpB2B,YAAY,EAAEA,YAAY;MAC1B,YAAY,EAAEO,YAAY,CAACwE,cAAc,CAACF,QAAQ;IACpD,CAAC,EAAEA,QAAQ,CAAC;EACd,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzH,wBAAwB,CAAC0H,SAAS,GAAG;EAC3E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEnH,IAAI,EAAE9C,SAAS,CAACkK,IAAI;EACpB;AACF;AACA;AACA;AACA;AACA;EACEhH,SAAS,EAAElD,SAAS,CAACkK,IAAI;EACzB;AACF;AACA;EACExI,OAAO,EAAE1B,SAAS,CAACmK,MAAM;EACzBzF,SAAS,EAAE1E,SAAS,CAACoK,MAAM;EAC3B;AACF;AACA;AACA;EACE9G,YAAY,EAAEtD,SAAS,CAACmK,MAAM;EAC9B;AACF;AACA;AACA;AACA;EACEvF,QAAQ,EAAE5E,SAAS,CAACkK,IAAI;EACxB;AACF;AACA;AACA;EACEtG,aAAa,EAAE5D,SAAS,CAACkK,IAAI;EAC7B;AACF;AACA;AACA;EACEzG,wCAAwC,EAAEzD,SAAS,CAACkK,IAAI;EACxD;AACF;AACA;AACA;EACErG,WAAW,EAAE7D,SAAS,CAACkK,IAAI;EAC3B;AACF;AACA;EACE3F,WAAW,EAAEvE,SAAS,CAACqK,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACzE;AACF;AACA;AACA;EACE3G,OAAO,EAAE1D,SAAS,CAACmK,MAAM;EACzB;AACF;AACA;AACA;EACExG,OAAO,EAAE3D,SAAS,CAACmK,MAAM;EACzB;AACF;AACA;AACA;EACErG,WAAW,EAAE9D,SAAS,CAACsK,MAAM;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEtG,QAAQ,EAAEhE,SAAS,CAACuK,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACE9F,mBAAmB,EAAEzE,SAAS,CAACuK,IAAI;EACnC;AACF;AACA;AACA;AACA;EACEjG,YAAY,EAAEtE,SAAS,CAACuK,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACElG,MAAM,EAAErE,SAAS,CAACqK,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACpE;AACF;AACA;AACA;AACA;EACExF,QAAQ,EAAE7E,SAAS,CAACkK,IAAI;EACxB;AACF;AACA;AACA;EACE3G,aAAa,EAAEvD,SAAS,CAACmK,MAAM;EAC/B;AACF;AACA;AACA;AACA;AACA;EACEpG,iBAAiB,EAAE/D,SAAS,CAACuK,IAAI;EACjC;AACF;AACA;AACA;EACEzF,YAAY,EAAE9E,SAAS,CAACkK,IAAI;EAC5B;AACF;AACA;AACA;EACE/G,SAAS,EAAEnD,SAAS,CAACmK,MAAM;EAC3B;AACF;AACA;AACA;EACExI,KAAK,EAAE3B,SAAS,CAACmK,MAAM;EACvB;AACF;AACA;EACEK,EAAE,EAAExK,SAAS,CAACyK,SAAS,CAAC,CAACzK,SAAS,CAAC0K,OAAO,CAAC1K,SAAS,CAACyK,SAAS,CAAC,CAACzK,SAAS,CAACuK,IAAI,EAAEvK,SAAS,CAACmK,MAAM,EAAEnK,SAAS,CAACkK,IAAI,CAAC,CAAC,CAAC,EAAElK,SAAS,CAACuK,IAAI,EAAEvK,SAAS,CAACmK,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;EACEnH,SAAS,EAAEhD,SAAS,CAAC2K,KAAK,CAAC;IACzBnF,KAAK,EAAExF,SAAS,CAACsK,MAAM;IACvB7E,OAAO,EAAEzF,SAAS,CAACsK,MAAM;IACzB5E,OAAO,EAAE1F,SAAS,CAACsK;EACrB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;EACEvF,QAAQ,EAAE/E,SAAS,CAACoK,MAAM;EAC1B;AACF;AACA;AACA;EACEhH,KAAK,EAAEpD,SAAS,CAACmK,MAAM;EACvB;AACF;AACA;AACA;AACA;EACElG,IAAI,EAAEjE,SAAS,CAACqK,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EAClE;AACF;AACA;AACA;EACElG,KAAK,EAAEnE,SAAS,CAAC0K,OAAO,CAAC1K,SAAS,CAACqK,KAAK,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAACO,UAAU;AAClG,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}