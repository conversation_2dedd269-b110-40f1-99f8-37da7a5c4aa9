{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\nconst dateFormats = {\n  full: \"d MMMM y EEEE\",\n  long: \"d MMMM y\",\n  medium: \"d MMM y\",\n  short: \"dd.MM.yyyy\"\n};\nconst timeFormats = {\n  full: \"HH:mm:ss zzzz\",\n  long: \"HH:mm:ss z\",\n  medium: \"HH:mm:ss\",\n  short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n  full: \"{{date}} 'saat' {{time}}\",\n  long: \"{{date}} 'saat' {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\"\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/tr/_lib/formatLong.js"], "sourcesContent": ["import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\nconst dateFormats = {\n  full: \"d MMMM y EEEE\",\n  long: \"d MMMM y\",\n  medium: \"d MMM y\",\n  short: \"dd.MM.yyyy\",\n};\n\nconst timeFormats = {\n  full: \"HH:mm:ss zzzz\",\n  long: \"HH:mm:ss z\",\n  medium: \"HH:mm:ss\",\n  short: \"HH:mm\",\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} 'saat' {{time}}\",\n  long: \"{{date}} 'saat' {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iCAAiC;AAEnE,MAAMC,WAAW,GAAG;EAClBC,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,WAAW,GAAG;EAClBJ,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE;AACT,CAAC;AAED,MAAME,eAAe,GAAG;EACtBL,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,0BAA0B;EAChCC,MAAM,EAAE,oBAAoB;EAC5BC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMG,UAAU,GAAG;EACxBC,IAAI,EAAET,iBAAiB,CAAC;IACtBU,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,IAAI,EAAEZ,iBAAiB,CAAC;IACtBU,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFE,QAAQ,EAAEb,iBAAiB,CAAC;IAC1BU,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}