{"ast": null, "code": "'use client';\n\nimport { useField } from \"../internals/hooks/useField/index.js\";\nimport { useDateManager } from \"../managers/index.js\";\nexport const useDateField = props => {\n  const manager = useDateManager(props);\n  return useField({\n    manager,\n    props\n  });\n};", "map": {"version": 3, "names": ["useField", "useDateManager", "useDateField", "props", "manager"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/DateField/useDateField.js"], "sourcesContent": ["'use client';\n\nimport { useField } from \"../internals/hooks/useField/index.js\";\nimport { useDateManager } from \"../managers/index.js\";\nexport const useDateField = props => {\n  const manager = useDateManager(props);\n  return useField({\n    manager,\n    props\n  });\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,sCAAsC;AAC/D,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAO,MAAMC,YAAY,GAAGC,KAAK,IAAI;EACnC,MAAMC,OAAO,GAAGH,cAAc,CAACE,KAAK,CAAC;EACrC,OAAOH,QAAQ,CAAC;IACdI,OAAO;IACPD;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}