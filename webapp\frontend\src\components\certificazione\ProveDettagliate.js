import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Science as ScienceIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { it } from 'date-fns/locale';

import proveDettagliateService from '../../services/proveDettagliateService';

const ProveDettagliate = forwardRef(({ cantiereId, certificazioneId, onError, onSuccess }, ref) => {
  const [prove, setProve] = useState([]);
  const [tipiProva, setTipiProva] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedProva, setSelectedProva] = useState(null);
  const [formData, setFormData] = useState({
    tipo_prova: '',
    data_prova: new Date(),
    operatore: '',
    condizioni_ambientali: {
      temperatura: '',
      umidita: ''
    },
    risultati: {},
    valori_misurati: {},
    valori_attesi: {},
    esito: 'CONFORME',
    note_prova: ''
  });

  // Carica i tipi di prova disponibili
  const loadTipiProva = async () => {
    try {
      const data = await proveDettagliateService.getTipiProva();
      setTipiProva(data.tipi_prova || []);
    } catch (error) {
      console.error('Errore nel caricamento tipi prova:', error);
    }
  };

  // Carica le prove della certificazione
  const loadProve = async () => {
    try {
      setLoading(true);
      const data = await proveDettagliateService.getProve(cantiereId, certificazioneId);
      setProve(data);
    } catch (error) {
      onError('Errore nel caricamento delle prove dettagliate');
      console.error('Errore nel caricamento delle prove:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTipiProva();
    if (certificazioneId) {
      loadProve();
    }
  }, [certificazioneId]);

  // Espone i metodi tramite ref
  useImperativeHandle(ref, () => ({
    handleOptionSelect
  }));

  const handleOptionSelect = (option) => {
    switch (option) {
      case 'visualizzaProve':
        loadProve();
        break;
      case 'creaProva':
        resetForm();
        setDialogType('creaProva');
        setOpenDialog(true);
        break;
      case 'modificaProva':
        loadProve();
        setDialogType('selezionaProvaModifica');
        setOpenDialog(true);
        break;
      case 'eliminaProva':
        loadProve();
        setDialogType('selezionaProvaElimina');
        setOpenDialog(true);
        break;
      default:
        break;
    }
  };

  const resetForm = () => {
    setFormData({
      tipo_prova: '',
      data_prova: new Date(),
      operatore: '',
      condizioni_ambientali: {
        temperatura: '',
        umidita: ''
      },
      risultati: {},
      valori_misurati: {},
      valori_attesi: {},
      esito: 'CONFORME',
      note_prova: ''
    });
    setSelectedProva(null);
  };

  const handleFormChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      const submitData = {
        ...formData,
        data_prova: formData.data_prova.toISOString(),
        condizioni_ambientali: {
          temperatura: formData.condizioni_ambientali.temperatura ? 
            parseFloat(formData.condizioni_ambientali.temperatura) : null,
          umidita: formData.condizioni_ambientali.umidita ? 
            parseFloat(formData.condizioni_ambientali.umidita) : null
        }
      };

      if (dialogType === 'creaProva') {
        await proveDettagliateService.createProva(cantiereId, certificazioneId, submitData);
        onSuccess('Prova dettagliata creata con successo');
      } else if (dialogType === 'modificaProva') {
        await proveDettagliateService.updateProva(cantiereId, certificazioneId, selectedProva.id_prova, submitData);
        onSuccess('Prova dettagliata aggiornata con successo');
      }

      setOpenDialog(false);
      loadProve();
    } catch (error) {
      onError('Errore nel salvataggio della prova dettagliata');
      console.error('Errore nel salvataggio:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (prova) => {
    if (window.confirm(`Sei sicuro di voler eliminare la prova ${prova.tipo_prova}?`)) {
      try {
        setLoading(true);
        await proveDettagliateService.deleteProva(cantiereId, certificazioneId, prova.id_prova);
        onSuccess('Prova dettagliata eliminata con successo');
        loadProve();
      } catch (error) {
        onError('Errore nell\'eliminazione della prova dettagliata');
        console.error('Errore nell\'eliminazione:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleEdit = (prova) => {
    setSelectedProva(prova);
    setFormData({
      tipo_prova: prova.tipo_prova || '',
      data_prova: prova.data_prova ? new Date(prova.data_prova) : new Date(),
      operatore: prova.operatore || '',
      condizioni_ambientali: {
        temperatura: prova.condizioni_ambientali?.temperatura || '',
        umidita: prova.condizioni_ambientali?.umidita || ''
      },
      risultati: prova.risultati || {},
      valori_misurati: prova.valori_misurati || {},
      valori_attesi: prova.valori_attesi || {},
      esito: prova.esito || 'CONFORME',
      note_prova: prova.note_prova || ''
    });
    setDialogType('modificaProva');
    setOpenDialog(true);
  };

  const getEsitoColor = (esito) => {
    switch (esito) {
      case 'CONFORME': return 'success';
      case 'NON_CONFORME': return 'error';
      case 'N_A': return 'default';
      default: return 'default';
    }
  };

  const renderProveList = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Tipo Prova</TableCell>
            <TableCell>Data/Ora</TableCell>
            <TableCell>Operatore</TableCell>
            <TableCell>Esito</TableCell>
            <TableCell>Azioni</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {prove.map((prova) => (
            <TableRow key={prova.id_prova}>
              <TableCell>{prova.tipo_prova}</TableCell>
              <TableCell>
                {prova.data_prova ? 
                  new Date(prova.data_prova).toLocaleString('it-IT') : '-'}
              </TableCell>
              <TableCell>{prova.operatore || '-'}</TableCell>
              <TableCell>
                <Chip 
                  label={prova.esito} 
                  color={getEsitoColor(prova.esito)}
                  size="small"
                />
              </TableCell>
              <TableCell>
                <IconButton onClick={() => handleEdit(prova)} size="small">
                  <EditIcon />
                </IconButton>
                <IconButton onClick={() => handleDelete(prova)} size="small">
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderTipoProvaFields = () => {
    switch (formData.tipo_prova) {
      case 'ESAME_VISTA':
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Parametri Esame a Vista
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Conformità Percorso</InputLabel>
                <Select
                  value={formData.risultati.conformita_percorso || ''}
                  onChange={(e) => handleFormChange('risultati.conformita_percorso', e.target.value)}
                >
                  <MenuItem value="OK">OK</MenuItem>
                  <MenuItem value="NON_OK">NON OK</MenuItem>
                  <MenuItem value="N_A">N/A</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Integrità Guaina</InputLabel>
                <Select
                  value={formData.risultati.integrita_guaina || ''}
                  onChange={(e) => handleFormChange('risultati.integrita_guaina', e.target.value)}
                >
                  <MenuItem value="OK">OK</MenuItem>
                  <MenuItem value="NON_OK">NON OK</MenuItem>
                  <MenuItem value="N_A">N/A</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        );
      case 'CONTINUITA':
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Parametri Prova di Continuità
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Resistenza L1 (Ω)"
                type="number"
                value={formData.valori_misurati.resistenza_l1 || ''}
                onChange={(e) => handleFormChange('valori_misurati.resistenza_l1', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Resistenza L2 (Ω)"
                type="number"
                value={formData.valori_misurati.resistenza_l2 || ''}
                onChange={(e) => handleFormChange('valori_misurati.resistenza_l2', e.target.value)}
              />
            </Grid>
          </Grid>
        );
      case 'ISOLAMENTO':
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Parametri Prova di Isolamento
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Tensione di Prova (V)"
                type="number"
                value={formData.valori_misurati.tensione_prova || ''}
                onChange={(e) => handleFormChange('valori_misurati.tensione_prova', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Isolamento L1-L2 (MΩ)"
                type="number"
                value={formData.valori_misurati.isolamento_l1_l2 || ''}
                onChange={(e) => handleFormChange('valori_misurati.isolamento_l1_l2', e.target.value)}
              />
            </Grid>
          </Grid>
        );
      default:
        return null;
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={it}>
      <Box>
        {/* Lista prove */}
        {prove.length > 0 && renderProveList()}

        {/* Dialog per creazione/modifica */}
        <Dialog 
          open={openDialog && (dialogType === 'creaProva' || dialogType === 'modificaProva')} 
          onClose={() => setOpenDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" alignItems="center" gap={1}>
              <ScienceIcon />
              {dialogType === 'creaProva' ? 'Nuova Prova Dettagliata' : 'Modifica Prova Dettagliata'}
            </Box>
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel>Tipo Prova</InputLabel>
                  <Select
                    value={formData.tipo_prova}
                    onChange={(e) => handleFormChange('tipo_prova', e.target.value)}
                  >
                    {tipiProva.map((tipo) => (
                      <MenuItem key={tipo.codice} value={tipo.codice}>
                        {tipo.nome}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <DateTimePicker
                  label="Data e Ora Prova"
                  value={formData.data_prova}
                  onChange={(date) => handleFormChange('data_prova', date)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Operatore"
                  value={formData.operatore}
                  onChange={(e) => handleFormChange('operatore', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Esito</InputLabel>
                  <Select
                    value={formData.esito}
                    onChange={(e) => handleFormChange('esito', e.target.value)}
                  >
                    <MenuItem value="CONFORME">Conforme</MenuItem>
                    <MenuItem value="NON_CONFORME">Non Conforme</MenuItem>
                    <MenuItem value="N_A">N/A</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              {/* Condizioni ambientali */}
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }}>Condizioni Ambientali</Divider>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Temperatura (°C)"
                  type="number"
                  value={formData.condizioni_ambientali.temperatura}
                  onChange={(e) => handleFormChange('condizioni_ambientali.temperatura', e.target.value)}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Umidità (%)"
                  type="number"
                  value={formData.condizioni_ambientali.umidita}
                  onChange={(e) => handleFormChange('condizioni_ambientali.umidita', e.target.value)}
                />
              </Grid>

              {/* Campi specifici per tipo di prova */}
              {formData.tipo_prova && (
                <>
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }}>Parametri Specifici</Divider>
                  </Grid>
                  {renderTipoProvaFields()}
                </>
              )}

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Note"
                  value={formData.note_prova}
                  onChange={(e) => handleFormChange('note_prova', e.target.value)}
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>Annulla</Button>
            <Button onClick={handleSubmit} variant="contained" disabled={loading}>
              {loading ? 'Salvando...' : 'Salva'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
});

export default ProveDettagliate;
