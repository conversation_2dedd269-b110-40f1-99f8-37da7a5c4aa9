{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nexport const IsValidValueContext = /*#__PURE__*/React.createContext(() => true);\n\n/**\n * Returns a function to check if a value is valid according to the validation props passed to the parent Picker.\n */\nexport function useIsValidValue() {\n  return React.useContext(IsValidValueContext);\n}", "map": {"version": 3, "names": ["React", "IsValidValueContext", "createContext", "useIsValidValue", "useContext"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/hooks/useIsValidValue.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nexport const IsValidValueContext = /*#__PURE__*/React.createContext(() => true);\n\n/**\n * Returns a function to check if a value is valid according to the validation props passed to the parent Picker.\n */\nexport function useIsValidValue() {\n  return React.useContext(IsValidValueContext);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,mBAAmB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,MAAM,IAAI,CAAC;;AAE/E;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,OAAOH,KAAK,CAACI,UAAU,CAACH,mBAAmB,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}