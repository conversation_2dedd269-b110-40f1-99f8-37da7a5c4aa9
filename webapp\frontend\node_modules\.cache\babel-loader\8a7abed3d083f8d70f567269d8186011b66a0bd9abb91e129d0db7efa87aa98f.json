{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'ئ‍ۆتكەن' eeee 'دە' p\",\n  yesterday: \"'تۈنۈگۈن دە' p\",\n  today: \"'بۈگۈن دە' p\",\n  tomorrow: \"'ئەتە دە' p\",\n  nextWeek: \"eeee 'دە' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ug/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'ئ‍ۆتكەن' eeee 'دە' p\",\n  yesterday: \"'تۈنۈگۈن دە' p\",\n  today: \"'بۈگۈن دە' p\",\n  tomorrow: \"'ئەتە دە' p\",\n  nextWeek: \"eeee 'دە' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,uBAAuB;EACjCC,SAAS,EAAE,gBAAgB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}