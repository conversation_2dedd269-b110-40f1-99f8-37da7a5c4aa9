{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\BobineFilterableTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, IconButton, Tooltip } from '@mui/material';\nimport { Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Add as AddIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport { REEL_STATES, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per visualizzare la lista delle bobine con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.bobine - Lista delle bobine da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare una bobina\n * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare una bobina\n * @param {Function} props.onViewHistory - Funzione chiamata quando si vuole visualizzare lo storico di una bobina\n * @param {Function} props.onQuickAdd - Funzione chiamata quando si vuole aggiungere rapidamente cavi a una bobina\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BobineFilterableTable = ({\n  bobine = [],\n  loading = false,\n  onFilteredDataChange = null,\n  onEdit = null,\n  onDelete = null,\n  onViewHistory = null,\n  onQuickAdd = null\n}) => {\n  _s();\n  const [filteredBobine, setFilteredBobine] = useState(bobine);\n\n  // Aggiorna i dati filtrati quando cambiano le bobine\n  useEffect(() => {\n    setFilteredBobine(bobine);\n  }, [bobine]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = data => {\n    setFilteredBobine(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Definizione delle colonne\n  const columns = [{\n    field: 'numero_bobina',\n    headerName: 'ID Bobina',\n    dataType: 'text',\n    width: 120,\n    headerStyle: {\n      fontWeight: 'bold'\n    },\n    renderCell: row => /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      sx: {\n        fontSize: '0.95rem',\n        fontWeight: 600\n      },\n      children: row.numero_bobina\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'utility',\n    headerName: 'Utility',\n    dataType: 'text',\n    width: 100,\n    renderCell: row => /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      sx: {\n        fontSize: '0.95rem'\n      },\n      children: row.utility\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'tipologia',\n    headerName: 'Tipologia',\n    dataType: 'text',\n    width: 150,\n    renderCell: row => /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      sx: {\n        fontSize: '0.95rem'\n      },\n      children: row.tipologia\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 9\n    }, this)\n  },\n  // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n  {\n    field: 'sezione',\n    headerName: 'Formazione',\n    dataType: 'text',\n    width: 120,\n    align: 'center',\n    renderCell: row => /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      sx: {\n        fontSize: '0.95rem',\n        textAlign: 'center'\n      },\n      children: row.sezione\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'metri_totali',\n    headerName: 'Metri Totali',\n    dataType: 'number',\n    width: 110,\n    align: 'center',\n    renderCell: row => /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      sx: {\n        fontSize: '0.95rem',\n        textAlign: 'center',\n        fontWeight: 500\n      },\n      children: row.metri_totali ? row.metri_totali.toFixed(1) : '0'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'metri_residui',\n    headerName: 'Metri Residui',\n    dataType: 'number',\n    width: 110,\n    align: 'center',\n    renderCell: row => /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      sx: {\n        fontSize: '0.95rem',\n        textAlign: 'center',\n        fontWeight: 500\n      },\n      children: row.metri_residui ? row.metri_residui.toFixed(1) : '0'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'stato_bobina',\n    headerName: 'Stato',\n    dataType: 'text',\n    renderCell: row => {\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.stato_bobina || 'N/D',\n        size: \"small\",\n        color: getReelStateColor(row.stato_bobina),\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    field: 'ubicazione_bobina',\n    headerName: 'Ubicazione',\n    dataType: 'text'\n  }, {\n    field: 'fornitore',\n    headerName: 'Fornitore',\n    dataType: 'text'\n  }, {\n    field: 'n_DDT',\n    headerName: 'N° DDT',\n    dataType: 'text'\n  }, {\n    field: 'data_DDT',\n    headerName: 'Data DDT',\n    dataType: 'text'\n  }, {\n    field: 'actions',\n    headerName: 'Azioni',\n    disableFilter: true,\n    disableSort: true,\n    align: 'center',\n    renderCell: row => /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center'\n      },\n      children: [onEdit && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onEdit(row),\n        title: \"Modifica bobina\",\n        color: \"primary\",\n        children: /*#__PURE__*/_jsxDEV(EditIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 13\n      }, this), onDelete && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onDelete(row),\n        title: \"Elimina bobina\",\n        color: \"error\",\n        disabled: row.stato_bobina !== 'Disponibile' || row.metri_residui !== row.metri_totali,\n        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 13\n      }, this), onViewHistory && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onViewHistory(row),\n        title: \"Visualizza storico utilizzo\",\n        color: \"info\",\n        children: /*#__PURE__*/_jsxDEV(HistoryIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 13\n      }, this), onQuickAdd && row.stato_bobina !== 'Terminata' && row.stato_bobina !== 'Over' && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Aggiungi cavi a questa bobina\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: () => onQuickAdd(row),\n          color: \"success\",\n          children: /*#__PURE__*/_jsxDEV(AddIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_bobina === REEL_STATES.DISPONIBILE) bgColor = 'rgba(76, 175, 80, 0.1)';else if (row.stato_bobina === REEL_STATES.IN_USO) bgColor = 'rgba(255, 152, 0, 0.1)';else if (row.stato_bobina === REEL_STATES.TERMINATA) bgColor = 'rgba(255, 152, 0, 0.2)';else if (row.stato_bobina === REEL_STATES.OVER) bgColor = 'rgba(244, 67, 54, 0.1)';\n    return /*#__PURE__*/_jsxDEV(TableRow, {\n      sx: {\n        backgroundColor: bgColor,\n        '&:hover': {\n          backgroundColor: 'rgba(0, 0, 0, 0.04)'\n        }\n      },\n      children: columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n        align: column.align || 'left',\n        sx: column.cellStyle,\n        children: column.renderCell ? column.renderCell(row) : row[column.field]\n      }, column.field, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this))\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredBobine.length) return null;\n    const totalBobine = filteredBobine.length;\n    const disponibili = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.DISPONIBILE).length;\n    const inUso = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.IN_USO).length;\n    const terminate = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.TERMINATA).length;\n    const over = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.OVER).length;\n    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0);\n    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0);\n    const metriUtilizzati = metriTotali - metriResidui;\n    const percentualeUtilizzo = metriTotali ? Math.round(metriUtilizzati / metriTotali * 100) : 0;\n    return {\n      totalBobine,\n      disponibili,\n      inUso,\n      terminate,\n      over,\n      metriTotali,\n      metriResidui,\n      metriUtilizzati,\n      percentualeUtilizzo\n    };\n  };\n  const stats = calculateStats();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [stats && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        p: 2,\n        bgcolor: 'background.paper',\n        borderRadius: 1,\n        boxShadow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: [\"Statistiche (\", filteredBobine.length, \" bobine visualizzate)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Utilizzo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.percentualeUtilizzo, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Disponibili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"success.main\",\n            children: stats.disponibili\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"In uso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"warning.main\",\n            children: stats.inUso\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Terminate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"warning.main\",\n            children: stats.terminate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Over\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"error.main\",\n            children: stats.over\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriTotali.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri residui\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriResidui.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri utilizzati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriUtilizzati.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n      data: bobine,\n      columns: columns,\n      onFilteredDataChange: handleFilteredDataChange,\n      loading: loading,\n      emptyMessage: \"Nessuna bobina disponibile\",\n      renderRow: renderRow\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 268,\n    columnNumber: 5\n  }, this);\n};\n_s(BobineFilterableTable, \"f3TlITpreTFylm3or0YT9wPW5MI=\");\n_c = BobineFilterableTable;\nexport default BobineFilterableTable;\nvar _c;\n$RefreshReg$(_c, \"BobineFilterableTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "TableRow", "TableCell", "IconButton", "<PERSON><PERSON><PERSON>", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Add", "AddIcon", "FilterableTable", "REEL_STATES", "getReelStateColor", "jsxDEV", "_jsxDEV", "BobineFilterableTable", "bobine", "loading", "onFilteredDataChange", "onEdit", "onDelete", "onViewHistory", "onQuickAdd", "_s", "filteredBobine", "setFilteredBobine", "handleFilteredDataChange", "data", "columns", "field", "headerName", "dataType", "width", "headerStyle", "fontWeight", "renderCell", "row", "variant", "sx", "fontSize", "children", "numero_bobina", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "utility", "tipologia", "align", "textAlign", "sezione", "metri_totali", "toFixed", "metri_residui", "label", "stato_bobina", "size", "color", "disableFilter", "disableSort", "display", "justifyContent", "onClick", "title", "disabled", "renderRow", "index", "bgColor", "DISPONIBILE", "IN_USO", "TERMINATA", "OVER", "backgroundColor", "map", "column", "cellStyle", "calculateStats", "length", "totalBobine", "disponibili", "filter", "b", "inUso", "terminate", "over", "metriTotali", "reduce", "sum", "metriResidui", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "percentuale<PERSON><PERSON><PERSON><PERSON>", "Math", "round", "stats", "mb", "p", "bgcolor", "borderRadius", "boxShadow", "gutterBottom", "flexWrap", "gap", "emptyMessage", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/BobineFilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, IconButton, Tooltip } from '@mui/material';\nimport { Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Add as AddIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport { REEL_STATES, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per visualizzare la lista delle bobine con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.bobine - Lista delle bobine da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare una bobina\n * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare una bobina\n * @param {Function} props.onViewHistory - Funzione chiamata quando si vuole visualizzare lo storico di una bobina\n * @param {Function} props.onQuickAdd - Funzione chiamata quando si vuole aggiungere rapidamente cavi a una bobina\n */\nconst BobineFilterableTable = ({\n  bobine = [],\n  loading = false,\n  onFilteredDataChange = null,\n  onEdit = null,\n  onDelete = null,\n  onViewHistory = null,\n  onQuickAdd = null\n}) => {\n  const [filteredBobine, setFilteredBobine] = useState(bobine);\n\n  // Aggiorna i dati filtrati quando cambiano le bobine\n  useEffect(() => {\n    setFilteredBobine(bobine);\n  }, [bobine]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = (data) => {\n    setFilteredBobine(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Definizione delle colonne\n  const columns = [\n    {\n      field: 'numero_bobina',\n      headerName: 'ID Bobina',\n      dataType: 'text',\n      width: 120,\n      headerStyle: { fontWeight: 'bold' },\n      renderCell: (row) => (\n        <Typography variant=\"body1\" sx={{ fontSize: '0.95rem', fontWeight: 600 }}>\n          {row.numero_bobina}\n        </Typography>\n      )\n    },\n    {\n      field: 'utility',\n      headerName: 'Utility',\n      dataType: 'text',\n      width: 100,\n      renderCell: (row) => (\n        <Typography variant=\"body1\" sx={{ fontSize: '0.95rem' }}>\n          {row.utility}\n        </Typography>\n      )\n    },\n    {\n      field: 'tipologia',\n      headerName: 'Tipologia',\n      dataType: 'text',\n      width: 150,\n      renderCell: (row) => (\n        <Typography variant=\"body1\" sx={{ fontSize: '0.95rem' }}>\n          {row.tipologia}\n        </Typography>\n      )\n    },\n    // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n    {\n      field: 'sezione',\n      headerName: 'Formazione',\n      dataType: 'text',\n      width: 120,\n      align: 'center',\n      renderCell: (row) => (\n        <Typography variant=\"body1\" sx={{ fontSize: '0.95rem', textAlign: 'center' }}>\n          {row.sezione}\n        </Typography>\n      )\n    },\n    {\n      field: 'metri_totali',\n      headerName: 'Metri Totali',\n      dataType: 'number',\n      width: 110,\n      align: 'center',\n      renderCell: (row) => (\n        <Typography variant=\"body1\" sx={{ fontSize: '0.95rem', textAlign: 'center', fontWeight: 500 }}>\n          {row.metri_totali ? row.metri_totali.toFixed(1) : '0'}\n        </Typography>\n      )\n    },\n    {\n      field: 'metri_residui',\n      headerName: 'Metri Residui',\n      dataType: 'number',\n      width: 110,\n      align: 'center',\n      renderCell: (row) => (\n        <Typography variant=\"body1\" sx={{ fontSize: '0.95rem', textAlign: 'center', fontWeight: 500 }}>\n          {row.metri_residui ? row.metri_residui.toFixed(1) : '0'}\n        </Typography>\n      )\n    },\n    {\n      field: 'stato_bobina',\n      headerName: 'Stato',\n      dataType: 'text',\n      renderCell: (row) => {\n        return (\n          <Chip\n            label={row.stato_bobina || 'N/D'}\n            size=\"small\"\n            color={getReelStateColor(row.stato_bobina)}\n            variant=\"outlined\"\n          />\n        );\n      }\n    },\n    {\n      field: 'ubicazione_bobina',\n      headerName: 'Ubicazione',\n      dataType: 'text'\n    },\n    {\n      field: 'fornitore',\n      headerName: 'Fornitore',\n      dataType: 'text'\n    },\n    {\n      field: 'n_DDT',\n      headerName: 'N° DDT',\n      dataType: 'text'\n    },\n    {\n      field: 'data_DDT',\n      headerName: 'Data DDT',\n      dataType: 'text'\n    },\n    {\n      field: 'actions',\n      headerName: 'Azioni',\n      disableFilter: true,\n      disableSort: true,\n      align: 'center',\n      renderCell: (row) => (\n        <Box sx={{ display: 'flex', justifyContent: 'center' }}>\n          {onEdit && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onEdit(row)}\n              title=\"Modifica bobina\"\n              color=\"primary\"\n            >\n              <EditIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onDelete && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onDelete(row)}\n              title=\"Elimina bobina\"\n              color=\"error\"\n              disabled={row.stato_bobina !== 'Disponibile' || row.metri_residui !== row.metri_totali}\n            >\n              <DeleteIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onViewHistory && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onViewHistory(row)}\n              title=\"Visualizza storico utilizzo\"\n              color=\"info\"\n            >\n              <HistoryIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onQuickAdd && row.stato_bobina !== 'Terminata' && row.stato_bobina !== 'Over' && (\n            <Tooltip title=\"Aggiungi cavi a questa bobina\">\n              <IconButton\n                size=\"small\"\n                onClick={() => onQuickAdd(row)}\n                color=\"success\"\n              >\n                <AddIcon fontSize=\"small\" />\n              </IconButton>\n            </Tooltip>\n          )}\n        </Box>\n      )\n    }\n  ];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_bobina === REEL_STATES.DISPONIBILE) bgColor = 'rgba(76, 175, 80, 0.1)';\n    else if (row.stato_bobina === REEL_STATES.IN_USO) bgColor = 'rgba(255, 152, 0, 0.1)';\n    else if (row.stato_bobina === REEL_STATES.TERMINATA) bgColor = 'rgba(255, 152, 0, 0.2)';\n    else if (row.stato_bobina === REEL_STATES.OVER) bgColor = 'rgba(244, 67, 54, 0.1)';\n\n    return (\n      <TableRow\n        key={index}\n        sx={{\n          backgroundColor: bgColor,\n          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            align={column.align || 'left'}\n            sx={column.cellStyle}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    );\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredBobine.length) return null;\n\n    const totalBobine = filteredBobine.length;\n    const disponibili = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.DISPONIBILE).length;\n    const inUso = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.IN_USO).length;\n    const terminate = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.TERMINATA).length;\n    const over = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.OVER).length;\n\n    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0);\n    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0);\n    const metriUtilizzati = metriTotali - metriResidui;\n\n    const percentualeUtilizzo = metriTotali ? Math.round((metriUtilizzati / metriTotali) * 100) : 0;\n\n    return {\n      totalBobine,\n      disponibili,\n      inUso,\n      terminate,\n      over,\n      metriTotali,\n      metriResidui,\n      metriUtilizzati,\n      percentualeUtilizzo\n    };\n  };\n\n  const stats = calculateStats();\n\n  return (\n    <Box>\n      {stats && (\n        <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Statistiche ({filteredBobine.length} bobine visualizzate)\n          </Typography>\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Utilizzo</Typography>\n              <Typography variant=\"h6\">{stats.percentualeUtilizzo}%</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Disponibili</Typography>\n              <Typography variant=\"h6\" color=\"success.main\">{stats.disponibili}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">In uso</Typography>\n              <Typography variant=\"h6\" color=\"warning.main\">{stats.inUso}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Terminate</Typography>\n              <Typography variant=\"h6\" color=\"warning.main\">{stats.terminate}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Over</Typography>\n              <Typography variant=\"h6\" color=\"error.main\">{stats.over}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri totali</Typography>\n              <Typography variant=\"h6\">{stats.metriTotali.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri residui</Typography>\n              <Typography variant=\"h6\">{stats.metriResidui.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri utilizzati</Typography>\n              <Typography variant=\"h6\">{stats.metriUtilizzati.toFixed(1)} m</Typography>\n            </Box>\n          </Box>\n        </Box>\n      )}\n\n      <FilterableTable\n        data={bobine}\n        columns={columns}\n        onFilteredDataChange={handleFilteredDataChange}\n        loading={loading}\n        emptyMessage=\"Nessuna bobina disponibile\"\n        renderRow={renderRow}\n      />\n    </Box>\n  );\n};\n\nexport default BobineFilterableTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,OAAO,QAAQ,eAAe;AAC/F,SAASC,IAAI,IAAIC,QAAQ,EAAEC,MAAM,IAAIC,UAAU,EAAEC,OAAO,IAAIC,WAAW,EAAEC,GAAG,IAAIC,OAAO,QAAQ,qBAAqB;AACpH,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,wBAAwB;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,SAAAC,MAAA,IAAAC,OAAA;AAYA,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,MAAM,GAAG,EAAE;EACXC,OAAO,GAAG,KAAK;EACfC,oBAAoB,GAAG,IAAI;EAC3BC,MAAM,GAAG,IAAI;EACbC,QAAQ,GAAG,IAAI;EACfC,aAAa,GAAG,IAAI;EACpBC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAACuB,MAAM,CAAC;;EAE5D;EACAtB,SAAS,CAAC,MAAM;IACd+B,iBAAiB,CAACT,MAAM,CAAC;EAC3B,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMU,wBAAwB,GAAIC,IAAI,IAAK;IACzCF,iBAAiB,CAACE,IAAI,CAAC;IACvB,IAAIT,oBAAoB,EAAE;MACxBA,oBAAoB,CAACS,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,GAAG;IACVC,WAAW,EAAE;MAAEC,UAAU,EAAE;IAAO,CAAC;IACnCC,UAAU,EAAGC,GAAG,iBACdtB,OAAA,CAAClB,UAAU;MAACyC,OAAO,EAAC,OAAO;MAACC,EAAE,EAAE;QAAEC,QAAQ,EAAE,SAAS;QAAEL,UAAU,EAAE;MAAI,CAAE;MAAAM,QAAA,EACtEJ,GAAG,CAACK;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEhB,CAAC,EACD;IACEhB,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,GAAG;IACVG,UAAU,EAAGC,GAAG,iBACdtB,OAAA,CAAClB,UAAU;MAACyC,OAAO,EAAC,OAAO;MAACC,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAU,CAAE;MAAAC,QAAA,EACrDJ,GAAG,CAACU;IAAO;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEhB,CAAC,EACD;IACEhB,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,GAAG;IACVG,UAAU,EAAGC,GAAG,iBACdtB,OAAA,CAAClB,UAAU;MAACyC,OAAO,EAAC,OAAO;MAACC,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAU,CAAE;MAAAC,QAAA,EACrDJ,GAAG,CAACW;IAAS;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEhB,CAAC;EACD;EACA;IACEhB,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,GAAG;IACVgB,KAAK,EAAE,QAAQ;IACfb,UAAU,EAAGC,GAAG,iBACdtB,OAAA,CAAClB,UAAU;MAACyC,OAAO,EAAC,OAAO;MAACC,EAAE,EAAE;QAAEC,QAAQ,EAAE,SAAS;QAAEU,SAAS,EAAE;MAAS,CAAE;MAAAT,QAAA,EAC1EJ,GAAG,CAACc;IAAO;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAEhB,CAAC,EACD;IACEhB,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,cAAc;IAC1BC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,GAAG;IACVgB,KAAK,EAAE,QAAQ;IACfb,UAAU,EAAGC,GAAG,iBACdtB,OAAA,CAAClB,UAAU;MAACyC,OAAO,EAAC,OAAO;MAACC,EAAE,EAAE;QAAEC,QAAQ,EAAE,SAAS;QAAEU,SAAS,EAAE,QAAQ;QAAEf,UAAU,EAAE;MAAI,CAAE;MAAAM,QAAA,EAC3FJ,GAAG,CAACe,YAAY,GAAGf,GAAG,CAACe,YAAY,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C;EAEhB,CAAC,EACD;IACEhB,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,GAAG;IACVgB,KAAK,EAAE,QAAQ;IACfb,UAAU,EAAGC,GAAG,iBACdtB,OAAA,CAAClB,UAAU;MAACyC,OAAO,EAAC,OAAO;MAACC,EAAE,EAAE;QAAEC,QAAQ,EAAE,SAAS;QAAEU,SAAS,EAAE,QAAQ;QAAEf,UAAU,EAAE;MAAI,CAAE;MAAAM,QAAA,EAC3FJ,GAAG,CAACiB,aAAa,GAAGjB,GAAG,CAACiB,aAAa,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C;EAEhB,CAAC,EACD;IACEhB,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,OAAO;IACnBC,QAAQ,EAAE,MAAM;IAChBI,UAAU,EAAGC,GAAG,IAAK;MACnB,oBACEtB,OAAA,CAACjB,IAAI;QACHyD,KAAK,EAAElB,GAAG,CAACmB,YAAY,IAAI,KAAM;QACjCC,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE7C,iBAAiB,CAACwB,GAAG,CAACmB,YAAY,CAAE;QAC3ClB,OAAO,EAAC;MAAU;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEN;EACF,CAAC,EACD;IACEhB,KAAK,EAAE,mBAAmB;IAC1BC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,UAAU;IACjBC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,QAAQ;IACpB4B,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBX,KAAK,EAAE,QAAQ;IACfb,UAAU,EAAGC,GAAG,iBACdtB,OAAA,CAACnB,GAAG;MAAC2C,EAAE,EAAE;QAAEsB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAArB,QAAA,GACpDrB,MAAM,iBACLL,OAAA,CAACd,UAAU;QACTwD,IAAI,EAAC,OAAO;QACZM,OAAO,EAAEA,CAAA,KAAM3C,MAAM,CAACiB,GAAG,CAAE;QAC3B2B,KAAK,EAAC,iBAAiB;QACvBN,KAAK,EAAC,SAAS;QAAAjB,QAAA,eAEf1B,OAAA,CAACX,QAAQ;UAACoC,QAAQ,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACb,EACAzB,QAAQ,iBACPN,OAAA,CAACd,UAAU;QACTwD,IAAI,EAAC,OAAO;QACZM,OAAO,EAAEA,CAAA,KAAM1C,QAAQ,CAACgB,GAAG,CAAE;QAC7B2B,KAAK,EAAC,gBAAgB;QACtBN,KAAK,EAAC,OAAO;QACbO,QAAQ,EAAE5B,GAAG,CAACmB,YAAY,KAAK,aAAa,IAAInB,GAAG,CAACiB,aAAa,KAAKjB,GAAG,CAACe,YAAa;QAAAX,QAAA,eAEvF1B,OAAA,CAACT,UAAU;UAACkC,QAAQ,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CACb,EACAxB,aAAa,iBACZP,OAAA,CAACd,UAAU;QACTwD,IAAI,EAAC,OAAO;QACZM,OAAO,EAAEA,CAAA,KAAMzC,aAAa,CAACe,GAAG,CAAE;QAClC2B,KAAK,EAAC,6BAA6B;QACnCN,KAAK,EAAC,MAAM;QAAAjB,QAAA,eAEZ1B,OAAA,CAACP,WAAW;UAACgC,QAAQ,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CACb,EACAvB,UAAU,IAAIc,GAAG,CAACmB,YAAY,KAAK,WAAW,IAAInB,GAAG,CAACmB,YAAY,KAAK,MAAM,iBAC5EzC,OAAA,CAACb,OAAO;QAAC8D,KAAK,EAAC,+BAA+B;QAAAvB,QAAA,eAC5C1B,OAAA,CAACd,UAAU;UACTwD,IAAI,EAAC,OAAO;UACZM,OAAO,EAAEA,CAAA,KAAMxC,UAAU,CAACc,GAAG,CAAE;UAC/BqB,KAAK,EAAC,SAAS;UAAAjB,QAAA,eAEf1B,OAAA,CAACL,OAAO;YAAC8B,QAAQ,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,CACF;;EAED;EACA,MAAMoB,SAAS,GAAGA,CAAC7B,GAAG,EAAE8B,KAAK,KAAK;IAChC;IACA,IAAIC,OAAO,GAAG,SAAS;IACvB,IAAI/B,GAAG,CAACmB,YAAY,KAAK5C,WAAW,CAACyD,WAAW,EAAED,OAAO,GAAG,wBAAwB,CAAC,KAChF,IAAI/B,GAAG,CAACmB,YAAY,KAAK5C,WAAW,CAAC0D,MAAM,EAAEF,OAAO,GAAG,wBAAwB,CAAC,KAChF,IAAI/B,GAAG,CAACmB,YAAY,KAAK5C,WAAW,CAAC2D,SAAS,EAAEH,OAAO,GAAG,wBAAwB,CAAC,KACnF,IAAI/B,GAAG,CAACmB,YAAY,KAAK5C,WAAW,CAAC4D,IAAI,EAAEJ,OAAO,GAAG,wBAAwB;IAElF,oBACErD,OAAA,CAAChB,QAAQ;MAEPwC,EAAE,EAAE;QACFkC,eAAe,EAAEL,OAAO;QACxB,SAAS,EAAE;UAAEK,eAAe,EAAE;QAAsB;MACtD,CAAE;MAAAhC,QAAA,EAEDZ,OAAO,CAAC6C,GAAG,CAAEC,MAAM,iBAClB5D,OAAA,CAACf,SAAS;QAERiD,KAAK,EAAE0B,MAAM,CAAC1B,KAAK,IAAI,MAAO;QAC9BV,EAAE,EAAEoC,MAAM,CAACC,SAAU;QAAAnC,QAAA,EAEpBkC,MAAM,CAACvC,UAAU,GAAGuC,MAAM,CAACvC,UAAU,CAACC,GAAG,CAAC,GAAGA,GAAG,CAACsC,MAAM,CAAC7C,KAAK;MAAC,GAJ1D6C,MAAM,CAAC7C,KAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKR,CACZ;IAAC,GAdGqB,KAAK;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeF,CAAC;EAEf,CAAC;;EAED;EACA,MAAM+B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACpD,cAAc,CAACqD,MAAM,EAAE,OAAO,IAAI;IAEvC,MAAMC,WAAW,GAAGtD,cAAc,CAACqD,MAAM;IACzC,MAAME,WAAW,GAAGvD,cAAc,CAACwD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,YAAY,KAAK5C,WAAW,CAACyD,WAAW,CAAC,CAACS,MAAM;IACjG,MAAMK,KAAK,GAAG1D,cAAc,CAACwD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,YAAY,KAAK5C,WAAW,CAAC0D,MAAM,CAAC,CAACQ,MAAM;IACtF,MAAMM,SAAS,GAAG3D,cAAc,CAACwD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,YAAY,KAAK5C,WAAW,CAAC2D,SAAS,CAAC,CAACO,MAAM;IAC7F,MAAMO,IAAI,GAAG5D,cAAc,CAACwD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,YAAY,KAAK5C,WAAW,CAAC4D,IAAI,CAAC,CAACM,MAAM;IAEnF,MAAMQ,WAAW,GAAG7D,cAAc,CAAC8D,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC,KAAKM,GAAG,IAAIN,CAAC,CAAC9B,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACrF,MAAMqC,YAAY,GAAGhE,cAAc,CAAC8D,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC,KAAKM,GAAG,IAAIN,CAAC,CAAC5B,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACvF,MAAMoC,eAAe,GAAGJ,WAAW,GAAGG,YAAY;IAElD,MAAME,mBAAmB,GAAGL,WAAW,GAAGM,IAAI,CAACC,KAAK,CAAEH,eAAe,GAAGJ,WAAW,GAAI,GAAG,CAAC,GAAG,CAAC;IAE/F,OAAO;MACLP,WAAW;MACXC,WAAW;MACXG,KAAK;MACLC,SAAS;MACTC,IAAI;MACJC,WAAW;MACXG,YAAY;MACZC,eAAe;MACfC;IACF,CAAC;EACH,CAAC;EAED,MAAMG,KAAK,GAAGjB,cAAc,CAAC,CAAC;EAE9B,oBACE9D,OAAA,CAACnB,GAAG;IAAA6C,QAAA,GACDqD,KAAK,iBACJ/E,OAAA,CAACnB,GAAG;MAAC2C,EAAE,EAAE;QAAEwD,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE,kBAAkB;QAAEC,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAE;MAAA1D,QAAA,gBACnF1B,OAAA,CAAClB,UAAU;QAACyC,OAAO,EAAC,WAAW;QAAC8D,YAAY;QAAA3D,QAAA,GAAC,eAC9B,EAAChB,cAAc,CAACqD,MAAM,EAAC,uBACtC;MAAA;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/B,OAAA,CAACnB,GAAG;QAAC2C,EAAE,EAAE;UAAEsB,OAAO,EAAE,MAAM;UAAEwC,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAA7D,QAAA,gBACrD1B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACoB,KAAK,EAAC,gBAAgB;YAAAjB,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxE/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAAAG,QAAA,GAAEqD,KAAK,CAACH,mBAAmB,EAAC,GAAC;UAAA;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACoB,KAAK,EAAC,gBAAgB;YAAAjB,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3E/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAACoB,KAAK,EAAC,cAAc;YAAAjB,QAAA,EAAEqD,KAAK,CAACd;UAAW;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACoB,KAAK,EAAC,gBAAgB;YAAAjB,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtE/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAACoB,KAAK,EAAC,cAAc;YAAAjB,QAAA,EAAEqD,KAAK,CAACX;UAAK;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACoB,KAAK,EAAC,gBAAgB;YAAAjB,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzE/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAACoB,KAAK,EAAC,cAAc;YAAAjB,QAAA,EAAEqD,KAAK,CAACV;UAAS;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACoB,KAAK,EAAC,gBAAgB;YAAAjB,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpE/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAACoB,KAAK,EAAC,YAAY;YAAAjB,QAAA,EAAEqD,KAAK,CAACT;UAAI;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACoB,KAAK,EAAC,gBAAgB;YAAAjB,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5E/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAAAG,QAAA,GAAEqD,KAAK,CAACR,WAAW,CAACjC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACoB,KAAK,EAAC,gBAAgB;YAAAjB,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7E/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAAAG,QAAA,GAAEqD,KAAK,CAACL,YAAY,CAACpC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACN/B,OAAA,CAACnB,GAAG;UAAA6C,QAAA,gBACF1B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACoB,KAAK,EAAC,gBAAgB;YAAAjB,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChF/B,OAAA,CAAClB,UAAU;YAACyC,OAAO,EAAC,IAAI;YAAAG,QAAA,GAAEqD,KAAK,CAACJ,eAAe,CAACrC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED/B,OAAA,CAACJ,eAAe;MACdiB,IAAI,EAAEX,MAAO;MACbY,OAAO,EAAEA,OAAQ;MACjBV,oBAAoB,EAAEQ,wBAAyB;MAC/CT,OAAO,EAAEA,OAAQ;MACjBqF,YAAY,EAAC,4BAA4B;MACzCrC,SAAS,EAAEA;IAAU;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACtB,EAAA,CA9SIR,qBAAqB;AAAAwF,EAAA,GAArBxF,qBAAqB;AAgT3B,eAAeA,qBAAqB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}