{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { buildSectionsFromFormat } from \"../internals/hooks/useField/buildSectionsFromFormat.js\";\nimport { getLocalizedDigits } from \"../internals/hooks/useField/useField.utils.js\";\nimport { usePickerTranslations } from \"./usePickerTranslations.js\";\nimport { useNullablePickerContext } from \"../internals/hooks/useNullablePickerContext.js\";\n/**\n * Returns the parsed format to be rendered in the field when there is no value or in other parts of the Picker.\n * This format is localized (for example `AAAA` for the year with the French locale) and cannot be parsed by your date library.\n * @param {object} The parameters needed to build the placeholder.\n * @param {string} params.format Format to parse.\n * @returns\n */\nexport const useParsedFormat = (parameters = {}) => {\n  const pickerContext = useNullablePickerContext();\n  const utils = useUtils();\n  const isRtl = useRtl();\n  const translations = usePickerTranslations();\n  const localizedDigits = React.useMemo(() => getLocalizedDigits(utils), [utils]);\n  const {\n    format = pickerContext?.fieldFormat ?? utils.formats.fullDate\n  } = parameters;\n  return React.useMemo(() => {\n    const sections = buildSectionsFromFormat({\n      utils,\n      format,\n      formatDensity: 'dense',\n      isRtl,\n      shouldRespectLeadingZeros: true,\n      localeText: translations,\n      localizedDigits,\n      date: null,\n      // TODO v9: Make sure we still don't reverse in `buildSectionsFromFormat` when using `useParsedFormat`.\n      enableAccessibleFieldDOMStructure: false\n    });\n    return sections.map(section => `${section.startSeparator}${section.placeholder}${section.endSeparator}`).join('');\n  }, [utils, isRtl, translations, localizedDigits, format]);\n};", "map": {"version": 3, "names": ["React", "useRtl", "useUtils", "buildSectionsFromFormat", "getLocalizedDigits", "usePickerTranslations", "useNullablePickerContext", "useParsedFormat", "parameters", "picker<PERSON>ontext", "utils", "isRtl", "translations", "localizedDigits", "useMemo", "format", "fieldFormat", "formats", "fullDate", "sections", "formatDensity", "shouldRespectLeadingZeros", "localeText", "date", "enableAccessibleFieldDOMStructure", "map", "section", "startSeparator", "placeholder", "endSeparator", "join"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/hooks/useParsedFormat.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { buildSectionsFromFormat } from \"../internals/hooks/useField/buildSectionsFromFormat.js\";\nimport { getLocalizedDigits } from \"../internals/hooks/useField/useField.utils.js\";\nimport { usePickerTranslations } from \"./usePickerTranslations.js\";\nimport { useNullablePickerContext } from \"../internals/hooks/useNullablePickerContext.js\";\n/**\n * Returns the parsed format to be rendered in the field when there is no value or in other parts of the Picker.\n * This format is localized (for example `AAAA` for the year with the French locale) and cannot be parsed by your date library.\n * @param {object} The parameters needed to build the placeholder.\n * @param {string} params.format Format to parse.\n * @returns\n */\nexport const useParsedFormat = (parameters = {}) => {\n  const pickerContext = useNullablePickerContext();\n  const utils = useUtils();\n  const isRtl = useRtl();\n  const translations = usePickerTranslations();\n  const localizedDigits = React.useMemo(() => getLocalizedDigits(utils), [utils]);\n  const {\n    format = pickerContext?.fieldFormat ?? utils.formats.fullDate\n  } = parameters;\n  return React.useMemo(() => {\n    const sections = buildSectionsFromFormat({\n      utils,\n      format,\n      formatDensity: 'dense',\n      isRtl,\n      shouldRespectLeadingZeros: true,\n      localeText: translations,\n      localizedDigits,\n      date: null,\n      // TODO v9: Make sure we still don't reverse in `buildSectionsFromFormat` when using `useParsedFormat`.\n      enableAccessibleFieldDOMStructure: false\n    });\n    return sections.map(section => `${section.startSeparator}${section.placeholder}${section.endSeparator}`).join('');\n  }, [utils, isRtl, translations, localizedDigits, format]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,kBAAkB,QAAQ,+CAA+C;AAClF,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,wBAAwB,QAAQ,gDAAgD;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGA,CAACC,UAAU,GAAG,CAAC,CAAC,KAAK;EAClD,MAAMC,aAAa,GAAGH,wBAAwB,CAAC,CAAC;EAChD,MAAMI,KAAK,GAAGR,QAAQ,CAAC,CAAC;EACxB,MAAMS,KAAK,GAAGV,MAAM,CAAC,CAAC;EACtB,MAAMW,YAAY,GAAGP,qBAAqB,CAAC,CAAC;EAC5C,MAAMQ,eAAe,GAAGb,KAAK,CAACc,OAAO,CAAC,MAAMV,kBAAkB,CAACM,KAAK,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAC/E,MAAM;IACJK,MAAM,GAAGN,aAAa,EAAEO,WAAW,IAAIN,KAAK,CAACO,OAAO,CAACC;EACvD,CAAC,GAAGV,UAAU;EACd,OAAOR,KAAK,CAACc,OAAO,CAAC,MAAM;IACzB,MAAMK,QAAQ,GAAGhB,uBAAuB,CAAC;MACvCO,KAAK;MACLK,MAAM;MACNK,aAAa,EAAE,OAAO;MACtBT,KAAK;MACLU,yBAAyB,EAAE,IAAI;MAC/BC,UAAU,EAAEV,YAAY;MACxBC,eAAe;MACfU,IAAI,EAAE,IAAI;MACV;MACAC,iCAAiC,EAAE;IACrC,CAAC,CAAC;IACF,OAAOL,QAAQ,CAACM,GAAG,CAACC,OAAO,IAAI,GAAGA,OAAO,CAACC,cAAc,GAAGD,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACG,YAAY,EAAE,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EACnH,CAAC,EAAE,CAACpB,KAAK,EAAEC,KAAK,EAAEC,YAAY,EAAEC,eAAe,EAAEE,MAAM,CAAC,CAAC;AAC3D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}