#!/usr/bin/env python3
"""
Script per verificare se ci sono ancora tracce di 'x' minuscola nel sistema.
"""

import psycopg2
import psycopg2.extras
from modules.database_pg import Database

def check_remaining_lowercase():
    """Verifica se ci sono ancora tracce di case sensitivity nel sistema."""
    print("🔍 VERIFICA TRACCE RESIDUE DI CASE SENSITIVITY")
    print("=" * 60)
    
    db = Database()
    conn = db.get_dict_cursor_connection()
    
    try:
        with conn.cursor() as cur:
            cantiere_id = 1
            
            print("📋 STEP 1: RICERCA 'x' MINUSCOLA IN TUTTI I CAVI")
            print("-" * 50)
            
            # Cerca tutti i cavi con 'x' minuscola nella sezione
            cur.execute("""
                SELECT id_cavo, tipologia, sezione, stato_installazione, id_bobina
                FROM cavi
                WHERE id_cantiere = %s
                  AND sezione LIKE %s
                  AND sezione NOT LIKE %s
                ORDER BY tipologia, sezione, id_cavo
            """, (cantiere_id, '%x%', '%X%'))
            
            cavi_minuscola = cur.fetchall()
            
            if cavi_minuscola:
                print(f"❌ Trovati {len(cavi_minuscola)} cavi con 'x' minuscola:")
                for cavo in cavi_minuscola:
                    print(f"   {cavo['id_cavo']} | {cavo['tipologia']} | '{cavo['sezione']}'")
            else:
                print("✅ Nessun cavo con 'x' minuscola trovato")
            
            print("\n📦 STEP 2: RICERCA 'x' MINUSCOLA IN TUTTE LE BOBINE")
            print("-" * 50)
            
            # Cerca tutte le bobine con 'x' minuscola nella sezione
            cur.execute("""
                SELECT id_bobina, tipologia, sezione, stato_bobina
                FROM parco_cavi
                WHERE id_cantiere = %s
                  AND sezione LIKE %s
                  AND sezione NOT LIKE %s
                ORDER BY tipologia, sezione, id_bobina
            """, (cantiere_id, '%x%', '%X%'))
            
            bobine_minuscola = cur.fetchall()
            
            if bobine_minuscola:
                print(f"❌ Trovate {len(bobine_minuscola)} bobine con 'x' minuscola:")
                for bobina in bobine_minuscola:
                    print(f"   {bobina['id_bobina']} | {bobina['tipologia']} | '{bobina['sezione']}'")
            else:
                print("✅ Nessuna bobina con 'x' minuscola trovata")
            
            print("\n🔍 STEP 3: VERIFICA SPECIFICA FG16OR16 240MM2")
            print("-" * 50)
            
            # Verifica specifica per FG16OR16 240MM2
            cur.execute("""
                SELECT DISTINCT tipologia, sezione
                FROM cavi
                WHERE id_cantiere = %s 
                  AND tipologia ILIKE %s 
                  AND sezione ILIKE %s
                ORDER BY tipologia, sezione
            """, (cantiere_id, '%FG16OR16%', '%240MM2%'))
            
            fg16_variants = cur.fetchall()
            
            print(f"Varianti FG16OR16 240MM2 nei cavi: {len(fg16_variants)}")
            for variant in fg16_variants:
                print(f"   '{variant['tipologia']}' | '{variant['sezione']}'")
            
            # Verifica bobine FG16OR16 240MM2
            cur.execute("""
                SELECT DISTINCT tipologia, sezione
                FROM parco_cavi
                WHERE id_cantiere = %s 
                  AND tipologia ILIKE %s 
                  AND sezione ILIKE %s
                ORDER BY tipologia, sezione
            """, (cantiere_id, '%FG16OR16%', '%240MM2%'))
            
            fg16_bobine_variants = cur.fetchall()
            
            print(f"Varianti FG16OR16 240MM2 nelle bobine: {len(fg16_bobine_variants)}")
            for variant in fg16_bobine_variants:
                print(f"   '{variant['tipologia']}' | '{variant['sezione']}'")
            
            print("\n📊 STEP 4: TEST BOQ QUERY")
            print("-" * 50)
            
            # Testa la query BOQ per FG16OR16 240MM2
            cur.execute("""
                SELECT
                    tipologia,
                    sezione as formazione,
                    COUNT(*) as num_cavi
                FROM cavi
                WHERE id_cantiere = %s 
                  AND (modificato_manualmente IS NULL OR modificato_manualmente != 3)
                  AND tipologia ILIKE %s 
                  AND sezione ILIKE %s
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """, (cantiere_id, '%FG16OR16%', '%240MM2%'))
            
            boq_results = cur.fetchall()
            
            print(f"Risultati BOQ per FG16OR16 240MM2: {len(boq_results)} righe")
            for result in boq_results:
                print(f"   {result['tipologia']} | {result['formazione']} | {result['num_cavi']} cavi")
            
            print("\n✅ STEP 5: CONCLUSIONI")
            print("-" * 50)
            
            total_issues = len(cavi_minuscola) + len(bobine_minuscola)
            
            if total_issues == 0 and len(fg16_variants) == 1 and len(fg16_bobine_variants) == 1 and len(boq_results) == 1:
                print("🎉 PERFETTO! Il sistema è completamente normalizzato:")
                print("   ✅ Nessun cavo con 'x' minuscola")
                print("   ✅ Nessuna bobina con 'x' minuscola")
                print("   ✅ Una sola variante FG16OR16 240MM2")
                print("   ✅ BOQ mostra una sola riga")
                print("\n💡 La 'x' minuscola che hai visto prima era un dato temporaneo")
                print("   che è stato corretto durante la normalizzazione.")
            else:
                print("⚠️  Ci sono ancora alcuni problemi da risolvere:")
                if len(cavi_minuscola) > 0:
                    print(f"   - {len(cavi_minuscola)} cavi con 'x' minuscola")
                if len(bobine_minuscola) > 0:
                    print(f"   - {len(bobine_minuscola)} bobine con 'x' minuscola")
                if len(fg16_variants) > 1:
                    print(f"   - {len(fg16_variants)} varianti FG16OR16 nei cavi")
                if len(fg16_bobine_variants) > 1:
                    print(f"   - {len(fg16_bobine_variants)} varianti FG16OR16 nelle bobine")
                if len(boq_results) > 1:
                    print(f"   - {len(boq_results)} righe BOQ per FG16OR16")
                
    except Exception as e:
        print(f"❌ Errore durante la verifica: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    check_remaining_lowercase()
