import React, { useState, useRef } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Divider,
  Alert,
  Snackbar
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  Science as ScienceIcon,
  Warning as WarningIcon,
  Assessment as ReportIcon,
  Build as BuildIcon,
  Visibility as ViewIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Dashboard as DashboardIcon
} from '@mui/icons-material';

import CertificazioneCEI64_8 from '../../components/certificazione/CertificazioneCEI64_8';
import SelectedCantiereDisplay from '../../components/common/SelectedCantiereDisplay';

const CertificazioneCEI64_8Page = () => {
  const { cantiereId } = useParams();
  const location = useLocation();
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  
  // Ref per il componente principale
  const certificazioneCEIRef = useRef();

  // Determina l'azione iniziale basata sulla route
  const getInitialAction = () => {
    const path = location.pathname;
    if (path.includes('/dashboard')) return 'dashboardCEI';
    if (path.includes('/rapporti')) return 'visualizzaRapporti';
    if (path.includes('/prove')) return 'visualizzaProve';
    if (path.includes('/non-conformita')) return 'visualizzaNonConformita';
    if (path.includes('/strumenti')) return 'gestioneStrumenti';
    return 'dashboardCEI';
  };

  // Gestisce i messaggi di successo
  const handleSuccess = (message) => {
    setSnackbar({ open: true, message, severity: 'success' });
  };

  // Gestisce i messaggi di errore
  const handleError = (message) => {
    setSnackbar({ open: true, message, severity: 'error' });
  };

  // Chiude il snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Opzioni del menu per la certificazione CEI 64-8
  const menuOptions = [
    {
      category: 'Dashboard',
      items: [
        {
          key: 'dashboardCEI',
          label: 'Dashboard CEI 64-8',
          icon: <DashboardIcon />,
          description: 'Panoramica generale del sistema di certificazione'
        }
      ]
    },
    {
      category: 'Rapporti Generali',
      items: [
        {
          key: 'visualizzaRapporti',
          label: 'Visualizza Rapporti',
          icon: <ViewIcon />,
          description: 'Visualizza tutti i rapporti generali di collaudo'
        },
        {
          key: 'creaRapporto',
          label: 'Nuovo Rapporto',
          icon: <AddIcon />,
          description: 'Crea un nuovo rapporto generale di collaudo'
        },
        {
          key: 'modificaRapporto',
          label: 'Modifica Rapporto',
          icon: <EditIcon />,
          description: 'Modifica un rapporto esistente'
        },
        {
          key: 'eliminaRapporto',
          label: 'Elimina Rapporto',
          icon: <DeleteIcon />,
          description: 'Elimina un rapporto generale'
        }
      ]
    },
    {
      category: 'Certificazioni e Prove',
      items: [
        {
          key: 'visualizzaCertificazioni',
          label: 'Visualizza Certificazioni',
          icon: <ViewIcon />,
          description: 'Visualizza tutte le certificazioni cavi'
        },
        {
          key: 'creaCertificazione',
          label: 'Nuova Certificazione',
          icon: <AddIcon />,
          description: 'Crea una nuova certificazione cavo'
        },
        {
          key: 'visualizzaProve',
          label: 'Prove Dettagliate',
          icon: <ScienceIcon />,
          description: 'Gestisci le prove dettagliate per conformità CEI 64-8'
        },
        {
          key: 'creaProva',
          label: 'Nuova Prova',
          icon: <AddIcon />,
          description: 'Crea una nuova prova dettagliata'
        }
      ]
    },
    {
      category: 'Non Conformità',
      items: [
        {
          key: 'visualizzaNonConformita',
          label: 'Visualizza Non Conformità',
          icon: <WarningIcon />,
          description: 'Gestisci le non conformità rilevate'
        }
      ]
    },
    {
      category: 'Strumenti',
      items: [
        {
          key: 'gestioneStrumenti',
          label: 'Gestione Strumenti',
          icon: <BuildIcon />,
          description: 'Gestisci gli strumenti di misura certificati'
        }
      ]
    }
  ];

  // Gestisce la selezione di un'opzione
  const handleOptionSelect = (optionKey) => {
    if (certificazioneCEIRef.current) {
      certificazioneCEIRef.current.handleOptionSelect(optionKey);
    }
  };

  // Esegue l'azione iniziale
  React.useEffect(() => {
    const initialAction = getInitialAction();
    if (initialAction && certificazioneCEIRef.current) {
      setTimeout(() => {
        certificazioneCEIRef.current.handleOptionSelect(initialAction);
      }, 100);
    }
  }, [location.pathname]);

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <SelectedCantiereDisplay />
        <Typography variant="h4" gutterBottom sx={{ mt: 2 }}>
          Certificazione Cavi CEI 64-8
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Sistema di certificazione conforme alle normative CEI 64-8 e IEC 60364
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Menu laterale */}
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Menu Certificazione
            </Typography>
            
            {menuOptions.map((category, categoryIndex) => (
              <Box key={categoryIndex} sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="primary" sx={{ mb: 1, fontWeight: 'bold' }}>
                  {category.category}
                </Typography>
                <List dense>
                  {category.items.map((item) => (
                    <ListItemButton
                      key={item.key}
                      onClick={() => handleOptionSelect(item.key)}
                      sx={{ 
                        borderRadius: 1, 
                        mb: 0.5,
                        '&:hover': {
                          backgroundColor: 'action.hover'
                        }
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        {item.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={item.label}
                        secondary={item.description}
                        primaryTypographyProps={{ fontSize: '0.875rem' }}
                        secondaryTypographyProps={{ fontSize: '0.75rem' }}
                      />
                    </ListItemButton>
                  ))}
                </List>
                {categoryIndex < menuOptions.length - 1 && <Divider sx={{ my: 1 }} />}
              </Box>
            ))}
          </Paper>
        </Grid>

        {/* Contenuto principale */}
        <Grid item xs={12} md={9}>
          <Paper sx={{ p: 3, minHeight: '600px' }}>
            {cantiereId ? (
              <CertificazioneCEI64_8
                ref={certificazioneCEIRef}
                cantiereId={cantiereId}
                onSuccess={handleSuccess}
                onError={handleError}
              />
            ) : (
              <Alert severity="warning">
                Seleziona un cantiere per accedere alle funzionalità di certificazione CEI 64-8
              </Alert>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Snackbar per messaggi */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CertificazioneCEI64_8Page;
