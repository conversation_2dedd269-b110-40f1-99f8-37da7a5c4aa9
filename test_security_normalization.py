#!/usr/bin/env python3
"""
Test di sicurezza per verificare che la normalizzazione blocchi input pericolosi.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from webapp.backend.utils.cable_normalizer import CableNormalizer

def test_sql_injection_protection():
    """Testa la protezione contro SQL injection."""
    print("🛡️  TEST PROTEZIONE SQL INJECTION")
    print("-" * 50)
    
    normalizer = CableNormalizer()
    
    dangerous_inputs = [
        "'; DROP TABLE cavi; --",
        "1' OR '1'='1",
        "admin'; DELETE FROM users; --",
        "test' UNION SELECT * FROM passwords --",
        "'; INSERT INTO admin VALUES('hacker'); --"
    ]
    
    for dangerous_input in dangerous_inputs:
        sanitized = normalizer.sanitize_input(dangerous_input, 'tipologia')
        print(f"Input pericoloso: '{dangerous_input}'")
        print(f"Sanitizzato:      '{sanitized}'")
        
        # Verifica che non contenga più caratteri pericolosi
        dangerous_chars = ["'", '"', ';', '--', 'DROP', 'DELETE', 'INSERT', 'UNION']
        is_safe = not any(char.upper() in sanitized.upper() for char in dangerous_chars)
        
        if is_safe:
            print("✅ Input sanitizzato correttamente")
        else:
            print("❌ Input ancora pericoloso!")
        print()

def test_xss_protection():
    """Testa la protezione contro XSS."""
    print("🛡️  TEST PROTEZIONE XSS")
    print("-" * 50)
    
    normalizer = CableNormalizer()
    
    xss_inputs = [
        "<script>alert('XSS')</script>",
        "javascript:alert('XSS')",
        "<img src=x onerror=alert('XSS')>",
        "onload=alert('XSS')",
        "<iframe src='javascript:alert(1)'></iframe>"
    ]
    
    for xss_input in xss_inputs:
        sanitized = normalizer.sanitize_input(xss_input, 'descrizione')
        print(f"Input XSS:    '{xss_input}'")
        print(f"Sanitizzato:  '{sanitized}'")
        
        # Verifica che non contenga più tag o script pericolosi
        dangerous_patterns = ['<script', 'javascript:', 'onload=', 'onerror=', '<iframe']
        is_safe = not any(pattern.lower() in sanitized.lower() for pattern in dangerous_patterns)
        
        if is_safe:
            print("✅ Input sanitizzato correttamente")
        else:
            print("❌ Input ancora pericoloso!")
        print()

def test_field_length_limits():
    """Testa i limiti di lunghezza dei campi."""
    print("📏 TEST LIMITI LUNGHEZZA CAMPI")
    print("-" * 50)
    
    normalizer = CableNormalizer()
    
    # Test con campo tipologia (limite 100 caratteri)
    long_input = "A" * 200  # 200 caratteri
    sanitized = normalizer.normalize_text_field(long_input, 'tipologia')
    
    print(f"Input lungo ({len(long_input)} caratteri): '{long_input[:50]}...'")
    print(f"Sanitizzato ({len(sanitized)} caratteri): '{sanitized}'")
    
    if len(sanitized) <= 100:
        print("✅ Lunghezza limitata correttamente")
    else:
        print("❌ Lunghezza non limitata!")
    print()

def test_unicode_normalization():
    """Testa la normalizzazione Unicode."""
    print("🌐 TEST NORMALIZZAZIONE UNICODE")
    print("-" * 50)
    
    normalizer = CableNormalizer()
    
    unicode_inputs = [
        "Café",  # accenti
        "naïve",  # dieresi
        "résumé",  # accenti multipli
        "Москва",  # cirillico
        "北京",  # cinese
        "🚀💻",  # emoji
    ]
    
    for unicode_input in unicode_inputs:
        sanitized = normalizer.sanitize_input(unicode_input, 'descrizione')
        print(f"Input Unicode: '{unicode_input}'")
        print(f"Sanitizzato:   '{sanitized}'")
        
        # Verifica che sia stato normalizzato
        if sanitized != unicode_input:
            print("✅ Unicode normalizzato")
        else:
            print("ℹ️  Unicode mantenuto")
        print()

def test_cable_data_normalization():
    """Testa la normalizzazione completa dei dati cavo."""
    print("🔧 TEST NORMALIZZAZIONE COMPLETA DATI CAVO")
    print("-" * 50)
    
    normalizer = CableNormalizer()
    
    # Dati di test con vari problemi
    test_data = {
        'id_cavo': "test'; drop table cavi; --",  # SQL injection
        'tipologia': "fg16or16<script>alert('xss')</script>",  # XSS
        'sezione': "1x240 mm2",  # normalizzazione sezione
        'utility': "test   utility",  # spazi multipli
        'colore_cavo': "rosso",  # minuscolo
        'sistema': "A" * 200,  # troppo lungo
        'ubicazione_partenza': "test location",
        'descrizione_utenza_partenza': "Descrizione con àccénti",
    }
    
    print("Dati originali:")
    for field, value in test_data.items():
        print(f"  {field}: '{value}'")
    
    normalized = normalizer.normalize_all_cable_fields(test_data)
    
    print("\nDati normalizzati:")
    for field, value in normalized.items():
        if field in test_data:
            print(f"  {field}: '{value}'")
    
    # Verifica sicurezza
    dangerous_found = False
    for field, value in normalized.items():
        if isinstance(value, str):
            if any(danger in value.lower() for danger in ['script', 'drop', 'delete', 'insert']):
                dangerous_found = True
                print(f"❌ Contenuto pericoloso trovato in {field}: {value}")
    
    if not dangerous_found:
        print("✅ Tutti i campi sono sicuri")
    
    print()

def main():
    """Funzione principale del test di sicurezza."""
    print("🛡️  TEST DI SICUREZZA NORMALIZZAZIONE")
    print("=" * 60)
    
    test_sql_injection_protection()
    test_xss_protection()
    test_field_length_limits()
    test_unicode_normalization()
    test_cable_data_normalization()
    
    print("🎉 TUTTI I TEST DI SICUREZZA COMPLETATI!")
    print("✅ Il sistema di normalizzazione è sicuro e robusto!")

if __name__ == "__main__":
    main()
