{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { DateTimePickerTabs } from \"./DateTimePickerTabs.js\";\nimport { DateTimePickerToolbar } from \"./DateTimePickerToolbar.js\";\nimport { applyDefaultViewProps } from \"../internals/utils/views.js\";\nimport { resolveTimeViewsResponse } from \"../internals/utils/date-time-utils.js\";\nimport { useApplyDefaultValuesToDateTimeValidationProps } from \"../managers/useDateTimeManager.js\";\nexport function useDateTimePickerDefaultizedProps(props, name) {\n  const utils = useUtils();\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  const validationProps = useApplyDefaultValuesToDateTimeValidationProps(themeProps);\n  const ampm = themeProps.ampm ?? utils.is12HourCycleInCurrentLocale();\n  const localeText = React.useMemo(() => {\n    if (themeProps.localeText?.toolbarTitle == null) {\n      return themeProps.localeText;\n    }\n    return _extends({}, themeProps.localeText, {\n      dateTimePickerToolbarTitle: themeProps.localeText.toolbarTitle\n    });\n  }, [themeProps.localeText]);\n  const {\n    openTo,\n    views: defaultViews\n  } = applyDefaultViewProps({\n    views: themeProps.views,\n    openTo: themeProps.openTo,\n    defaultViews: ['year', 'day', 'hours', 'minutes'],\n    defaultOpenTo: 'day'\n  });\n  const {\n    shouldRenderTimeInASingleColumn,\n    thresholdToRenderTimeInASingleColumn,\n    views,\n    timeSteps\n  } = resolveTimeViewsResponse({\n    thresholdToRenderTimeInASingleColumn: themeProps.thresholdToRenderTimeInASingleColumn,\n    ampm,\n    timeSteps: themeProps.timeSteps,\n    views: defaultViews\n  });\n  return _extends({}, themeProps, validationProps, {\n    timeSteps,\n    openTo,\n    shouldRenderTimeInASingleColumn,\n    thresholdToRenderTimeInASingleColumn,\n    views,\n    ampm,\n    localeText,\n    orientation: themeProps.orientation ?? 'portrait',\n    slots: _extends({\n      toolbar: DateTimePickerToolbar,\n      tabs: DateTimePickerTabs\n    }, themeProps.slots),\n    slotProps: _extends({}, themeProps.slotProps, {\n      toolbar: _extends({\n        ampm\n      }, themeProps.slotProps?.toolbar)\n    })\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "useThemeProps", "useUtils", "DateTimePickerTabs", "DateTimePickerToolbar", "applyDefaultViewProps", "resolveTimeViewsResponse", "useApplyDefaultValuesToDateTimeValidationProps", "useDateTimePickerDefaultizedProps", "props", "name", "utils", "themeProps", "validationProps", "ampm", "is12HourCycleInCurrentLocale", "localeText", "useMemo", "toolbarTitle", "dateTimePickerToolbarTitle", "openTo", "views", "defaultViews", "defaultOpenTo", "shouldRenderTimeInASingleColumn", "thresholdToRenderTimeInASingleColumn", "timeSteps", "orientation", "slots", "toolbar", "tabs", "slotProps"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/DateTimePicker/shared.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { DateTimePickerTabs } from \"./DateTimePickerTabs.js\";\nimport { DateTimePickerToolbar } from \"./DateTimePickerToolbar.js\";\nimport { applyDefaultViewProps } from \"../internals/utils/views.js\";\nimport { resolveTimeViewsResponse } from \"../internals/utils/date-time-utils.js\";\nimport { useApplyDefaultValuesToDateTimeValidationProps } from \"../managers/useDateTimeManager.js\";\nexport function useDateTimePickerDefaultizedProps(props, name) {\n  const utils = useUtils();\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  const validationProps = useApplyDefaultValuesToDateTimeValidationProps(themeProps);\n  const ampm = themeProps.ampm ?? utils.is12HourCycleInCurrentLocale();\n  const localeText = React.useMemo(() => {\n    if (themeProps.localeText?.toolbarTitle == null) {\n      return themeProps.localeText;\n    }\n    return _extends({}, themeProps.localeText, {\n      dateTimePickerToolbarTitle: themeProps.localeText.toolbarTitle\n    });\n  }, [themeProps.localeText]);\n  const {\n    openTo,\n    views: defaultViews\n  } = applyDefaultViewProps({\n    views: themeProps.views,\n    openTo: themeProps.openTo,\n    defaultViews: ['year', 'day', 'hours', 'minutes'],\n    defaultOpenTo: 'day'\n  });\n  const {\n    shouldRenderTimeInASingleColumn,\n    thresholdToRenderTimeInASingleColumn,\n    views,\n    timeSteps\n  } = resolveTimeViewsResponse({\n    thresholdToRenderTimeInASingleColumn: themeProps.thresholdToRenderTimeInASingleColumn,\n    ampm,\n    timeSteps: themeProps.timeSteps,\n    views: defaultViews\n  });\n  return _extends({}, themeProps, validationProps, {\n    timeSteps,\n    openTo,\n    shouldRenderTimeInASingleColumn,\n    thresholdToRenderTimeInASingleColumn,\n    views,\n    ampm,\n    localeText,\n    orientation: themeProps.orientation ?? 'portrait',\n    slots: _extends({\n      toolbar: DateTimePickerToolbar,\n      tabs: DateTimePickerTabs\n    }, themeProps.slots),\n    slotProps: _extends({}, themeProps.slotProps, {\n      toolbar: _extends({\n        ampm\n      }, themeProps.slotProps?.toolbar)\n    })\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,8CAA8C,QAAQ,mCAAmC;AAClG,OAAO,SAASC,iCAAiCA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC7D,MAAMC,KAAK,GAAGT,QAAQ,CAAC,CAAC;EACxB,MAAMU,UAAU,GAAGX,aAAa,CAAC;IAC/BQ,KAAK;IACLC;EACF,CAAC,CAAC;EACF,MAAMG,eAAe,GAAGN,8CAA8C,CAACK,UAAU,CAAC;EAClF,MAAME,IAAI,GAAGF,UAAU,CAACE,IAAI,IAAIH,KAAK,CAACI,4BAA4B,CAAC,CAAC;EACpE,MAAMC,UAAU,GAAGhB,KAAK,CAACiB,OAAO,CAAC,MAAM;IACrC,IAAIL,UAAU,CAACI,UAAU,EAAEE,YAAY,IAAI,IAAI,EAAE;MAC/C,OAAON,UAAU,CAACI,UAAU;IAC9B;IACA,OAAOjB,QAAQ,CAAC,CAAC,CAAC,EAAEa,UAAU,CAACI,UAAU,EAAE;MACzCG,0BAA0B,EAAEP,UAAU,CAACI,UAAU,CAACE;IACpD,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,UAAU,CAACI,UAAU,CAAC,CAAC;EAC3B,MAAM;IACJI,MAAM;IACNC,KAAK,EAAEC;EACT,CAAC,GAAGjB,qBAAqB,CAAC;IACxBgB,KAAK,EAAET,UAAU,CAACS,KAAK;IACvBD,MAAM,EAAER,UAAU,CAACQ,MAAM;IACzBE,YAAY,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC;IACjDC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM;IACJC,+BAA+B;IAC/BC,oCAAoC;IACpCJ,KAAK;IACLK;EACF,CAAC,GAAGpB,wBAAwB,CAAC;IAC3BmB,oCAAoC,EAAEb,UAAU,CAACa,oCAAoC;IACrFX,IAAI;IACJY,SAAS,EAAEd,UAAU,CAACc,SAAS;IAC/BL,KAAK,EAAEC;EACT,CAAC,CAAC;EACF,OAAOvB,QAAQ,CAAC,CAAC,CAAC,EAAEa,UAAU,EAAEC,eAAe,EAAE;IAC/Ca,SAAS;IACTN,MAAM;IACNI,+BAA+B;IAC/BC,oCAAoC;IACpCJ,KAAK;IACLP,IAAI;IACJE,UAAU;IACVW,WAAW,EAAEf,UAAU,CAACe,WAAW,IAAI,UAAU;IACjDC,KAAK,EAAE7B,QAAQ,CAAC;MACd8B,OAAO,EAAEzB,qBAAqB;MAC9B0B,IAAI,EAAE3B;IACR,CAAC,EAAES,UAAU,CAACgB,KAAK,CAAC;IACpBG,SAAS,EAAEhC,QAAQ,CAAC,CAAC,CAAC,EAAEa,UAAU,CAACmB,SAAS,EAAE;MAC5CF,OAAO,EAAE9B,QAAQ,CAAC;QAChBe;MACF,CAAC,EAAEF,UAAU,CAACmB,SAAS,EAAEF,OAAO;IAClC,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}