import axiosInstance from './axiosConfig';

const proveDettagliateService = {
  // Ottiene la lista delle prove dettagliate di una certificazione
  getProve: async (cantiereId, certificazioneId, tipoProva = null) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const params = {};
      if (tipoProva) {
        params.tipo_prova = tipoProva;
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove`, {
        params
      });
      return response.data;
    } catch (error) {
      console.error('Get prove error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea una nuova prova dettagliata
  createProva: async (cantiereId, certificazioneId, provaData) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove`, provaData);
      return response.data;
    } catch (error) {
      console.error('Create prova error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i dettagli di una prova dettagliata
  getProva: async (cantiereId, certificazioneId, provaId) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove/${provaId}`);
      return response.data;
    } catch (error) {
      console.error('Get prova error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna una prova dettagliata
  updateProva: async (cantiereId, certificazioneId, provaId, provaData) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove/${provaId}`, provaData);
      return response.data;
    } catch (error) {
      console.error('Update prova error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Elimina una prova dettagliata
  deleteProva: async (cantiereId, certificazioneId, provaId) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/certificazioni/${certificazioneId}/prove/${provaId}`);
      return response.data;
    } catch (error) {
      console.error('Delete prova error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i tipi di prova disponibili
  getTipiProva: async () => {
    try {
      const response = await axiosInstance.get('/cantieri/tipi-prova');
      return response.data;
    } catch (error) {
      console.error('Get tipi prova error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default proveDettagliateService;
