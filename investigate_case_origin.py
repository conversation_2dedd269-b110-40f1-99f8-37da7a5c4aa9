#!/usr/bin/env python3
"""
Script per investigare l'origine del problema di case sensitivity.
<PERSON>li<PERSON> quando e come sono state create le varianti con x minuscola e X maiuscola.
"""

import psycopg2
import psycopg2.extras
from modules.database_pg import Database

def investigate_case_origin():
    """Investiga l'origine del problema di case sensitivity."""
    print("🔍 INVESTIGAZIONE ORIGINE CASE SENSITIVITY")
    print("=" * 60)
    
    db = Database()
    conn = db.get_dict_cursor_connection()
    
    try:
        with conn.cursor() as cur:
            cantiere_id = 1
            
            print("📋 STEP 1: ANALISI STORICA CAVI FG16OR16 240MM2")
            print("-" * 50)
            
            # Analizza tutti i cavi FG16OR16 240MM2 con dettagli temporali
            cur.execute("""
                SELECT 
                    id_cavo,
                    tipologia,
                    sezione,
                    stato_installazione,
                    metratura_reale,
                    id_bobina,
                    modificato_manualmente,
                    timestamp,
                    CASE 
                        WHEN sezione = '1x240MM2' THEN 'MINUSCOLA'
                        WHEN sezione = '1X240MM2' THEN 'MAIUSCOLA'
                        ELSE 'ALTRO'
                    END as case_type
                FROM cavi
                WHERE id_cantiere = %s 
                  AND tipologia ILIKE %s 
                  AND sezione ILIKE %s
                ORDER BY timestamp ASC, id_cavo
            """, (cantiere_id, '%FG16OR16%', '%240MM2%'))
            
            cavi_results = cur.fetchall()
            
            print(f"Trovati {len(cavi_results)} cavi FG16OR16 240MM2:")
            
            minuscola_count = 0
            maiuscola_count = 0
            
            for i, cavo in enumerate(cavi_results, 1):
                case_type = cavo['case_type']
                if case_type == 'MINUSCOLA':
                    minuscola_count += 1
                elif case_type == 'MAIUSCOLA':
                    maiuscola_count += 1
                
                print(f"\n{i}. {cavo['id_cavo']} | {cavo['tipologia']} | '{cavo['sezione']}'")
                print(f"   Case Type: {case_type}")
                print(f"   Stato: {cavo['stato_installazione']}")
                print(f"   Metri reali: {cavo['metratura_reale']}")
                print(f"   Bobina: {cavo['id_bobina']}")
                print(f"   Modificato: {cavo['modificato_manualmente']}")
                print(f"   Timestamp: {cavo['timestamp']}")
                
                # Evidenzia pattern sospetti
                if cavo['modificato_manualmente'] and cavo['modificato_manualmente'] >= 10:
                    print(f"   ⚠️  MODIFICATO DALLA NORMALIZZAZIONE (modificato_manualmente >= 10)")
                elif cavo['modificato_manualmente'] == 1:
                    print(f"   📝 Modificato manualmente")
                elif cavo['modificato_manualmente'] == 0 or cavo['modificato_manualmente'] is None:
                    print(f"   📥 Dato originale (non modificato)")
            
            print(f"\n📊 RIEPILOGO CASE SENSITIVITY:")
            print(f"   Cavi con 'x' minuscola: {minuscola_count}")
            print(f"   Cavi con 'X' maiuscola: {maiuscola_count}")
            
            print("\n📦 STEP 2: ANALISI STORICA BOBINE FG16OR16 240MM2")
            print("-" * 50)
            
            # Analizza le bobine
            cur.execute("""
                SELECT 
                    id_bobina,
                    numero_bobina,
                    tipologia,
                    sezione,
                    metri_totali,
                    metri_residui,
                    stato_bobina,
                    CASE 
                        WHEN sezione = '1x240MM2' THEN 'MINUSCOLA'
                        WHEN sezione = '1X240MM2' THEN 'MAIUSCOLA'
                        ELSE 'ALTRO'
                    END as case_type
                FROM parco_cavi
                WHERE id_cantiere = %s 
                  AND tipologia ILIKE %s 
                  AND sezione ILIKE %s
                ORDER BY id_bobina
            """, (cantiere_id, '%FG16OR16%', '%240MM2%'))
            
            bobine_results = cur.fetchall()
            
            print(f"Trovate {len(bobine_results)} bobine FG16OR16 240MM2:")
            
            for i, bobina in enumerate(bobine_results, 1):
                print(f"\n{i}. {bobina['id_bobina']} | {bobina['tipologia']} | '{bobina['sezione']}'")
                print(f"   Case Type: {bobina['case_type']}")
                print(f"   Numero: {bobina['numero_bobina']}")
                print(f"   Metri totali: {bobina['metri_totali']}")
                print(f"   Metri residui: {bobina['metri_residui']}")
                print(f"   Stato: {bobina['stato_bobina']}")
            
            print("\n🔍 STEP 3: ANALISI PATTERN DI CREAZIONE")
            print("-" * 50)
            
            # Analizza i pattern di timestamp per capire l'ordine di creazione
            if cavi_results:
                print("Ordine cronologico di creazione/modifica:")
                
                for i, cavo in enumerate(cavi_results):
                    timestamp_str = cavo['timestamp'].strftime('%Y-%m-%d %H:%M:%S') if cavo['timestamp'] else 'N/A'
                    print(f"{i+1}. {cavo['id_cavo']} | '{cavo['sezione']}' | {timestamp_str}")
                    
                    if i == 0:
                        print(f"   🥇 PRIMO CAVO CREATO")
                    elif cavo['modificato_manualmente'] and cavo['modificato_manualmente'] >= 10:
                        print(f"   🔧 NORMALIZZATO SUCCESSIVAMENTE")
            
            print("\n💡 STEP 4: IPOTESI SULL'ORIGINE")
            print("-" * 50)
            
            # Analizza possibili cause
            original_minuscola = [c for c in cavi_results if (c['modificato_manualmente'] is None or c['modificato_manualmente'] < 10) and c['case_type'] == 'MINUSCOLA']
            original_maiuscola = [c for c in cavi_results if (c['modificato_manualmente'] is None or c['modificato_manualmente'] < 10) and c['case_type'] == 'MAIUSCOLA']
            
            print(f"Cavi originali con 'x' minuscola: {len(original_minuscola)}")
            print(f"Cavi originali con 'X' maiuscola: {len(original_maiuscola)}")
            
            if len(original_minuscola) > 0 and len(original_maiuscola) > 0:
                print("\n🚨 CAUSA PROBABILE: IMPORTAZIONE INCONSISTENTE")
                print("   I dati sono stati importati con case sensitivity diversa in momenti diversi")
                print("   Possibili cause:")
                print("   - Import da file Excel/CSV con formattazione inconsistente")
                print("   - Inserimento manuale con case diversa")
                print("   - Import da sistemi diversi")
            elif len(original_minuscola) > 0:
                print("\n📥 CAUSA PROBABILE: IMPORT ORIGINALE CON 'x' MINUSCOLA")
                print("   Tutti i dati originali avevano 'x' minuscola")
                print("   La 'X' maiuscola è stata introdotta successivamente")
            elif len(original_maiuscola) > 0:
                print("\n📥 CAUSA PROBABILE: IMPORT ORIGINALE CON 'X' MAIUSCOLA")
                print("   Tutti i dati originali avevano 'X' maiuscola")
                print("   La 'x' minuscola è stata introdotta successivamente")
            
            print("\n🔧 STEP 5: RACCOMANDAZIONI PREVENTIVE")
            print("-" * 50)
            print("Per evitare problemi futuri:")
            print("1. 📝 Standardizzare l'input dei dati (sempre maiuscolo)")
            print("2. 🔍 Validazione case-insensitive nell'import")
            print("3. 🛡️  Normalizzazione automatica nei form di inserimento")
            print("4. 📊 Query BOQ case-insensitive come backup")
                
    except Exception as e:
        print(f"❌ Errore durante l'investigazione: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    investigate_case_origin()
