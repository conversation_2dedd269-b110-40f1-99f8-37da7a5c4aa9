{"ast": null, "code": "'use client';\n\nimport { useField } from \"../internals/hooks/useField/index.js\";\nimport { useDateTimeManager } from \"../managers/index.js\";\nexport const useDateTimeField = props => {\n  const manager = useDateTimeManager(props);\n  return useField({\n    manager,\n    props\n  });\n};", "map": {"version": 3, "names": ["useField", "useDateTimeManager", "useDateTimeField", "props", "manager"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/DateTimeField/useDateTimeField.js"], "sourcesContent": ["'use client';\n\nimport { useField } from \"../internals/hooks/useField/index.js\";\nimport { useDateTimeManager } from \"../managers/index.js\";\nexport const useDateTimeField = props => {\n  const manager = useDateTimeManager(props);\n  return useField({\n    manager,\n    props\n  });\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,sCAAsC;AAC/D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,OAAO,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EACvC,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,KAAK,CAAC;EACzC,OAAOH,QAAQ,CAAC;IACdI,OAAO;IACPD;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}