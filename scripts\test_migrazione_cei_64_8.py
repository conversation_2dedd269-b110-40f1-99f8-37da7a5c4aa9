#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di test per verificare la migrazione CEI 64-8.
Controlla che tutte le tabelle e colonne siano state create correttamente.
"""

import sys
import os
import logging
from datetime import datetime

# Aggiungi la directory principale al path per importare i moduli
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importa il modulo database_pg
from modules.database_pg import Database

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_table_exists(cursor, table_name):
    """Verifica se una tabella esiste."""
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name = %s
    """, (table_name.lower(),))
    return cursor.fetchone() is not None

def check_column_exists(cursor, table_name, column_name):
    """Verifica se una colonna esiste in una tabella."""
    cursor.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = %s AND column_name = %s
    """, (table_name.lower(), column_name.lower()))
    return cursor.fetchone() is not None

def check_foreign_key_exists(cursor, table_name, constraint_name):
    """Verifica se un foreign key constraint esiste."""
    cursor.execute("""
        SELECT constraint_name 
        FROM information_schema.table_constraints 
        WHERE table_name = %s AND constraint_name = %s AND constraint_type = 'FOREIGN KEY'
    """, (table_name.lower(), constraint_name.lower()))
    return cursor.fetchone() is not None

def test_certificazioni_cavi_table(cursor):
    """Testa la tabella CertificazioniCavi aggiornata."""
    logging.info("🔍 Test tabella CertificazioniCavi...")
    
    if not check_table_exists(cursor, 'certificazionicavi'):
        logging.error("  ❌ Tabella CertificazioniCavi non trovata!")
        return False
    
    # Colonne che dovrebbero esistere dopo la migrazione
    expected_columns = [
        'id_certificazione', 'id_cantiere', 'id_cavo', 'numero_certificato',
        'data_certificazione', 'id_operatore', 'strumento_utilizzato',
        'id_strumento', 'lunghezza_misurata', 'valore_continuita',
        'valore_isolamento', 'valore_resistenza', 'percorso_certificato',
        'percorso_foto', 'note', 'timestamp_creazione', 'timestamp_modifica',
        # Nuove colonne aggiunte
        'tipo_certificato', 'stato_certificato', 'designazione_funzionale',
        'tensione_nominale', 'tensione_prova_isolamento', 'durata_prova_isolamento',
        'valore_minimo_isolamento', 'temperatura_prova', 'umidita_prova',
        'esito_complessivo', 'id_rapporto'
    ]
    
    missing_columns = []
    for column in expected_columns:
        if not check_column_exists(cursor, 'certificazionicavi', column):
            missing_columns.append(column)
    
    if missing_columns:
        logging.error(f"  ❌ Colonne mancanti: {', '.join(missing_columns)}")
        return False
    else:
        logging.info(f"  ✅ Tutte le {len(expected_columns)} colonne presenti")
        return True

def test_strumenti_certificati_table(cursor):
    """Testa la tabella StrumentiCertificati aggiornata."""
    logging.info("🔍 Test tabella StrumentiCertificati...")
    
    if not check_table_exists(cursor, 'strumenticertificati'):
        logging.error("  ❌ Tabella StrumentiCertificati non trovata!")
        return False
    
    # Colonne che dovrebbero esistere dopo la migrazione
    expected_columns = [
        'id_strumento', 'nome', 'marca', 'modello', 'numero_serie',
        'data_calibrazione', 'data_scadenza_calibrazione', 'certificato_calibrazione',
        'note', 'id_cantiere', 'timestamp_creazione', 'timestamp_modifica',
        # Nuove colonne aggiunte
        'tipo_strumento', 'ente_certificatore', 'range_misura',
        'precisione', 'stato_strumento'
    ]
    
    missing_columns = []
    for column in expected_columns:
        if not check_column_exists(cursor, 'strumenticertificati', column):
            missing_columns.append(column)
    
    if missing_columns:
        logging.error(f"  ❌ Colonne mancanti: {', '.join(missing_columns)}")
        return False
    else:
        logging.info(f"  ✅ Tutte le {len(expected_columns)} colonne presenti")
        return True

def test_rapporti_generali_collaudo_table(cursor):
    """Testa la nuova tabella RapportiGeneraliCollaudo."""
    logging.info("🔍 Test tabella RapportiGeneraliCollaudo...")
    
    if not check_table_exists(cursor, 'rapportigeneralicollaudo'):
        logging.error("  ❌ Tabella RapportiGeneraliCollaudo non trovata!")
        return False
    
    # Colonne che dovrebbero esistere
    expected_columns = [
        'id_rapporto', 'id_cantiere', 'numero_rapporto', 'data_rapporto',
        'nome_progetto', 'codice_progetto', 'cliente_finale', 'localita_impianto',
        'societa_installatrice', 'societa_responsabile_prove', 'data_inizio_collaudo',
        'data_fine_collaudo', 'normative_applicate', 'documentazione_progetto',
        'scopo_rapporto', 'ambito_collaudo', 'temperatura_ambiente', 'umidita_ambiente',
        'numero_cavi_totali', 'numero_cavi_conformi', 'numero_cavi_non_conformi',
        'responsabile_tecnico', 'rappresentante_cliente', 'stato_rapporto',
        'conclusioni', 'dichiarazione_conformita', 'timestamp_creazione', 'timestamp_modifica'
    ]
    
    missing_columns = []
    for column in expected_columns:
        if not check_column_exists(cursor, 'rapportigeneralicollaudo', column):
            missing_columns.append(column)
    
    if missing_columns:
        logging.error(f"  ❌ Colonne mancanti: {', '.join(missing_columns)}")
        return False
    else:
        logging.info(f"  ✅ Tutte le {len(expected_columns)} colonne presenti")
        return True

def test_prove_dettagliate_table(cursor):
    """Testa la nuova tabella ProveDettagliate."""
    logging.info("🔍 Test tabella ProveDettagliate...")
    
    if not check_table_exists(cursor, 'provedettagliate'):
        logging.error("  ❌ Tabella ProveDettagliate non trovata!")
        return False
    
    # Colonne che dovrebbero esistere
    expected_columns = [
        'id_prova', 'id_certificazione', 'tipo_prova', 'data_prova',
        'operatore', 'condizioni_ambientali', 'risultati', 'valori_misurati',
        'valori_attesi', 'esito', 'note_prova'
    ]
    
    missing_columns = []
    for column in expected_columns:
        if not check_column_exists(cursor, 'provedettagliate', column):
            missing_columns.append(column)
    
    if missing_columns:
        logging.error(f"  ❌ Colonne mancanti: {', '.join(missing_columns)}")
        return False
    else:
        logging.info(f"  ✅ Tutte le {len(expected_columns)} colonne presenti")
        return True

def test_non_conformita_table(cursor):
    """Testa la nuova tabella NonConformita."""
    logging.info("🔍 Test tabella NonConformita...")
    
    if not check_table_exists(cursor, 'nonconformita'):
        logging.error("  ❌ Tabella NonConformita non trovata!")
        return False
    
    # Colonne che dovrebbero esistere
    expected_columns = [
        'id_nc', 'id_certificazione', 'id_rapporto', 'codice_nc',
        'data_rilevazione', 'tipo_nc', 'descrizione', 'riferimento_cavo',
        'riferimento_prova', 'azione_correttiva', 'responsabile_azione',
        'data_scadenza_azione', 'data_completamento_azione', 'esito_riverifica',
        'note_riverifica', 'stato_nc'
    ]
    
    missing_columns = []
    for column in expected_columns:
        if not check_column_exists(cursor, 'nonconformita', column):
            missing_columns.append(column)
    
    if missing_columns:
        logging.error(f"  ❌ Colonne mancanti: {', '.join(missing_columns)}")
        return False
    else:
        logging.info(f"  ✅ Tutte le {len(expected_columns)} colonne presenti")
        return True

def test_foreign_keys(cursor):
    """Testa i foreign key constraints."""
    logging.info("🔍 Test foreign key constraints...")
    
    # Foreign key che dovrebbero esistere
    expected_fks = [
        ('certificazionicavi', 'fk_rapporto'),
        ('provedettagliate', 'provedettagliate_id_certificazione_fkey'),
        ('nonconformita', 'nonconformita_id_certificazione_fkey'),
        ('nonconformita', 'nonconformita_id_rapporto_fkey')
    ]
    
    missing_fks = []
    for table_name, constraint_name in expected_fks:
        if not check_foreign_key_exists(cursor, table_name, constraint_name):
            # Per alcuni constraint, PostgreSQL genera nomi automatici
            # Verifichiamo almeno che esistano foreign key sulla tabella
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.table_constraints 
                WHERE table_name = %s AND constraint_type = 'FOREIGN KEY'
            """, (table_name.lower(),))
            fk_count = cursor.fetchone()[0]
            if fk_count == 0:
                missing_fks.append(f"{table_name}.{constraint_name}")
    
    if missing_fks:
        logging.warning(f"  ⚠️ Alcuni foreign key potrebbero avere nomi diversi: {', '.join(missing_fks)}")
    else:
        logging.info("  ✅ Foreign key constraints presenti")
    
    return True

def test_data_integrity(cursor):
    """Testa l'integrità dei dati dopo la migrazione."""
    logging.info("🔍 Test integrità dati...")
    
    try:
        # Test conteggio record esistenti
        cursor.execute("SELECT COUNT(*) FROM CertificazioniCavi")
        cert_count = cursor.fetchone()[0]
        logging.info(f"  📊 Certificazioni esistenti: {cert_count}")
        
        cursor.execute("SELECT COUNT(*) FROM StrumentiCertificati")
        strumenti_count = cursor.fetchone()[0]
        logging.info(f"  📊 Strumenti esistenti: {strumenti_count}")
        
        # Test valori di default per nuove colonne
        if cert_count > 0:
            cursor.execute("""
                SELECT COUNT(*) FROM CertificazioniCavi 
                WHERE tipo_certificato = 'SINGOLO' AND stato_certificato = 'BOZZA'
            """)
            default_values_count = cursor.fetchone()[0]
            logging.info(f"  📊 Certificazioni con valori di default: {default_values_count}")
        
        logging.info("  ✅ Integrità dati verificata")
        return True
        
    except Exception as e:
        logging.error(f"  ❌ Errore nel test integrità dati: {str(e)}")
        return False

def run_migration_tests():
    """
    Esegue tutti i test per verificare la migrazione CEI 64-8.
    """
    logging.info("🧪 INIZIO TEST MIGRAZIONE CEI 64-8")
    
    db = Database()
    
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Lista dei test da eseguire
            tests = [
                ("CertificazioniCavi", test_certificazioni_cavi_table),
                ("StrumentiCertificati", test_strumenti_certificati_table),
                ("RapportiGeneraliCollaudo", test_rapporti_generali_collaudo_table),
                ("ProveDettagliate", test_prove_dettagliate_table),
                ("NonConformita", test_non_conformita_table),
                ("Foreign Keys", test_foreign_keys),
                ("Integrità Dati", test_data_integrity)
            ]
            
            # Esegui tutti i test
            passed_tests = 0
            total_tests = len(tests)
            
            for test_name, test_function in tests:
                try:
                    if test_function(cursor):
                        passed_tests += 1
                except Exception as e:
                    logging.error(f"❌ Errore nel test {test_name}: {str(e)}")
            
            # Riepilogo risultati
            logging.info(f"📊 RISULTATI TEST: {passed_tests}/{total_tests} test superati")
            
            if passed_tests == total_tests:
                logging.info("✅ TUTTI I TEST SUPERATI - Migrazione completata con successo!")
                return True
            else:
                logging.warning(f"⚠️ {total_tests - passed_tests} test falliti - Verificare la migrazione")
                return False
            
    except Exception as e:
        logging.error(f"❌ Errore durante i test: {str(e)}")
        return False

if __name__ == "__main__":
    run_migration_tests()
