{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'Praėjusį' eeee p\",\n  yesterday: \"'<PERSON><PERSON><PERSON>' p\",\n  today: \"'Šiandien' p\",\n  tomorrow: \"'Rytoj' p\",\n  nextWeek: \"eeee p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/lt/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'Praėjusį' eeee p\",\n  yesterday: \"'<PERSON><PERSON><PERSON>' p\",\n  today: \"'Šiandien' p\",\n  tomorrow: \"'Rytoj' p\",\n  nextWeek: \"eeee p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,WAAW;EACtBC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,WAAW;EACrBC,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}