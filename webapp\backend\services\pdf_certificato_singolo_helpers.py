"""
Helper methods per la generazione di PDF dei certificati singoli CEI 64-8
"""

import json
from datetime import datetime
from typing import List
from reportlab.lib import colors
from reportlab.platypus import Table, TableStyle, Paragraph, Spacer
from reportlab.lib.units import cm

from backend.models.certificazione_cavo import CertificazioneCavo
from backend.models.prova_dettagliata import ProvaDettagliata
from backend.models.strumento_certificato import StrumentoCertificato
from backend.models.cantiere import Cantiere
from backend.models.rapporto_generale_collaudo import RapportoGeneraleCollaudo


class PDFCertificatoSingoloHelpers:
    """Helper methods per certificati singoli"""
    
    def __init__(self, pdf_service):
        self.pdf_service = pdf_service
        self.db = pdf_service.db
        self.styles = pdf_service.styles
    
    def build_identificazione_certificato_section(self, certificazione: CertificazioneCavo, cantiere: Cantiere) -> List:
        """Costruisce la sezione Identificazione del Certificato"""
        content = []
        
        content.append(Paragraph("1. IDENTIFICAZIONE DEL CERTIFICATO", self.styles['SectionHeader']))
        
        data = [
            ['Numero Certificato:', certificazione.numero_certificato],
            ['Data Emissione:', certificazione.data_certificazione.strftime('%d/%m/%Y')],
            ['Cantiere:', cantiere.nome if cantiere else '-'],
            ['Tipo Certificato:', certificazione.tipo_certificato or 'SINGOLO'],
            ['Stato Certificato:', certificazione.stato_certificato or 'COMPLETATO'],
        ]
        
        if certificazione.id_rapporto:
            rapporto = self.db.query(RapportoGeneraleCollaudo).filter(
                RapportoGeneraleCollaudo.id_rapporto == certificazione.id_rapporto
            ).first()
            if rapporto:
                data.append(['Rapporto Generale di Riferimento:', rapporto.numero_rapporto])
        
        table = Table(data, colWidths=[6*cm, 10*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))
        
        content.append(table)
        content.append(Spacer(1, 20))
        
        return content
    
    def build_dati_cavo_section(self, certificazione: CertificazioneCavo) -> List:
        """Costruisce la sezione Dati Identificativi del Cavo"""
        content = []
        
        content.append(Paragraph("2. DATI IDENTIFICATIVI DEL CAVO", self.styles['SectionHeader']))
        
        data = [
            ['Sigla Cavo (ID Univoco):', certificazione.id_cavo],
            ['Designazione Funzionale:', certificazione.designazione_funzionale or '-'],
            ['Tensione Nominale:', certificazione.tensione_nominale or '-'],
            ['Lunghezza Posata (m):', str(certificazione.lunghezza_misurata or '-')],
        ]
        
        table = Table(data, colWidths=[6*cm, 10*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))
        
        content.append(table)
        content.append(Spacer(1, 20))
        
        return content
    
    def build_prove_risultati_section(self, certificazione: CertificazioneCavo, prove: List[ProvaDettagliata]) -> List:
        """Costruisce la sezione Prove Eseguite e Risultati"""
        content = []
        
        content.append(Paragraph("3. PROVE ESEGUITE E RISULTATI", self.styles['SectionHeader']))
        
        # Condizioni ambientali generali
        if certificazione.temperatura_prova or certificazione.umidita_prova:
            content.append(Paragraph("<b>Condizioni Ambientali:</b>", self.styles['Normal']))
            condizioni = []
            if certificazione.temperatura_prova:
                condizioni.append(f"Temperatura: {certificazione.temperatura_prova}°C")
            if certificazione.umidita_prova:
                condizioni.append(f"Umidità: {certificazione.umidita_prova}%")
            content.append(Paragraph(" - ".join(condizioni), self.styles['Normal']))
            content.append(Spacer(1, 10))
        
        # Prove base (legacy)
        content.append(Paragraph("3.1 PROVE BASE", self.styles['Normal']))
        
        prove_base_data = [
            ['Tipo Prova', 'Risultato', 'Esito'],
            ['Continuità', certificazione.valore_continuita or '-', 'OK' if certificazione.valore_continuita else '-'],
            ['Isolamento', f"{certificazione.valore_isolamento or '-'} MΩ", 'OK' if certificazione.valore_isolamento else '-'],
            ['Resistenza', certificazione.valore_resistenza or '-', 'OK' if certificazione.valore_resistenza else '-'],
        ]
        
        table_base = Table(prove_base_data, colWidths=[5*cm, 6*cm, 3*cm])
        table_base.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))
        
        content.append(table_base)
        content.append(Spacer(1, 15))
        
        # Prove dettagliate CEI 64-8
        if prove:
            content.append(Paragraph("3.2 PROVE DETTAGLIATE CEI 64-8", self.styles['Normal']))
            
            for i, prova in enumerate(prove, 1):
                content.append(Paragraph(f"<b>Prova {i}: {prova.tipo_prova}</b>", self.styles['Normal']))
                
                prova_data = [
                    ['Data/Ora Prova:', prova.data_prova.strftime('%d/%m/%Y %H:%M') if prova.data_prova else '-'],
                    ['Operatore:', prova.operatore or '-'],
                    ['Esito:', prova.esito],
                ]
                
                # Aggiungi condizioni ambientali specifiche se presenti
                if prova.condizioni_ambientali:
                    try:
                        condizioni = prova.condizioni_ambientali
                        if isinstance(condizioni, str):
                            condizioni = json.loads(condizioni)
                        
                        if condizioni.get('temperatura'):
                            prova_data.append(['Temperatura:', f"{condizioni['temperatura']}°C"])
                        if condizioni.get('umidita'):
                            prova_data.append(['Umidità:', f"{condizioni['umidita']}%"])
                    except:
                        pass
                
                # Aggiungi risultati specifici
                if prova.risultati:
                    try:
                        risultati = prova.risultati
                        if isinstance(risultati, str):
                            risultati = json.loads(risultati)
                        
                        for key, value in risultati.items():
                            prova_data.append([key.replace('_', ' ').title() + ':', str(value)])
                    except:
                        pass
                
                # Aggiungi valori misurati
                if prova.valori_misurati:
                    try:
                        valori = prova.valori_misurati
                        if isinstance(valori, str):
                            valori = json.loads(valori)
                        
                        for key, value in valori.items():
                            prova_data.append([key.replace('_', ' ').title() + ':', str(value)])
                    except:
                        pass
                
                if prova.note_prova:
                    prova_data.append(['Note:', prova.note_prova])
                
                table_prova = Table(prova_data, colWidths=[4*cm, 12*cm])
                table_prova.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (0, -1), colors.lightyellow),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ]))
                
                content.append(table_prova)
                content.append(Spacer(1, 10))
        
        content.append(Spacer(1, 20))
        
        return content
    
    def build_strumentazione_certificato_section(self, strumento: StrumentoCertificato) -> List:
        """Costruisce la sezione Strumentazione per certificato singolo"""
        content = []
        
        content.append(Paragraph("4. STRUMENTAZIONE UTILIZZATA", self.styles['SectionHeader']))
        
        data = [
            ['Nome Strumento:', strumento.nome],
            ['Marca:', strumento.marca],
            ['Modello:', strumento.modello],
            ['Numero di Serie:', strumento.numero_serie],
            ['Tipo Strumento:', strumento.tipo_strumento or '-'],
            ['Range di Misura:', strumento.range_misura or '-'],
            ['Precisione:', strumento.precisione or '-'],
            ['Data Ultima Taratura:', strumento.data_calibrazione.strftime('%d/%m/%Y')],
            ['Data Scadenza Taratura:', strumento.data_scadenza_calibrazione.strftime('%d/%m/%Y')],
            ['Ente Certificatore:', strumento.ente_certificatore or '-'],
            ['Stato Strumento:', strumento.stato_strumento or 'ATTIVO'],
        ]
        
        table = Table(data, colWidths=[5*cm, 11*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightcyan),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))
        
        content.append(table)
        content.append(Spacer(1, 20))
        
        return content
    
    def build_esito_complessivo_section(self, certificazione: CertificazioneCavo) -> List:
        """Costruisce la sezione Esito Complessivo"""
        content = []
        
        content.append(Paragraph("5. ESITO COMPLESSIVO DEL COLLAUDO", self.styles['SectionHeader']))
        
        esito = certificazione.esito_complessivo or 'NON DEFINITO'
        
        # Colore basato sull'esito
        if esito == 'CONFORME':
            bg_color = colors.lightgreen
            text_color = colors.darkgreen
        elif esito == 'NON_CONFORME':
            bg_color = colors.lightcoral
            text_color = colors.darkred
        else:
            bg_color = colors.lightyellow
            text_color = colors.darkorange
        
        data = [
            ['ESITO COMPLESSIVO:', esito],
        ]
        
        if certificazione.note:
            data.append(['Note:', certificazione.note])
        
        table = Table(data, colWidths=[5*cm, 11*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), bg_color),
            ('TEXTCOLOR', (0, 0), (-1, -1), text_color),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 2, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        content.append(table)
        content.append(Spacer(1, 30))
        
        return content
    
    def build_firme_certificato_section(self, certificazione: CertificazioneCavo) -> List:
        """Costruisce la sezione Firme per certificato singolo"""
        content = []
        
        content.append(Paragraph("6. FIRME E RESPONSABILITÀ", self.styles['SectionHeader']))
        
        data_firme = [
            ['TECNICO ESECUTORE PROVE', 'RESPONSABILE VERIFICHE'],
            ['', ''],
            [f"Nome: {certificazione.id_operatore or '___________________'}", "Nome: ___________________"],
            ['', ''],
            ['Firma: ___________________', 'Firma: ___________________'],
            ['', ''],
            [f"Data: {certificazione.data_certificazione.strftime('%d/%m/%Y')}", f"Data: {certificazione.data_certificazione.strftime('%d/%m/%Y')}"],
        ]
        
        table_firme = Table(data_firme, colWidths=[8*cm, 8*cm])
        table_firme.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        content.append(table_firme)
        
        return content
