{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: \"តិចជាង {{count}} វិនាទី\",\n  xSeconds: \"{{count}} វិនាទី\",\n  halfAMinute: \"កន្លះនាទី\",\n  lessThanXMinutes: \"តិចជាង {{count}} នាទី\",\n  xMinutes: \"{{count}} នាទី\",\n  aboutXHours: \"ប្រហែល {{count}} ម៉ោង\",\n  xHours: \"{{count}} ម៉ោង\",\n  xDays: \"{{count}} ថ្ងៃ\",\n  aboutXWeeks: \"ប្រហែល {{count}} សប្តាហ៍\",\n  xWeeks: \"{{count}} សប្តាហ៍\",\n  aboutXMonths: \"ប្រហែល {{count}} ខែ\",\n  xMonths: \"{{count}} ខែ\",\n  aboutXYears: \"ប្រហែល {{count}} ឆ្នាំ\",\n  xYears: \"{{count}} ឆ្នាំ\",\n  overXYears: \"ជាង {{count}} ឆ្នាំ\",\n  almostXYears: \"ជិត {{count}} ឆ្នាំ\"\n};\nexport const formatDistance = (token, count, options) => {\n  const tokenValue = formatDistanceLocale[token];\n  let result = tokenValue;\n  if (typeof count === \"number\") {\n    result = result.replace(\"{{count}}\", count.toString());\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"ក្នុងរយៈពេល \" + result;\n    } else {\n      return result + \"មុន\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "tokenValue", "result", "replace", "toString", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/km/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: \"តិចជាង {{count}} វិនាទី\",\n  xSeconds: \"{{count}} វិនាទី\",\n  halfAMinute: \"កន្លះនាទី\",\n  lessThanXMinutes: \"តិចជាង {{count}} នាទី\",\n  xMinutes: \"{{count}} នាទី\",\n  aboutXHours: \"ប្រហែល {{count}} ម៉ោង\",\n  xHours: \"{{count}} ម៉ោង\",\n  xDays: \"{{count}} ថ្ងៃ\",\n  aboutXWeeks: \"ប្រហែល {{count}} សប្តាហ៍\",\n  xWeeks: \"{{count}} សប្តាហ៍\",\n  aboutXMonths: \"ប្រហែល {{count}} ខែ\",\n  xMonths: \"{{count}} ខែ\",\n  aboutXYears: \"ប្រហែល {{count}} ឆ្នាំ\",\n  xYears: \"{{count}} ឆ្នាំ\",\n  overXYears: \"ជាង {{count}} ឆ្នាំ\",\n  almostXYears: \"ជិត {{count}} ឆ្នាំ\",\n};\n\nexport const formatDistance = (token, count, options) => {\n  const tokenValue = formatDistanceLocale[token];\n\n  let result = tokenValue;\n\n  if (typeof count === \"number\") {\n    result = result.replace(\"{{count}}\", count.toString());\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"ក្នុងរយៈពេល \" + result;\n    } else {\n      return result + \"មុន\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE,yBAAyB;EAC3CC,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,WAAW;EACxBC,gBAAgB,EAAE,uBAAuB;EACzCC,QAAQ,EAAE,gBAAgB;EAC1BC,WAAW,EAAE,uBAAuB;EACpCC,MAAM,EAAE,gBAAgB;EACxBC,KAAK,EAAE,gBAAgB;EACvBC,WAAW,EAAE,0BAA0B;EACvCC,MAAM,EAAE,mBAAmB;EAC3BC,YAAY,EAAE,qBAAqB;EACnCC,OAAO,EAAE,cAAc;EACvBC,WAAW,EAAE,wBAAwB;EACrCC,MAAM,EAAE,iBAAiB;EACzBC,UAAU,EAAE,qBAAqB;EACjCC,YAAY,EAAE;AAChB,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,MAAMC,UAAU,GAAGrB,oBAAoB,CAACkB,KAAK,CAAC;EAE9C,IAAII,MAAM,GAAGD,UAAU;EAEvB,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;IAC7BG,MAAM,GAAGA,MAAM,CAACC,OAAO,CAAC,WAAW,EAAEJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;EACxD;EAEA,IAAIJ,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,cAAc,GAAGJ,MAAM;IAChC,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,KAAK;IACvB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}