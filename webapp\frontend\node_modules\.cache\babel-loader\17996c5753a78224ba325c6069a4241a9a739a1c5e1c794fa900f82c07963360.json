{"ast": null, "code": "import * as React from 'react';\nexport const PickerFieldPrivateContext = /*#__PURE__*/React.createContext(null);\nexport function useNullableFieldPrivateContext() {\n  return React.useContext(PickerFieldPrivateContext);\n}", "map": {"version": 3, "names": ["React", "PickerFieldPrivateContext", "createContext", "useNullableFieldPrivateContext", "useContext"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useNullableFieldPrivateContext.js"], "sourcesContent": ["import * as React from 'react';\nexport const PickerFieldPrivateContext = /*#__PURE__*/React.createContext(null);\nexport function useNullableFieldPrivateContext() {\n  return React.useContext(PickerFieldPrivateContext);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,yBAAyB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC/E,OAAO,SAASC,8BAA8BA,CAAA,EAAG;EAC/C,OAAOH,KAAK,CAACI,UAAU,CAACH,yBAAyB,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}