{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ب\", \"ك\"],\n  abbreviated: [\"ب\", \"ك\"],\n  wide: [\"مىيلادىدىن بۇرۇن\", \"مىيلادىدىن كىيىن\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1\", \"2\", \"3\", \"4\"],\n  wide: [\"بىرىنجى چارەك\", \"ئىككىنجى چارەك\", \"ئۈچىنجى چارەك\", \"تۆتىنجى چارەك\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"ي\", \"ف\", \"م\", \"ا\", \"م\", \"ى\", \"ى\", \"ا\", \"س\", \"ۆ\", \"ن\", \"د\"],\n  abbreviated: [\"يانۋار\", \"فېۋىرال\", \"مارت\", \"ئاپرىل\", \"ماي\", \"ئىيۇن\", \"ئىيول\", \"ئاۋغۇست\", \"سىنتەبىر\", \"ئۆكتەبىر\", \"نويابىر\", \"دىكابىر\"],\n  wide: [\"يانۋار\", \"فېۋىرال\", \"مارت\", \"ئاپرىل\", \"ماي\", \"ئىيۇن\", \"ئىيول\", \"ئاۋغۇست\", \"سىنتەبىر\", \"ئۆكتەبىر\", \"نويابىر\", \"دىكابىر\"]\n};\nconst dayValues = {\n  narrow: [\"ي\", \"د\", \"س\", \"چ\", \"پ\", \"ج\", \"ش\"],\n  short: [\"ي\", \"د\", \"س\", \"چ\", \"پ\", \"ج\", \"ش\"],\n  abbreviated: [\"يەكشەنبە\", \"دۈشەنبە\", \"سەيشەنبە\", \"چارشەنبە\", \"پەيشەنبە\", \"جۈمە\", \"شەنبە\"],\n  wide: [\"يەكشەنبە\", \"دۈشەنبە\", \"سەيشەنبە\", \"چارشەنبە\", \"پەيشەنبە\", \"جۈمە\", \"شەنبە\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەن\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشىم\",\n    night: \"كىچە\"\n  },\n  abbreviated: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەن\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشىم\",\n    night: \"كىچە\"\n  },\n  wide: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەن\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشىم\",\n    night: \"كىچە\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەندە\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشامدا\",\n    night: \"كىچىدە\"\n  },\n  abbreviated: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەندە\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشامدا\",\n    night: \"كىچىدە\"\n  },\n  wide: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەندە\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشامدا\",\n    night: \"كىچىدە\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ug/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"ب\", \"ك\"],\n  abbreviated: [\"ب\", \"ك\"],\n  wide: [\"مىيلادىدىن بۇرۇن\", \"مىيلادىدىن كىيىن\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1\", \"2\", \"3\", \"4\"],\n  wide: [\"بىرىنجى چارەك\", \"ئىككىنجى چارەك\", \"ئۈچىنجى چارەك\", \"تۆتىنجى چارەك\"],\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"ي\", \"ف\", \"م\", \"ا\", \"م\", \"ى\", \"ى\", \"ا\", \"س\", \"ۆ\", \"ن\", \"د\"],\n  abbreviated: [\n    \"يانۋار\",\n    \"فېۋىرال\",\n    \"مارت\",\n    \"ئاپرىل\",\n    \"ماي\",\n    \"ئىيۇن\",\n    \"ئىيول\",\n    \"ئاۋغۇست\",\n    \"سىنتەبىر\",\n    \"ئۆكتەبىر\",\n    \"نويابىر\",\n    \"دىكابىر\",\n  ],\n\n  wide: [\n    \"يانۋار\",\n    \"فېۋىرال\",\n    \"مارت\",\n    \"ئاپرىل\",\n    \"ماي\",\n    \"ئىيۇن\",\n    \"ئىيول\",\n    \"ئاۋغۇست\",\n    \"سىنتەبىر\",\n    \"ئۆكتەبىر\",\n    \"نويابىر\",\n    \"دىكابىر\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"ي\", \"د\", \"س\", \"چ\", \"پ\", \"ج\", \"ش\"],\n  short: [\"ي\", \"د\", \"س\", \"چ\", \"پ\", \"ج\", \"ش\"],\n  abbreviated: [\n    \"يەكشەنبە\",\n    \"دۈشەنبە\",\n    \"سەيشەنبە\",\n    \"چارشەنبە\",\n    \"پەيشەنبە\",\n    \"جۈمە\",\n    \"شەنبە\",\n  ],\n\n  wide: [\n    \"يەكشەنبە\",\n    \"دۈشەنبە\",\n    \"سەيشەنبە\",\n    \"چارشەنبە\",\n    \"پەيشەنبە\",\n    \"جۈمە\",\n    \"شەنبە\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەن\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشىم\",\n    night: \"كىچە\",\n  },\n  abbreviated: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەن\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشىم\",\n    night: \"كىچە\",\n  },\n  wide: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەن\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشىم\",\n    night: \"كىچە\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەندە\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشامدا\",\n    night: \"كىچىدە\",\n  },\n  abbreviated: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەندە\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشامدا\",\n    night: \"كىچىدە\",\n  },\n  wide: {\n    am: \"ئە\",\n    pm: \"چ\",\n    midnight: \"ك\",\n    noon: \"چ\",\n    morning: \"ئەتىگەندە\",\n    afternoon: \"چۈشتىن كىيىن\",\n    evening: \"ئاخشامدا\",\n    night: \"كىچىدە\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACvBC,IAAI,EAAE,CAAC,kBAAkB,EAAE,kBAAkB;AAC/C,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACjCC,IAAI,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe;AAC5E,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,QAAQ,EACR,SAAS,EACT,MAAM,EACN,QAAQ,EACR,KAAK,EACL,OAAO,EACP,OAAO,EACP,SAAS,EACT,UAAU,EACV,UAAU,EACV,SAAS,EACT,SAAS,CACV;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,SAAS,EACT,MAAM,EACN,QAAQ,EACR,KAAK,EACL,OAAO,EACP,OAAO,EACP,SAAS,EACT,UAAU,EACV,UAAU,EACV,SAAS,EACT,SAAS;AAEb,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1CL,WAAW,EAAE,CACX,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU,EACV,MAAM,EACN,OAAO,CACR;EAEDC,IAAI,EAAE,CACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU,EACV,MAAM,EACN,OAAO;AAEX,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,cAAc;IACzBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,cAAc;IACzBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,cAAc;IACzBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,cAAc;IACzBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,cAAc;IACzBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,cAAc;IACzBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBJ,aAAa;EAEbK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}