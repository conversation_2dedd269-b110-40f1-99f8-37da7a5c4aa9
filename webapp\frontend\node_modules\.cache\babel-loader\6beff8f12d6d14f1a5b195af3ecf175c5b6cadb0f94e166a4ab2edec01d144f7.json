{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useTimeout from '@mui/utils/useTimeout';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useSplitFieldProps } from \"../../../hooks/index.js\";\nimport { getActiveElement } from \"../../utils/utils.js\";\nimport { getSectionVisibleValue, isAndroid } from \"./useField.utils.js\";\nimport { useFieldCharacterEditing } from \"./useFieldCharacterEditing.js\";\nimport { useFieldRootHandleKeyDown } from \"./useFieldRootHandleKeyDown.js\";\nimport { useFieldState } from \"./useFieldState.js\";\nimport { useFieldInternalPropsWithDefaults } from \"./useFieldInternalPropsWithDefaults.js\";\nconst cleanString = dirtyString => dirtyString.replace(/[\\u2066\\u2067\\u2068\\u2069]/g, '');\nexport const addPositionPropertiesToSections = (sections, localizedDigits, isRtl) => {\n  let position = 0;\n  let positionInInput = isRtl ? 1 : 0;\n  const newSections = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const renderedValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    const sectionStr = `${section.startSeparator}${renderedValue}${section.endSeparator}`;\n    const sectionLength = cleanString(sectionStr).length;\n    const sectionLengthInInput = sectionStr.length;\n\n    // The ...InInput values consider the unicode characters but do include them in their indexes\n    const cleanedValue = cleanString(renderedValue);\n    const startInInput = positionInInput + (cleanedValue === '' ? 0 : renderedValue.indexOf(cleanedValue[0])) + section.startSeparator.length;\n    const endInInput = startInInput + cleanedValue.length;\n    newSections.push(_extends({}, section, {\n      start: position,\n      end: position + sectionLength,\n      startInInput,\n      endInInput\n    }));\n    position += sectionLength;\n    // Move position to the end of string associated to the current section\n    positionInInput += sectionLengthInInput;\n  }\n  return newSections;\n};\nexport const useFieldV6TextField = parameters => {\n  const isRtl = useRtl();\n  const focusTimeout = useTimeout();\n  const selectionSyncTimeout = useTimeout();\n  const {\n    props,\n    manager,\n    skipContextFieldRefAssignment,\n    manager: {\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager,\n      internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel\n    }\n  } = parameters;\n  const {\n    internalProps,\n    forwardedProps\n  } = useSplitFieldProps(props, valueType);\n  const internalPropsWithDefaults = useFieldInternalPropsWithDefaults({\n    manager,\n    internalProps,\n    skipContextFieldRefAssignment\n  });\n  const {\n    onFocus,\n    onClick,\n    onPaste,\n    onBlur,\n    onKeyDown,\n    onClear,\n    clearable,\n    inputRef: inputRefProp,\n    placeholder: inPlaceholder\n  } = forwardedProps;\n  const {\n    readOnly = false,\n    disabled = false,\n    autoFocus = false,\n    focused,\n    unstableFieldRef\n  } = internalPropsWithDefaults;\n  const inputRef = React.useRef(null);\n  const handleRef = useForkRef(inputRefProp, inputRef);\n  const stateResponse = useFieldState({\n    manager,\n    internalPropsWithDefaults,\n    forwardedProps\n  });\n  const {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    state,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  } = stateResponse;\n  const applyCharacterEditing = useFieldCharacterEditing({\n    stateResponse\n  });\n  const openPickerAriaLabel = useOpenPickerButtonAriaLabel(value);\n  const sections = React.useMemo(() => addPositionPropertiesToSections(state.sections, localizedDigits, isRtl), [state.sections, localizedDigits, isRtl]);\n  function syncSelectionFromDOM() {\n    const browserStartIndex = inputRef.current.selectionStart ?? 0;\n    let nextSectionIndex;\n    if (browserStartIndex <= sections[0].startInInput) {\n      // Special case if browser index is in invisible characters at the beginning\n      nextSectionIndex = 1;\n    } else if (browserStartIndex >= sections[sections.length - 1].endInInput) {\n      // If the click is after the last character of the input, then we want to select the 1st section.\n      nextSectionIndex = 1;\n    } else {\n      nextSectionIndex = sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n    }\n    const sectionIndex = nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    setSelectedSections(sectionIndex);\n  }\n  function focusField(newSelectedSection = 0) {\n    if (getActiveElement(document) === inputRef.current) {\n      return;\n    }\n    inputRef.current?.focus();\n    setSelectedSections(newSelectedSection);\n  }\n  const handleInputFocus = useEventCallback(event => {\n    onFocus?.(event);\n    // The ref is guaranteed to be resolved at this point.\n    const input = inputRef.current;\n    focusTimeout.start(0, () => {\n      // The ref changed, the component got remounted, the focus event is no longer relevant.\n      if (!input || input !== inputRef.current) {\n        return;\n      }\n      if (activeSectionIndex != null) {\n        return;\n      }\n      if (\n      // avoid selecting all sections when focusing empty field without value\n      input.value.length && Number(input.selectionEnd) - Number(input.selectionStart) === input.value.length) {\n        setSelectedSections('all');\n      } else {\n        syncSelectionFromDOM();\n      }\n    });\n  });\n  const handleInputClick = useEventCallback((event, ...args) => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    onClick?.(event, ...args);\n    syncSelectionFromDOM();\n  });\n  const handleInputPaste = useEventCallback(event => {\n    onPaste?.(event);\n\n    // prevent default to avoid the input `onChange` handler being called\n    event.preventDefault();\n    if (readOnly || disabled) {\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    if (typeof parsedSelectedSections === 'number') {\n      const activeSection = state.sections[parsedSelectedSections];\n      const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n      const digitsOnly = /^[0-9]+$/.test(pastedValue);\n      const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n      const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n      if (isValidPastedValue) {\n        setCharacterQuery(null);\n        updateSectionValue({\n          section: activeSection,\n          newSectionValue: pastedValue,\n          shouldGoToNextSection: true\n        });\n        return;\n      }\n      if (lettersOnly || digitsOnly) {\n        // The pasted value corresponds to a single section, but not the expected type,\n        // skip the modification\n        return;\n      }\n    }\n    setCharacterQuery(null);\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleContainerBlur = useEventCallback(event => {\n    onBlur?.(event);\n    setSelectedSections(null);\n  });\n  const handleInputChange = useEventCallback(event => {\n    if (readOnly) {\n      return;\n    }\n    const targetValue = event.target.value;\n    if (targetValue === '') {\n      clearValue();\n      return;\n    }\n    const eventData = event.nativeEvent.data;\n    // Calling `.fill(04/11/2022)` in playwright will trigger a change event with the requested content to insert in `event.nativeEvent.data`\n    // usual changes have only the currently typed character in the `event.nativeEvent.data`\n    const shouldUseEventData = eventData && eventData.length > 1;\n    const valueStr = shouldUseEventData ? eventData : targetValue;\n    const cleanValueStr = cleanString(valueStr);\n    if (parsedSelectedSections === 'all') {\n      setSelectedSections(activeSectionIndex);\n    }\n\n    // If no section is selected or eventData should be used, we just try to parse the new value\n    // This line is mostly triggered by imperative code / application tests.\n    if (activeSectionIndex == null || shouldUseEventData) {\n      updateValueFromValueStr(shouldUseEventData ? eventData : cleanValueStr);\n      return;\n    }\n    let keyPressed;\n    if (parsedSelectedSections === 'all' && cleanValueStr.length === 1) {\n      keyPressed = cleanValueStr;\n    } else {\n      const prevValueStr = cleanString(fieldValueManager.getV6InputValueFromSections(sections, localizedDigits, isRtl));\n      let startOfDiffIndex = -1;\n      let endOfDiffIndex = -1;\n      for (let i = 0; i < prevValueStr.length; i += 1) {\n        if (startOfDiffIndex === -1 && prevValueStr[i] !== cleanValueStr[i]) {\n          startOfDiffIndex = i;\n        }\n        if (endOfDiffIndex === -1 && prevValueStr[prevValueStr.length - i - 1] !== cleanValueStr[cleanValueStr.length - i - 1]) {\n          endOfDiffIndex = i;\n        }\n      }\n      const activeSection = sections[activeSectionIndex];\n      const hasDiffOutsideOfActiveSection = startOfDiffIndex < activeSection.start || prevValueStr.length - endOfDiffIndex - 1 > activeSection.end;\n      if (hasDiffOutsideOfActiveSection) {\n        // TODO: Support if the new date is valid\n        return;\n      }\n\n      // The active section being selected, the browser has replaced its value with the key pressed by the user.\n      const activeSectionEndRelativeToNewValue = cleanValueStr.length - prevValueStr.length + activeSection.end - cleanString(activeSection.endSeparator || '').length;\n      keyPressed = cleanValueStr.slice(activeSection.start + cleanString(activeSection.startSeparator || '').length, activeSectionEndRelativeToNewValue);\n    }\n    if (keyPressed.length === 0) {\n      if (isAndroid()) {\n        setTempAndroidValueStr(valueStr);\n      }\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex: activeSectionIndex\n    });\n  });\n  const handleClear = useEventCallback((event, ...args) => {\n    event.preventDefault();\n    onClear?.(event, ...args);\n    clearValue();\n    if (!isFieldFocused(inputRef)) {\n      // setSelectedSections is called internally\n      focusField(0);\n    } else {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleContainerKeyDown = useFieldRootHandleKeyDown({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse\n  });\n  const wrappedHandleContainerKeyDown = useEventCallback(event => {\n    onKeyDown?.(event);\n    handleContainerKeyDown(event);\n  });\n  const placeholder = React.useMemo(() => {\n    if (inPlaceholder !== undefined) {\n      return inPlaceholder;\n    }\n    return fieldValueManager.getV6InputValueFromSections(getSectionsFromValue(valueManager.emptyValue), localizedDigits, isRtl);\n  }, [inPlaceholder, fieldValueManager, getSectionsFromValue, valueManager.emptyValue, localizedDigits, isRtl]);\n  const valueStr = React.useMemo(() => state.tempValueStrAndroid ?? fieldValueManager.getV6InputValueFromSections(state.sections, localizedDigits, isRtl), [state.sections, fieldValueManager, state.tempValueStrAndroid, localizedDigits, isRtl]);\n  React.useEffect(() => {\n    // Select all the sections when focused on mount (`autoFocus = true` on the input)\n    if (inputRef.current && inputRef.current === getActiveElement(document)) {\n      setSelectedSections('all');\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEnhancedEffect(() => {\n    function syncSelectionToDOM() {\n      if (!inputRef.current) {\n        return;\n      }\n      if (parsedSelectedSections == null) {\n        if (inputRef.current.scrollLeft) {\n          // Ensure that input content is not marked as selected.\n          // setting selection range to 0 causes issues in Safari.\n          // https://bugs.webkit.org/show_bug.cgi?id=224425\n          inputRef.current.scrollLeft = 0;\n        }\n        return;\n      }\n\n      // On multi input range pickers we want to update selection range only for the active input\n      // This helps to avoid the focus jumping on Safari https://github.com/mui/mui-x/issues/9003\n      // because WebKit implements the `setSelectionRange` based on the spec: https://bugs.webkit.org/show_bug.cgi?id=224425\n      if (inputRef.current !== getActiveElement(document)) {\n        return;\n      }\n\n      // Fix scroll jumping on iOS browser: https://github.com/mui/mui-x/issues/8321\n      const currentScrollTop = inputRef.current.scrollTop;\n      if (parsedSelectedSections === 'all') {\n        inputRef.current.select();\n      } else {\n        const selectedSection = sections[parsedSelectedSections];\n        const selectionStart = selectedSection.type === 'empty' ? selectedSection.startInInput - selectedSection.startSeparator.length : selectedSection.startInInput;\n        const selectionEnd = selectedSection.type === 'empty' ? selectedSection.endInInput + selectedSection.endSeparator.length : selectedSection.endInInput;\n        if (selectionStart !== inputRef.current.selectionStart || selectionEnd !== inputRef.current.selectionEnd) {\n          if (inputRef.current === getActiveElement(document)) {\n            inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n          }\n        }\n        selectionSyncTimeout.start(0, () => {\n          // handle case when the selection is not updated correctly\n          // could happen on Android\n          if (inputRef.current && inputRef.current === getActiveElement(document) &&\n          // The section might loose all selection, where `selectionStart === selectionEnd`\n          // https://github.com/mui/mui-x/pull/13652\n          inputRef.current.selectionStart === inputRef.current.selectionEnd && (inputRef.current.selectionStart !== selectionStart || inputRef.current.selectionEnd !== selectionEnd)) {\n            syncSelectionToDOM();\n          }\n        });\n      }\n\n      // Even reading this variable seems to do the trick, but also setting it just to make use of it\n      inputRef.current.scrollTop = currentScrollTop;\n    }\n    syncSelectionToDOM();\n  });\n  const inputMode = React.useMemo(() => {\n    if (activeSectionIndex == null) {\n      return 'text';\n    }\n    if (state.sections[activeSectionIndex].contentType === 'letter') {\n      return 'text';\n    }\n    return 'numeric';\n  }, [activeSectionIndex, state.sections]);\n  const inputHasFocus = inputRef.current && inputRef.current === getActiveElement(document);\n  const shouldShowPlaceholder = !inputHasFocus && areAllSectionsEmpty;\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: () => {\n      const browserStartIndex = inputRef.current.selectionStart ?? 0;\n      const browserEndIndex = inputRef.current.selectionEnd ?? 0;\n      if (browserStartIndex === 0 && browserEndIndex === 0) {\n        return null;\n      }\n      const nextSectionIndex = browserStartIndex <= sections[0].startInInput ? 1 // Special case if browser index is in invisible characters at the beginning.\n      : sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n      return nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    },\n    setSelectedSections: newSelectedSections => setSelectedSections(newSelectedSections),\n    focusField,\n    isFieldFocused: () => isFieldFocused(inputRef)\n  }));\n  return _extends({}, forwardedProps, {\n    error,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled),\n    onBlur: handleContainerBlur,\n    onClick: handleInputClick,\n    onFocus: handleInputFocus,\n    onPaste: handleInputPaste,\n    onKeyDown: wrappedHandleContainerKeyDown,\n    onClear: handleClear,\n    inputRef: handleRef,\n    // Additional\n    enableAccessibleFieldDOMStructure: false,\n    placeholder,\n    inputMode,\n    autoComplete: 'off',\n    value: shouldShowPlaceholder ? '' : valueStr,\n    onChange: handleInputChange,\n    focused,\n    disabled,\n    readOnly,\n    autoFocus,\n    openPickerAriaLabel\n  });\n};\nfunction isFieldFocused(inputRef) {\n  return inputRef.current === getActiveElement(document);\n}", "map": {"version": 3, "names": ["_extends", "React", "useRtl", "useEnhancedEffect", "useEventCallback", "useTimeout", "useForkRef", "useSplitFieldProps", "getActiveElement", "getSectionVisibleValue", "isAndroid", "useFieldCharacterEditing", "useFieldRootHandleKeyDown", "useFieldState", "useFieldInternalPropsWithDefaults", "cleanString", "dirtyString", "replace", "addPositionPropertiesToSections", "sections", "localizedDigits", "isRtl", "position", "positionInInput", "newSections", "i", "length", "section", "renderedValue", "sectionStr", "startSeparator", "endSeparator", "sectionLength", "sectionLengthInInput", "cleanedValue", "startInInput", "indexOf", "endInInput", "push", "start", "end", "useFieldV6TextField", "parameters", "focusTimeout", "selectionSyncTimeout", "props", "manager", "skipContextFieldRefAssignment", "valueType", "internal_valueManager", "valueManager", "internal_fieldValueManager", "field<PERSON><PERSON>ueManager", "internal_useOpenPickerButtonAriaLabel", "useOpenPickerButtonAriaLabel", "internalProps", "forwardedProps", "internalPropsWithDefaults", "onFocus", "onClick", "onPaste", "onBlur", "onKeyDown", "onClear", "clearable", "inputRef", "inputRefProp", "placeholder", "inPlaceholder", "readOnly", "disabled", "autoFocus", "focused", "unstableFieldRef", "useRef", "handleRef", "stateResponse", "activeSectionIndex", "areAllSectionsEmpty", "error", "parsedSelectedSections", "sectionOrder", "state", "value", "clearValue", "clearActiveSection", "setCharacterQuery", "setSelectedSections", "setTempAndroidValueStr", "updateSectionValue", "updateValueFromValueStr", "getSectionsFromValue", "applyCharacterEditing", "openPickerAriaLabel", "useMemo", "syncSelectionFromDOM", "browserStartIndex", "current", "selectionStart", "nextSectionIndex", "findIndex", "sectionIndex", "focusField", "newSelectedSection", "document", "focus", "handleInputFocus", "event", "input", "Number", "selectionEnd", "handleInputClick", "args", "isDefaultPrevented", "handleInputPaste", "preventDefault", "pastedValue", "clipboardData", "getData", "activeSection", "lettersOnly", "test", "digitsOnly", "digitsAndLetterOnly", "isValidPastedValue", "contentType", "newSectionValue", "shouldGoToNextSection", "handleContainerBlur", "handleInputChange", "targetValue", "target", "eventData", "nativeEvent", "data", "shouldUseEventData", "valueStr", "cleanValueStr", "keyPressed", "prevValueStr", "getV6InputValueFromSections", "startOfDiffIndex", "endOfDiffIndex", "hasDiffOutsideOfActiveSection", "activeSectionEndRelativeToNewValue", "slice", "handleClear", "isFieldFocused", "startIndex", "handleContainerKeyDown", "wrappedHandleContainerKeyDown", "undefined", "emptyValue", "tempValueStrAndroid", "useEffect", "syncSelectionToDOM", "scrollLeft", "currentScrollTop", "scrollTop", "select", "selectedSection", "type", "setSelectionRange", "inputMode", "inputHasFocus", "shouldShowPlaceholder", "useImperativeHandle", "getSections", "getActiveSectionIndex", "browserEndIndex", "newSelectedSections", "Boolean", "enableAccessibleFieldDOMStructure", "autoComplete", "onChange"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useFieldV6TextField.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useTimeout from '@mui/utils/useTimeout';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useSplitFieldProps } from \"../../../hooks/index.js\";\nimport { getActiveElement } from \"../../utils/utils.js\";\nimport { getSectionVisibleValue, isAndroid } from \"./useField.utils.js\";\nimport { useFieldCharacterEditing } from \"./useFieldCharacterEditing.js\";\nimport { useFieldRootHandleKeyDown } from \"./useFieldRootHandleKeyDown.js\";\nimport { useFieldState } from \"./useFieldState.js\";\nimport { useFieldInternalPropsWithDefaults } from \"./useFieldInternalPropsWithDefaults.js\";\nconst cleanString = dirtyString => dirtyString.replace(/[\\u2066\\u2067\\u2068\\u2069]/g, '');\nexport const addPositionPropertiesToSections = (sections, localizedDigits, isRtl) => {\n  let position = 0;\n  let positionInInput = isRtl ? 1 : 0;\n  const newSections = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const renderedValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    const sectionStr = `${section.startSeparator}${renderedValue}${section.endSeparator}`;\n    const sectionLength = cleanString(sectionStr).length;\n    const sectionLengthInInput = sectionStr.length;\n\n    // The ...InInput values consider the unicode characters but do include them in their indexes\n    const cleanedValue = cleanString(renderedValue);\n    const startInInput = positionInInput + (cleanedValue === '' ? 0 : renderedValue.indexOf(cleanedValue[0])) + section.startSeparator.length;\n    const endInInput = startInInput + cleanedValue.length;\n    newSections.push(_extends({}, section, {\n      start: position,\n      end: position + sectionLength,\n      startInInput,\n      endInInput\n    }));\n    position += sectionLength;\n    // Move position to the end of string associated to the current section\n    positionInInput += sectionLengthInInput;\n  }\n  return newSections;\n};\nexport const useFieldV6TextField = parameters => {\n  const isRtl = useRtl();\n  const focusTimeout = useTimeout();\n  const selectionSyncTimeout = useTimeout();\n  const {\n    props,\n    manager,\n    skipContextFieldRefAssignment,\n    manager: {\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager,\n      internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel\n    }\n  } = parameters;\n  const {\n    internalProps,\n    forwardedProps\n  } = useSplitFieldProps(props, valueType);\n  const internalPropsWithDefaults = useFieldInternalPropsWithDefaults({\n    manager,\n    internalProps,\n    skipContextFieldRefAssignment\n  });\n  const {\n    onFocus,\n    onClick,\n    onPaste,\n    onBlur,\n    onKeyDown,\n    onClear,\n    clearable,\n    inputRef: inputRefProp,\n    placeholder: inPlaceholder\n  } = forwardedProps;\n  const {\n    readOnly = false,\n    disabled = false,\n    autoFocus = false,\n    focused,\n    unstableFieldRef\n  } = internalPropsWithDefaults;\n  const inputRef = React.useRef(null);\n  const handleRef = useForkRef(inputRefProp, inputRef);\n  const stateResponse = useFieldState({\n    manager,\n    internalPropsWithDefaults,\n    forwardedProps\n  });\n  const {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    state,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  } = stateResponse;\n  const applyCharacterEditing = useFieldCharacterEditing({\n    stateResponse\n  });\n  const openPickerAriaLabel = useOpenPickerButtonAriaLabel(value);\n  const sections = React.useMemo(() => addPositionPropertiesToSections(state.sections, localizedDigits, isRtl), [state.sections, localizedDigits, isRtl]);\n  function syncSelectionFromDOM() {\n    const browserStartIndex = inputRef.current.selectionStart ?? 0;\n    let nextSectionIndex;\n    if (browserStartIndex <= sections[0].startInInput) {\n      // Special case if browser index is in invisible characters at the beginning\n      nextSectionIndex = 1;\n    } else if (browserStartIndex >= sections[sections.length - 1].endInInput) {\n      // If the click is after the last character of the input, then we want to select the 1st section.\n      nextSectionIndex = 1;\n    } else {\n      nextSectionIndex = sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n    }\n    const sectionIndex = nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    setSelectedSections(sectionIndex);\n  }\n  function focusField(newSelectedSection = 0) {\n    if (getActiveElement(document) === inputRef.current) {\n      return;\n    }\n    inputRef.current?.focus();\n    setSelectedSections(newSelectedSection);\n  }\n  const handleInputFocus = useEventCallback(event => {\n    onFocus?.(event);\n    // The ref is guaranteed to be resolved at this point.\n    const input = inputRef.current;\n    focusTimeout.start(0, () => {\n      // The ref changed, the component got remounted, the focus event is no longer relevant.\n      if (!input || input !== inputRef.current) {\n        return;\n      }\n      if (activeSectionIndex != null) {\n        return;\n      }\n      if (\n      // avoid selecting all sections when focusing empty field without value\n      input.value.length && Number(input.selectionEnd) - Number(input.selectionStart) === input.value.length) {\n        setSelectedSections('all');\n      } else {\n        syncSelectionFromDOM();\n      }\n    });\n  });\n  const handleInputClick = useEventCallback((event, ...args) => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    onClick?.(event, ...args);\n    syncSelectionFromDOM();\n  });\n  const handleInputPaste = useEventCallback(event => {\n    onPaste?.(event);\n\n    // prevent default to avoid the input `onChange` handler being called\n    event.preventDefault();\n    if (readOnly || disabled) {\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    if (typeof parsedSelectedSections === 'number') {\n      const activeSection = state.sections[parsedSelectedSections];\n      const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n      const digitsOnly = /^[0-9]+$/.test(pastedValue);\n      const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n      const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n      if (isValidPastedValue) {\n        setCharacterQuery(null);\n        updateSectionValue({\n          section: activeSection,\n          newSectionValue: pastedValue,\n          shouldGoToNextSection: true\n        });\n        return;\n      }\n      if (lettersOnly || digitsOnly) {\n        // The pasted value corresponds to a single section, but not the expected type,\n        // skip the modification\n        return;\n      }\n    }\n    setCharacterQuery(null);\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleContainerBlur = useEventCallback(event => {\n    onBlur?.(event);\n    setSelectedSections(null);\n  });\n  const handleInputChange = useEventCallback(event => {\n    if (readOnly) {\n      return;\n    }\n    const targetValue = event.target.value;\n    if (targetValue === '') {\n      clearValue();\n      return;\n    }\n    const eventData = event.nativeEvent.data;\n    // Calling `.fill(04/11/2022)` in playwright will trigger a change event with the requested content to insert in `event.nativeEvent.data`\n    // usual changes have only the currently typed character in the `event.nativeEvent.data`\n    const shouldUseEventData = eventData && eventData.length > 1;\n    const valueStr = shouldUseEventData ? eventData : targetValue;\n    const cleanValueStr = cleanString(valueStr);\n    if (parsedSelectedSections === 'all') {\n      setSelectedSections(activeSectionIndex);\n    }\n\n    // If no section is selected or eventData should be used, we just try to parse the new value\n    // This line is mostly triggered by imperative code / application tests.\n    if (activeSectionIndex == null || shouldUseEventData) {\n      updateValueFromValueStr(shouldUseEventData ? eventData : cleanValueStr);\n      return;\n    }\n    let keyPressed;\n    if (parsedSelectedSections === 'all' && cleanValueStr.length === 1) {\n      keyPressed = cleanValueStr;\n    } else {\n      const prevValueStr = cleanString(fieldValueManager.getV6InputValueFromSections(sections, localizedDigits, isRtl));\n      let startOfDiffIndex = -1;\n      let endOfDiffIndex = -1;\n      for (let i = 0; i < prevValueStr.length; i += 1) {\n        if (startOfDiffIndex === -1 && prevValueStr[i] !== cleanValueStr[i]) {\n          startOfDiffIndex = i;\n        }\n        if (endOfDiffIndex === -1 && prevValueStr[prevValueStr.length - i - 1] !== cleanValueStr[cleanValueStr.length - i - 1]) {\n          endOfDiffIndex = i;\n        }\n      }\n      const activeSection = sections[activeSectionIndex];\n      const hasDiffOutsideOfActiveSection = startOfDiffIndex < activeSection.start || prevValueStr.length - endOfDiffIndex - 1 > activeSection.end;\n      if (hasDiffOutsideOfActiveSection) {\n        // TODO: Support if the new date is valid\n        return;\n      }\n\n      // The active section being selected, the browser has replaced its value with the key pressed by the user.\n      const activeSectionEndRelativeToNewValue = cleanValueStr.length - prevValueStr.length + activeSection.end - cleanString(activeSection.endSeparator || '').length;\n      keyPressed = cleanValueStr.slice(activeSection.start + cleanString(activeSection.startSeparator || '').length, activeSectionEndRelativeToNewValue);\n    }\n    if (keyPressed.length === 0) {\n      if (isAndroid()) {\n        setTempAndroidValueStr(valueStr);\n      }\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex: activeSectionIndex\n    });\n  });\n  const handleClear = useEventCallback((event, ...args) => {\n    event.preventDefault();\n    onClear?.(event, ...args);\n    clearValue();\n    if (!isFieldFocused(inputRef)) {\n      // setSelectedSections is called internally\n      focusField(0);\n    } else {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleContainerKeyDown = useFieldRootHandleKeyDown({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse\n  });\n  const wrappedHandleContainerKeyDown = useEventCallback(event => {\n    onKeyDown?.(event);\n    handleContainerKeyDown(event);\n  });\n  const placeholder = React.useMemo(() => {\n    if (inPlaceholder !== undefined) {\n      return inPlaceholder;\n    }\n    return fieldValueManager.getV6InputValueFromSections(getSectionsFromValue(valueManager.emptyValue), localizedDigits, isRtl);\n  }, [inPlaceholder, fieldValueManager, getSectionsFromValue, valueManager.emptyValue, localizedDigits, isRtl]);\n  const valueStr = React.useMemo(() => state.tempValueStrAndroid ?? fieldValueManager.getV6InputValueFromSections(state.sections, localizedDigits, isRtl), [state.sections, fieldValueManager, state.tempValueStrAndroid, localizedDigits, isRtl]);\n  React.useEffect(() => {\n    // Select all the sections when focused on mount (`autoFocus = true` on the input)\n    if (inputRef.current && inputRef.current === getActiveElement(document)) {\n      setSelectedSections('all');\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEnhancedEffect(() => {\n    function syncSelectionToDOM() {\n      if (!inputRef.current) {\n        return;\n      }\n      if (parsedSelectedSections == null) {\n        if (inputRef.current.scrollLeft) {\n          // Ensure that input content is not marked as selected.\n          // setting selection range to 0 causes issues in Safari.\n          // https://bugs.webkit.org/show_bug.cgi?id=224425\n          inputRef.current.scrollLeft = 0;\n        }\n        return;\n      }\n\n      // On multi input range pickers we want to update selection range only for the active input\n      // This helps to avoid the focus jumping on Safari https://github.com/mui/mui-x/issues/9003\n      // because WebKit implements the `setSelectionRange` based on the spec: https://bugs.webkit.org/show_bug.cgi?id=224425\n      if (inputRef.current !== getActiveElement(document)) {\n        return;\n      }\n\n      // Fix scroll jumping on iOS browser: https://github.com/mui/mui-x/issues/8321\n      const currentScrollTop = inputRef.current.scrollTop;\n      if (parsedSelectedSections === 'all') {\n        inputRef.current.select();\n      } else {\n        const selectedSection = sections[parsedSelectedSections];\n        const selectionStart = selectedSection.type === 'empty' ? selectedSection.startInInput - selectedSection.startSeparator.length : selectedSection.startInInput;\n        const selectionEnd = selectedSection.type === 'empty' ? selectedSection.endInInput + selectedSection.endSeparator.length : selectedSection.endInInput;\n        if (selectionStart !== inputRef.current.selectionStart || selectionEnd !== inputRef.current.selectionEnd) {\n          if (inputRef.current === getActiveElement(document)) {\n            inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n          }\n        }\n        selectionSyncTimeout.start(0, () => {\n          // handle case when the selection is not updated correctly\n          // could happen on Android\n          if (inputRef.current && inputRef.current === getActiveElement(document) &&\n          // The section might loose all selection, where `selectionStart === selectionEnd`\n          // https://github.com/mui/mui-x/pull/13652\n          inputRef.current.selectionStart === inputRef.current.selectionEnd && (inputRef.current.selectionStart !== selectionStart || inputRef.current.selectionEnd !== selectionEnd)) {\n            syncSelectionToDOM();\n          }\n        });\n      }\n\n      // Even reading this variable seems to do the trick, but also setting it just to make use of it\n      inputRef.current.scrollTop = currentScrollTop;\n    }\n    syncSelectionToDOM();\n  });\n  const inputMode = React.useMemo(() => {\n    if (activeSectionIndex == null) {\n      return 'text';\n    }\n    if (state.sections[activeSectionIndex].contentType === 'letter') {\n      return 'text';\n    }\n    return 'numeric';\n  }, [activeSectionIndex, state.sections]);\n  const inputHasFocus = inputRef.current && inputRef.current === getActiveElement(document);\n  const shouldShowPlaceholder = !inputHasFocus && areAllSectionsEmpty;\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: () => {\n      const browserStartIndex = inputRef.current.selectionStart ?? 0;\n      const browserEndIndex = inputRef.current.selectionEnd ?? 0;\n      if (browserStartIndex === 0 && browserEndIndex === 0) {\n        return null;\n      }\n      const nextSectionIndex = browserStartIndex <= sections[0].startInInput ? 1 // Special case if browser index is in invisible characters at the beginning.\n      : sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n      return nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    },\n    setSelectedSections: newSelectedSections => setSelectedSections(newSelectedSections),\n    focusField,\n    isFieldFocused: () => isFieldFocused(inputRef)\n  }));\n  return _extends({}, forwardedProps, {\n    error,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled),\n    onBlur: handleContainerBlur,\n    onClick: handleInputClick,\n    onFocus: handleInputFocus,\n    onPaste: handleInputPaste,\n    onKeyDown: wrappedHandleContainerKeyDown,\n    onClear: handleClear,\n    inputRef: handleRef,\n    // Additional\n    enableAccessibleFieldDOMStructure: false,\n    placeholder,\n    inputMode,\n    autoComplete: 'off',\n    value: shouldShowPlaceholder ? '' : valueStr,\n    onChange: handleInputChange,\n    focused,\n    disabled,\n    readOnly,\n    autoFocus,\n    openPickerAriaLabel\n  });\n};\nfunction isFieldFocused(inputRef) {\n  return inputRef.current === getActiveElement(document);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,sBAAsB,EAAEC,SAAS,QAAQ,qBAAqB;AACvE,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,iCAAiC,QAAQ,wCAAwC;AAC1F,MAAMC,WAAW,GAAGC,WAAW,IAAIA,WAAW,CAACC,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC;AACzF,OAAO,MAAMC,+BAA+B,GAAGA,CAACC,QAAQ,EAAEC,eAAe,EAAEC,KAAK,KAAK;EACnF,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,eAAe,GAAGF,KAAK,GAAG,CAAC,GAAG,CAAC;EACnC,MAAMG,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACO,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC3C,MAAME,OAAO,GAAGR,QAAQ,CAACM,CAAC,CAAC;IAC3B,MAAMG,aAAa,GAAGnB,sBAAsB,CAACkB,OAAO,EAAEN,KAAK,GAAG,WAAW,GAAG,WAAW,EAAED,eAAe,CAAC;IACzG,MAAMS,UAAU,GAAG,GAAGF,OAAO,CAACG,cAAc,GAAGF,aAAa,GAAGD,OAAO,CAACI,YAAY,EAAE;IACrF,MAAMC,aAAa,GAAGjB,WAAW,CAACc,UAAU,CAAC,CAACH,MAAM;IACpD,MAAMO,oBAAoB,GAAGJ,UAAU,CAACH,MAAM;;IAE9C;IACA,MAAMQ,YAAY,GAAGnB,WAAW,CAACa,aAAa,CAAC;IAC/C,MAAMO,YAAY,GAAGZ,eAAe,IAAIW,YAAY,KAAK,EAAE,GAAG,CAAC,GAAGN,aAAa,CAACQ,OAAO,CAACF,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGP,OAAO,CAACG,cAAc,CAACJ,MAAM;IACzI,MAAMW,UAAU,GAAGF,YAAY,GAAGD,YAAY,CAACR,MAAM;IACrDF,WAAW,CAACc,IAAI,CAACtC,QAAQ,CAAC,CAAC,CAAC,EAAE2B,OAAO,EAAE;MACrCY,KAAK,EAAEjB,QAAQ;MACfkB,GAAG,EAAElB,QAAQ,GAAGU,aAAa;MAC7BG,YAAY;MACZE;IACF,CAAC,CAAC,CAAC;IACHf,QAAQ,IAAIU,aAAa;IACzB;IACAT,eAAe,IAAIU,oBAAoB;EACzC;EACA,OAAOT,WAAW;AACpB,CAAC;AACD,OAAO,MAAMiB,mBAAmB,GAAGC,UAAU,IAAI;EAC/C,MAAMrB,KAAK,GAAGnB,MAAM,CAAC,CAAC;EACtB,MAAMyC,YAAY,GAAGtC,UAAU,CAAC,CAAC;EACjC,MAAMuC,oBAAoB,GAAGvC,UAAU,CAAC,CAAC;EACzC,MAAM;IACJwC,KAAK;IACLC,OAAO;IACPC,6BAA6B;IAC7BD,OAAO,EAAE;MACPE,SAAS;MACTC,qBAAqB,EAAEC,YAAY;MACnCC,0BAA0B,EAAEC,iBAAiB;MAC7CC,qCAAqC,EAAEC;IACzC;EACF,CAAC,GAAGZ,UAAU;EACd,MAAM;IACJa,aAAa;IACbC;EACF,CAAC,GAAGjD,kBAAkB,CAACsC,KAAK,EAAEG,SAAS,CAAC;EACxC,MAAMS,yBAAyB,GAAG3C,iCAAiC,CAAC;IAClEgC,OAAO;IACPS,aAAa;IACbR;EACF,CAAC,CAAC;EACF,MAAM;IACJW,OAAO;IACPC,OAAO;IACPC,OAAO;IACPC,MAAM;IACNC,SAAS;IACTC,OAAO;IACPC,SAAS;IACTC,QAAQ,EAAEC,YAAY;IACtBC,WAAW,EAAEC;EACf,CAAC,GAAGZ,cAAc;EAClB,MAAM;IACJa,QAAQ,GAAG,KAAK;IAChBC,QAAQ,GAAG,KAAK;IAChBC,SAAS,GAAG,KAAK;IACjBC,OAAO;IACPC;EACF,CAAC,GAAGhB,yBAAyB;EAC7B,MAAMQ,QAAQ,GAAGhE,KAAK,CAACyE,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,SAAS,GAAGrE,UAAU,CAAC4D,YAAY,EAAED,QAAQ,CAAC;EACpD,MAAMW,aAAa,GAAG/D,aAAa,CAAC;IAClCiC,OAAO;IACPW,yBAAyB;IACzBD;EACF,CAAC,CAAC;EACF,MAAM;IACJ;IACAqB,kBAAkB;IAClBC,mBAAmB;IACnBC,KAAK;IACL3D,eAAe;IACf4D,sBAAsB;IACtBC,YAAY;IACZC,KAAK;IACLC,KAAK;IACL;IACAC,UAAU;IACVC,kBAAkB;IAClBC,iBAAiB;IACjBC,mBAAmB;IACnBC,sBAAsB;IACtBC,kBAAkB;IAClBC,uBAAuB;IACvB;IACAC;EACF,CAAC,GAAGf,aAAa;EACjB,MAAMgB,qBAAqB,GAAGjF,wBAAwB,CAAC;IACrDiE;EACF,CAAC,CAAC;EACF,MAAMiB,mBAAmB,GAAGvC,4BAA4B,CAAC6B,KAAK,CAAC;EAC/D,MAAMhE,QAAQ,GAAGlB,KAAK,CAAC6F,OAAO,CAAC,MAAM5E,+BAA+B,CAACgE,KAAK,CAAC/D,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,EAAE,CAAC6D,KAAK,CAAC/D,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,CAAC;EACvJ,SAAS0E,oBAAoBA,CAAA,EAAG;IAC9B,MAAMC,iBAAiB,GAAG/B,QAAQ,CAACgC,OAAO,CAACC,cAAc,IAAI,CAAC;IAC9D,IAAIC,gBAAgB;IACpB,IAAIH,iBAAiB,IAAI7E,QAAQ,CAAC,CAAC,CAAC,CAACgB,YAAY,EAAE;MACjD;MACAgE,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM,IAAIH,iBAAiB,IAAI7E,QAAQ,CAACA,QAAQ,CAACO,MAAM,GAAG,CAAC,CAAC,CAACW,UAAU,EAAE;MACxE;MACA8D,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM;MACLA,gBAAgB,GAAGhF,QAAQ,CAACiF,SAAS,CAACzE,OAAO,IAAIA,OAAO,CAACQ,YAAY,GAAGR,OAAO,CAACG,cAAc,CAACJ,MAAM,GAAGsE,iBAAiB,CAAC;IAC5H;IACA,MAAMK,YAAY,GAAGF,gBAAgB,KAAK,CAAC,CAAC,GAAGhF,QAAQ,CAACO,MAAM,GAAG,CAAC,GAAGyE,gBAAgB,GAAG,CAAC;IACzFZ,mBAAmB,CAACc,YAAY,CAAC;EACnC;EACA,SAASC,UAAUA,CAACC,kBAAkB,GAAG,CAAC,EAAE;IAC1C,IAAI/F,gBAAgB,CAACgG,QAAQ,CAAC,KAAKvC,QAAQ,CAACgC,OAAO,EAAE;MACnD;IACF;IACAhC,QAAQ,CAACgC,OAAO,EAAEQ,KAAK,CAAC,CAAC;IACzBlB,mBAAmB,CAACgB,kBAAkB,CAAC;EACzC;EACA,MAAMG,gBAAgB,GAAGtG,gBAAgB,CAACuG,KAAK,IAAI;IACjDjD,OAAO,GAAGiD,KAAK,CAAC;IAChB;IACA,MAAMC,KAAK,GAAG3C,QAAQ,CAACgC,OAAO;IAC9BtD,YAAY,CAACJ,KAAK,CAAC,CAAC,EAAE,MAAM;MAC1B;MACA,IAAI,CAACqE,KAAK,IAAIA,KAAK,KAAK3C,QAAQ,CAACgC,OAAO,EAAE;QACxC;MACF;MACA,IAAIpB,kBAAkB,IAAI,IAAI,EAAE;QAC9B;MACF;MACA;MACA;MACA+B,KAAK,CAACzB,KAAK,CAACzD,MAAM,IAAImF,MAAM,CAACD,KAAK,CAACE,YAAY,CAAC,GAAGD,MAAM,CAACD,KAAK,CAACV,cAAc,CAAC,KAAKU,KAAK,CAACzB,KAAK,CAACzD,MAAM,EAAE;QACtG6D,mBAAmB,CAAC,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLQ,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMgB,gBAAgB,GAAG3G,gBAAgB,CAAC,CAACuG,KAAK,EAAE,GAAGK,IAAI,KAAK;IAC5D;IACA;IACA,IAAIL,KAAK,CAACM,kBAAkB,CAAC,CAAC,EAAE;MAC9B;IACF;IACAtD,OAAO,GAAGgD,KAAK,EAAE,GAAGK,IAAI,CAAC;IACzBjB,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,MAAMmB,gBAAgB,GAAG9G,gBAAgB,CAACuG,KAAK,IAAI;IACjD/C,OAAO,GAAG+C,KAAK,CAAC;;IAEhB;IACAA,KAAK,CAACQ,cAAc,CAAC,CAAC;IACtB,IAAI9C,QAAQ,IAAIC,QAAQ,EAAE;MACxB;IACF;IACA,MAAM8C,WAAW,GAAGT,KAAK,CAACU,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvD,IAAI,OAAOtC,sBAAsB,KAAK,QAAQ,EAAE;MAC9C,MAAMuC,aAAa,GAAGrC,KAAK,CAAC/D,QAAQ,CAAC6D,sBAAsB,CAAC;MAC5D,MAAMwC,WAAW,GAAG,aAAa,CAACC,IAAI,CAACL,WAAW,CAAC;MACnD,MAAMM,UAAU,GAAG,UAAU,CAACD,IAAI,CAACL,WAAW,CAAC;MAC/C,MAAMO,mBAAmB,GAAG,wCAAwC,CAACF,IAAI,CAACL,WAAW,CAAC;MACtF,MAAMQ,kBAAkB,GAAGL,aAAa,CAACM,WAAW,KAAK,QAAQ,IAAIL,WAAW,IAAID,aAAa,CAACM,WAAW,KAAK,OAAO,IAAIH,UAAU,IAAIH,aAAa,CAACM,WAAW,KAAK,mBAAmB,IAAIF,mBAAmB;MACnN,IAAIC,kBAAkB,EAAE;QACtBtC,iBAAiB,CAAC,IAAI,CAAC;QACvBG,kBAAkB,CAAC;UACjB9D,OAAO,EAAE4F,aAAa;UACtBO,eAAe,EAAEV,WAAW;UAC5BW,qBAAqB,EAAE;QACzB,CAAC,CAAC;QACF;MACF;MACA,IAAIP,WAAW,IAAIE,UAAU,EAAE;QAC7B;QACA;QACA;MACF;IACF;IACApC,iBAAiB,CAAC,IAAI,CAAC;IACvBI,uBAAuB,CAAC0B,WAAW,CAAC;EACtC,CAAC,CAAC;EACF,MAAMY,mBAAmB,GAAG5H,gBAAgB,CAACuG,KAAK,IAAI;IACpD9C,MAAM,GAAG8C,KAAK,CAAC;IACfpB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC,CAAC;EACF,MAAM0C,iBAAiB,GAAG7H,gBAAgB,CAACuG,KAAK,IAAI;IAClD,IAAItC,QAAQ,EAAE;MACZ;IACF;IACA,MAAM6D,WAAW,GAAGvB,KAAK,CAACwB,MAAM,CAAChD,KAAK;IACtC,IAAI+C,WAAW,KAAK,EAAE,EAAE;MACtB9C,UAAU,CAAC,CAAC;MACZ;IACF;IACA,MAAMgD,SAAS,GAAGzB,KAAK,CAAC0B,WAAW,CAACC,IAAI;IACxC;IACA;IACA,MAAMC,kBAAkB,GAAGH,SAAS,IAAIA,SAAS,CAAC1G,MAAM,GAAG,CAAC;IAC5D,MAAM8G,QAAQ,GAAGD,kBAAkB,GAAGH,SAAS,GAAGF,WAAW;IAC7D,MAAMO,aAAa,GAAG1H,WAAW,CAACyH,QAAQ,CAAC;IAC3C,IAAIxD,sBAAsB,KAAK,KAAK,EAAE;MACpCO,mBAAmB,CAACV,kBAAkB,CAAC;IACzC;;IAEA;IACA;IACA,IAAIA,kBAAkB,IAAI,IAAI,IAAI0D,kBAAkB,EAAE;MACpD7C,uBAAuB,CAAC6C,kBAAkB,GAAGH,SAAS,GAAGK,aAAa,CAAC;MACvE;IACF;IACA,IAAIC,UAAU;IACd,IAAI1D,sBAAsB,KAAK,KAAK,IAAIyD,aAAa,CAAC/G,MAAM,KAAK,CAAC,EAAE;MAClEgH,UAAU,GAAGD,aAAa;IAC5B,CAAC,MAAM;MACL,MAAME,YAAY,GAAG5H,WAAW,CAACqC,iBAAiB,CAACwF,2BAA2B,CAACzH,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,CAAC;MACjH,IAAIwH,gBAAgB,GAAG,CAAC,CAAC;MACzB,IAAIC,cAAc,GAAG,CAAC,CAAC;MACvB,KAAK,IAAIrH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkH,YAAY,CAACjH,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC/C,IAAIoH,gBAAgB,KAAK,CAAC,CAAC,IAAIF,YAAY,CAAClH,CAAC,CAAC,KAAKgH,aAAa,CAAChH,CAAC,CAAC,EAAE;UACnEoH,gBAAgB,GAAGpH,CAAC;QACtB;QACA,IAAIqH,cAAc,KAAK,CAAC,CAAC,IAAIH,YAAY,CAACA,YAAY,CAACjH,MAAM,GAAGD,CAAC,GAAG,CAAC,CAAC,KAAKgH,aAAa,CAACA,aAAa,CAAC/G,MAAM,GAAGD,CAAC,GAAG,CAAC,CAAC,EAAE;UACtHqH,cAAc,GAAGrH,CAAC;QACpB;MACF;MACA,MAAM8F,aAAa,GAAGpG,QAAQ,CAAC0D,kBAAkB,CAAC;MAClD,MAAMkE,6BAA6B,GAAGF,gBAAgB,GAAGtB,aAAa,CAAChF,KAAK,IAAIoG,YAAY,CAACjH,MAAM,GAAGoH,cAAc,GAAG,CAAC,GAAGvB,aAAa,CAAC/E,GAAG;MAC5I,IAAIuG,6BAA6B,EAAE;QACjC;QACA;MACF;;MAEA;MACA,MAAMC,kCAAkC,GAAGP,aAAa,CAAC/G,MAAM,GAAGiH,YAAY,CAACjH,MAAM,GAAG6F,aAAa,CAAC/E,GAAG,GAAGzB,WAAW,CAACwG,aAAa,CAACxF,YAAY,IAAI,EAAE,CAAC,CAACL,MAAM;MAChKgH,UAAU,GAAGD,aAAa,CAACQ,KAAK,CAAC1B,aAAa,CAAChF,KAAK,GAAGxB,WAAW,CAACwG,aAAa,CAACzF,cAAc,IAAI,EAAE,CAAC,CAACJ,MAAM,EAAEsH,kCAAkC,CAAC;IACpJ;IACA,IAAIN,UAAU,CAAChH,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAIhB,SAAS,CAAC,CAAC,EAAE;QACf8E,sBAAsB,CAACgD,QAAQ,CAAC;MAClC;MACAnD,kBAAkB,CAAC,CAAC;MACpB;IACF;IACAO,qBAAqB,CAAC;MACpB8C,UAAU;MACVrC,YAAY,EAAExB;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMqE,WAAW,GAAG9I,gBAAgB,CAAC,CAACuG,KAAK,EAAE,GAAGK,IAAI,KAAK;IACvDL,KAAK,CAACQ,cAAc,CAAC,CAAC;IACtBpD,OAAO,GAAG4C,KAAK,EAAE,GAAGK,IAAI,CAAC;IACzB5B,UAAU,CAAC,CAAC;IACZ,IAAI,CAAC+D,cAAc,CAAClF,QAAQ,CAAC,EAAE;MAC7B;MACAqC,UAAU,CAAC,CAAC,CAAC;IACf,CAAC,MAAM;MACLf,mBAAmB,CAACN,YAAY,CAACmE,UAAU,CAAC;IAC9C;EACF,CAAC,CAAC;EACF,MAAMC,sBAAsB,GAAGzI,yBAAyB,CAAC;IACvDkC,OAAO;IACPW,yBAAyB;IACzBmB;EACF,CAAC,CAAC;EACF,MAAM0E,6BAA6B,GAAGlJ,gBAAgB,CAACuG,KAAK,IAAI;IAC9D7C,SAAS,GAAG6C,KAAK,CAAC;IAClB0C,sBAAsB,CAAC1C,KAAK,CAAC;EAC/B,CAAC,CAAC;EACF,MAAMxC,WAAW,GAAGlE,KAAK,CAAC6F,OAAO,CAAC,MAAM;IACtC,IAAI1B,aAAa,KAAKmF,SAAS,EAAE;MAC/B,OAAOnF,aAAa;IACtB;IACA,OAAOhB,iBAAiB,CAACwF,2BAA2B,CAACjD,oBAAoB,CAACzC,YAAY,CAACsG,UAAU,CAAC,EAAEpI,eAAe,EAAEC,KAAK,CAAC;EAC7H,CAAC,EAAE,CAAC+C,aAAa,EAAEhB,iBAAiB,EAAEuC,oBAAoB,EAAEzC,YAAY,CAACsG,UAAU,EAAEpI,eAAe,EAAEC,KAAK,CAAC,CAAC;EAC7G,MAAMmH,QAAQ,GAAGvI,KAAK,CAAC6F,OAAO,CAAC,MAAMZ,KAAK,CAACuE,mBAAmB,IAAIrG,iBAAiB,CAACwF,2BAA2B,CAAC1D,KAAK,CAAC/D,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,EAAE,CAAC6D,KAAK,CAAC/D,QAAQ,EAAEiC,iBAAiB,EAAE8B,KAAK,CAACuE,mBAAmB,EAAErI,eAAe,EAAEC,KAAK,CAAC,CAAC;EAChPpB,KAAK,CAACyJ,SAAS,CAAC,MAAM;IACpB;IACA,IAAIzF,QAAQ,CAACgC,OAAO,IAAIhC,QAAQ,CAACgC,OAAO,KAAKzF,gBAAgB,CAACgG,QAAQ,CAAC,EAAE;MACvEjB,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERpF,iBAAiB,CAAC,MAAM;IACtB,SAASwJ,kBAAkBA,CAAA,EAAG;MAC5B,IAAI,CAAC1F,QAAQ,CAACgC,OAAO,EAAE;QACrB;MACF;MACA,IAAIjB,sBAAsB,IAAI,IAAI,EAAE;QAClC,IAAIf,QAAQ,CAACgC,OAAO,CAAC2D,UAAU,EAAE;UAC/B;UACA;UACA;UACA3F,QAAQ,CAACgC,OAAO,CAAC2D,UAAU,GAAG,CAAC;QACjC;QACA;MACF;;MAEA;MACA;MACA;MACA,IAAI3F,QAAQ,CAACgC,OAAO,KAAKzF,gBAAgB,CAACgG,QAAQ,CAAC,EAAE;QACnD;MACF;;MAEA;MACA,MAAMqD,gBAAgB,GAAG5F,QAAQ,CAACgC,OAAO,CAAC6D,SAAS;MACnD,IAAI9E,sBAAsB,KAAK,KAAK,EAAE;QACpCf,QAAQ,CAACgC,OAAO,CAAC8D,MAAM,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,MAAMC,eAAe,GAAG7I,QAAQ,CAAC6D,sBAAsB,CAAC;QACxD,MAAMkB,cAAc,GAAG8D,eAAe,CAACC,IAAI,KAAK,OAAO,GAAGD,eAAe,CAAC7H,YAAY,GAAG6H,eAAe,CAAClI,cAAc,CAACJ,MAAM,GAAGsI,eAAe,CAAC7H,YAAY;QAC7J,MAAM2E,YAAY,GAAGkD,eAAe,CAACC,IAAI,KAAK,OAAO,GAAGD,eAAe,CAAC3H,UAAU,GAAG2H,eAAe,CAACjI,YAAY,CAACL,MAAM,GAAGsI,eAAe,CAAC3H,UAAU;QACrJ,IAAI6D,cAAc,KAAKjC,QAAQ,CAACgC,OAAO,CAACC,cAAc,IAAIY,YAAY,KAAK7C,QAAQ,CAACgC,OAAO,CAACa,YAAY,EAAE;UACxG,IAAI7C,QAAQ,CAACgC,OAAO,KAAKzF,gBAAgB,CAACgG,QAAQ,CAAC,EAAE;YACnDvC,QAAQ,CAACgC,OAAO,CAACiE,iBAAiB,CAAChE,cAAc,EAAEY,YAAY,CAAC;UAClE;QACF;QACAlE,oBAAoB,CAACL,KAAK,CAAC,CAAC,EAAE,MAAM;UAClC;UACA;UACA,IAAI0B,QAAQ,CAACgC,OAAO,IAAIhC,QAAQ,CAACgC,OAAO,KAAKzF,gBAAgB,CAACgG,QAAQ,CAAC;UACvE;UACA;UACAvC,QAAQ,CAACgC,OAAO,CAACC,cAAc,KAAKjC,QAAQ,CAACgC,OAAO,CAACa,YAAY,KAAK7C,QAAQ,CAACgC,OAAO,CAACC,cAAc,KAAKA,cAAc,IAAIjC,QAAQ,CAACgC,OAAO,CAACa,YAAY,KAAKA,YAAY,CAAC,EAAE;YAC3K6C,kBAAkB,CAAC,CAAC;UACtB;QACF,CAAC,CAAC;MACJ;;MAEA;MACA1F,QAAQ,CAACgC,OAAO,CAAC6D,SAAS,GAAGD,gBAAgB;IAC/C;IACAF,kBAAkB,CAAC,CAAC;EACtB,CAAC,CAAC;EACF,MAAMQ,SAAS,GAAGlK,KAAK,CAAC6F,OAAO,CAAC,MAAM;IACpC,IAAIjB,kBAAkB,IAAI,IAAI,EAAE;MAC9B,OAAO,MAAM;IACf;IACA,IAAIK,KAAK,CAAC/D,QAAQ,CAAC0D,kBAAkB,CAAC,CAACgD,WAAW,KAAK,QAAQ,EAAE;MAC/D,OAAO,MAAM;IACf;IACA,OAAO,SAAS;EAClB,CAAC,EAAE,CAAChD,kBAAkB,EAAEK,KAAK,CAAC/D,QAAQ,CAAC,CAAC;EACxC,MAAMiJ,aAAa,GAAGnG,QAAQ,CAACgC,OAAO,IAAIhC,QAAQ,CAACgC,OAAO,KAAKzF,gBAAgB,CAACgG,QAAQ,CAAC;EACzF,MAAM6D,qBAAqB,GAAG,CAACD,aAAa,IAAItF,mBAAmB;EACnE7E,KAAK,CAACqK,mBAAmB,CAAC7F,gBAAgB,EAAE,OAAO;IACjD8F,WAAW,EAAEA,CAAA,KAAMrF,KAAK,CAAC/D,QAAQ;IACjCqJ,qBAAqB,EAAEA,CAAA,KAAM;MAC3B,MAAMxE,iBAAiB,GAAG/B,QAAQ,CAACgC,OAAO,CAACC,cAAc,IAAI,CAAC;MAC9D,MAAMuE,eAAe,GAAGxG,QAAQ,CAACgC,OAAO,CAACa,YAAY,IAAI,CAAC;MAC1D,IAAId,iBAAiB,KAAK,CAAC,IAAIyE,eAAe,KAAK,CAAC,EAAE;QACpD,OAAO,IAAI;MACb;MACA,MAAMtE,gBAAgB,GAAGH,iBAAiB,IAAI7E,QAAQ,CAAC,CAAC,CAAC,CAACgB,YAAY,GAAG,CAAC,CAAC;MAAA,EACzEhB,QAAQ,CAACiF,SAAS,CAACzE,OAAO,IAAIA,OAAO,CAACQ,YAAY,GAAGR,OAAO,CAACG,cAAc,CAACJ,MAAM,GAAGsE,iBAAiB,CAAC;MACzG,OAAOG,gBAAgB,KAAK,CAAC,CAAC,GAAGhF,QAAQ,CAACO,MAAM,GAAG,CAAC,GAAGyE,gBAAgB,GAAG,CAAC;IAC7E,CAAC;IACDZ,mBAAmB,EAAEmF,mBAAmB,IAAInF,mBAAmB,CAACmF,mBAAmB,CAAC;IACpFpE,UAAU;IACV6C,cAAc,EAAEA,CAAA,KAAMA,cAAc,CAAClF,QAAQ;EAC/C,CAAC,CAAC,CAAC;EACH,OAAOjE,QAAQ,CAAC,CAAC,CAAC,EAAEwD,cAAc,EAAE;IAClCuB,KAAK;IACLf,SAAS,EAAE2G,OAAO,CAAC3G,SAAS,IAAI,CAACc,mBAAmB,IAAI,CAACT,QAAQ,IAAI,CAACC,QAAQ,CAAC;IAC/ET,MAAM,EAAEmE,mBAAmB;IAC3BrE,OAAO,EAAEoD,gBAAgB;IACzBrD,OAAO,EAAEgD,gBAAgB;IACzB9C,OAAO,EAAEsD,gBAAgB;IACzBpD,SAAS,EAAEwF,6BAA6B;IACxCvF,OAAO,EAAEmF,WAAW;IACpBjF,QAAQ,EAAEU,SAAS;IACnB;IACAiG,iCAAiC,EAAE,KAAK;IACxCzG,WAAW;IACXgG,SAAS;IACTU,YAAY,EAAE,KAAK;IACnB1F,KAAK,EAAEkF,qBAAqB,GAAG,EAAE,GAAG7B,QAAQ;IAC5CsC,QAAQ,EAAE7C,iBAAiB;IAC3BzD,OAAO;IACPF,QAAQ;IACRD,QAAQ;IACRE,SAAS;IACTsB;EACF,CAAC,CAAC;AACJ,CAAC;AACD,SAASsD,cAAcA,CAAClF,QAAQ,EAAE;EAChC,OAAOA,QAAQ,CAACgC,OAAO,KAAKzF,gBAAgB,CAACgG,QAAQ,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}