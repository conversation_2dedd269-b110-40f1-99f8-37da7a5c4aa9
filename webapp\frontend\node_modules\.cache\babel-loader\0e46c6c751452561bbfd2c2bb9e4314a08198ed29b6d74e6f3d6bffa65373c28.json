{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\nimport { toDate } from \"../../../toDate.js\";\n\n// Adapted from the `ru` translation\n\nconst weekdays = [\"неделя\", \"понеделник\", \"вторник\", \"сряда\", \"четвъртък\", \"петък\", \"събота\"];\nfunction lastWeek(day) {\n  const weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'миналата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'миналия \" + weekday + \" в' p\";\n  }\n}\nfunction thisWeek(day) {\n  const weekday = weekdays[day];\n  if (day === 2 /* Tue */) {\n    return \"'във \" + weekday + \" в' p\";\n  } else {\n    return \"'в \" + weekday + \" в' p\";\n  }\n}\nfunction nextWeek(day) {\n  const weekday = weekdays[day];\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следващата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следващия \" + weekday + \" в' p\";\n  }\n}\nconst lastWeekFormatToken = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\nconst nextWeekFormatToken = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\nconst formatRelativeLocale = {\n  lastWeek: lastWeekFormatToken,\n  yesterday: \"'вчера в' p\",\n  today: \"'днес в' p\",\n  tomorrow: \"'утре в' p\",\n  nextWeek: nextWeekFormatToken,\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["isSameWeek", "toDate", "weekdays", "lastWeek", "day", "weekday", "thisWeek", "nextWeek", "lastWeekFormatToken", "dirtyDate", "baseDate", "options", "date", "getDay", "nextWeekFormatToken", "formatRelativeLocale", "yesterday", "today", "tomorrow", "other", "formatRelative", "token", "format"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/bg/_lib/formatRelative.js"], "sourcesContent": ["import { isSameWeek } from \"../../../isSameWeek.js\";\nimport { toDate } from \"../../../toDate.js\";\n\n// Adapted from the `ru` translation\n\nconst weekdays = [\n  \"неделя\",\n  \"понеделник\",\n  \"вторник\",\n  \"сряда\",\n  \"четвъртък\",\n  \"петък\",\n  \"събота\",\n];\n\nfunction lastWeek(day) {\n  const weekday = weekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'миналата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'миналия \" + weekday + \" в' p\";\n  }\n}\n\nfunction thisWeek(day) {\n  const weekday = weekdays[day];\n\n  if (day === 2 /* Tue */) {\n    return \"'във \" + weekday + \" в' p\";\n  } else {\n    return \"'в \" + weekday + \" в' p\";\n  }\n}\n\nfunction nextWeek(day) {\n  const weekday = weekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следващата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следващия \" + weekday + \" в' p\";\n  }\n}\n\nconst lastWeekFormatToken = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\n\nconst nextWeekFormatToken = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\n\nconst formatRelativeLocale = {\n  lastWeek: lastWeekFormatToken,\n  yesterday: \"'вчера в' p\",\n  today: \"'днес в' p\",\n  tomorrow: \"'утре в' p\",\n  nextWeek: nextWeekFormatToken,\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAwB;AACnD,SAASC,MAAM,QAAQ,oBAAoB;;AAE3C;;AAEA,MAAMC,QAAQ,GAAG,CACf,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,OAAO,EACP,WAAW,EACX,OAAO,EACP,QAAQ,CACT;AAED,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAE7B,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,YAAY,GAAGC,OAAO,GAAG,OAAO;IACzC,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,WAAW,GAAGA,OAAO,GAAG,OAAO;EAC1C;AACF;AAEA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAE7B,IAAIA,GAAG,KAAK,CAAC,CAAC,WAAW;IACvB,OAAO,OAAO,GAAGC,OAAO,GAAG,OAAO;EACpC,CAAC,MAAM;IACL,OAAO,KAAK,GAAGA,OAAO,GAAG,OAAO;EAClC;AACF;AAEA,SAASE,QAAQA,CAACH,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,QAAQ,CAACE,GAAG,CAAC;EAE7B,QAAQA,GAAG;IACT,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,cAAc,GAAGC,OAAO,GAAG,OAAO;IAC3C,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,OAAO,aAAa,GAAGA,OAAO,GAAG,OAAO;EAC5C;AACF;AAEA,MAAMG,mBAAmB,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EAC5D,MAAMC,IAAI,GAAGX,MAAM,CAACQ,SAAS,CAAC;EAC9B,MAAML,GAAG,GAAGQ,IAAI,CAACC,MAAM,CAAC,CAAC;EACzB,IAAIb,UAAU,CAACY,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC,EAAE;IACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;EACtB,CAAC,MAAM;IACL,OAAOD,QAAQ,CAACC,GAAG,CAAC;EACtB;AACF,CAAC;AAED,MAAMU,mBAAmB,GAAGA,CAACL,SAAS,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EAC5D,MAAMC,IAAI,GAAGX,MAAM,CAACQ,SAAS,CAAC;EAC9B,MAAML,GAAG,GAAGQ,IAAI,CAACC,MAAM,CAAC,CAAC;EACzB,IAAIb,UAAU,CAACY,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC,EAAE;IACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;EACtB,CAAC,MAAM;IACL,OAAOG,QAAQ,CAACH,GAAG,CAAC;EACtB;AACF,CAAC;AAED,MAAMW,oBAAoB,GAAG;EAC3BZ,QAAQ,EAAEK,mBAAmB;EAC7BQ,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,YAAY;EACtBX,QAAQ,EAAEO,mBAAmB;EAC7BK,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAET,IAAI,EAAEF,QAAQ,EAAEC,OAAO,KAAK;EAChE,MAAMW,MAAM,GAAGP,oBAAoB,CAACM,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACV,IAAI,EAAEF,QAAQ,EAAEC,OAAO,CAAC;EACxC;EAEA,OAAOW,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}