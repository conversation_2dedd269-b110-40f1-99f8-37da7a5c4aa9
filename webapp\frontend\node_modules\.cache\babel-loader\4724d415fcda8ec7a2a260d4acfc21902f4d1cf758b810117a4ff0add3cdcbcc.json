{"ast": null, "code": "import { formatDistance } from \"./en-US/_lib/formatDistance.js\";\nimport { formatRelative } from \"./en-US/_lib/formatRelative.js\";\nimport { localize } from \"./en-US/_lib/localize.js\";\nimport { match } from \"./en-US/_lib/match.js\";\nimport { formatLong } from \"./en-IN/_lib/formatLong.js\";\n\n/**\n * @category Locales\n * @summary English locale (India).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@gbhasha](https://github.com/gbhasha)\n */\nexport const enIN = {\n  code: \"en-IN\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1,\n    // Monday is the first day of the week.\n    firstWeekContainsDate: 4 // The week that contains Jan 4th is the first week of the year.\n  }\n};\n\n// Fallback for modularized imports:\nexport default enIN;", "map": {"version": 3, "names": ["formatDistance", "formatRelative", "localize", "match", "formatLong", "enIN", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/en-IN.js"], "sourcesContent": ["import { formatDistance } from \"./en-US/_lib/formatDistance.js\";\nimport { formatRelative } from \"./en-US/_lib/formatRelative.js\";\nimport { localize } from \"./en-US/_lib/localize.js\";\nimport { match } from \"./en-US/_lib/match.js\";\n\nimport { formatLong } from \"./en-IN/_lib/formatLong.js\";\n\n/**\n * @category Locales\n * @summary English locale (India).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@gbhasha](https://github.com/gbhasha)\n */\nexport const enIN = {\n  code: \"en-IN\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1, // Monday is the first day of the week.\n    firstWeekContainsDate: 4, // The week that contains Jan 4th is the first week of the year.\n  },\n};\n\n// Fallback for modularized imports:\nexport default enIN;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAE7C,SAASC,UAAU,QAAQ,4BAA4B;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BI,UAAU,EAAEA,UAAU;EACtBH,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZI,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC;IAAE;IACjBC,qBAAqB,EAAE,CAAC,CAAE;EAC5B;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}