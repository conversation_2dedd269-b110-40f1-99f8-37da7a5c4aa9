{"ast": null, "code": "import { formatDistance } from \"./th/_lib/formatDistance.js\";\nimport { formatLong } from \"./th/_lib/formatLong.js\";\nimport { formatRelative } from \"./th/_lib/formatRelative.js\";\nimport { localize } from \"./th/_lib/localize.js\";\nimport { match } from \"./th/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Thai locale.\n * @language Thai\n * @iso-639-2 tha\n * <AUTHOR> <PERSON> [@athivvat](https://github.com/athivvat)\n * <AUTHOR>\n * <AUTHOR> I. [@nodtem66](https://github.com/nodtem66)\n */\nexport const th = {\n  code: \"th\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default th;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "th", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/th.js"], "sourcesContent": ["import { formatDistance } from \"./th/_lib/formatDistance.js\";\nimport { formatLong } from \"./th/_lib/formatLong.js\";\nimport { formatRelative } from \"./th/_lib/formatRelative.js\";\nimport { localize } from \"./th/_lib/localize.js\";\nimport { match } from \"./th/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Thai locale.\n * @language Thai\n * @iso-639-2 tha\n * <AUTHOR> <PERSON> [@athivvat](https://github.com/athivvat)\n * <AUTHOR>\n * <AUTHOR> I. [@nodtem66](https://github.com/nodtem66)\n */\nexport const th = {\n  code: \"th\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default th;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}