{"ast": null, "code": "import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\n/**\n * Generate the props to pass to the hidden input element of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldHiddenInputPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldHiddenInputPropsReturnValue} The props to forward to the hidden input element of the field.\n */\nexport function useFieldHiddenInputProps(parameters) {\n  const {\n    manager: {\n      internal_fieldValueManager: fieldValueManager\n    },\n    stateResponse: {\n      // States and derived states\n      areAllSectionsEmpty,\n      state,\n      // Methods to update the states\n      updateValueFromValueStr\n    }\n  } = parameters;\n  const handleChange = useEventCallback(event => {\n    updateValueFromValueStr(event.target.value);\n  });\n  const valueStr = React.useMemo(() => areAllSectionsEmpty ? '' : fieldValueManager.getV7HiddenInputValueFromSections(state.sections), [areAllSectionsEmpty, state.sections, fieldValueManager]);\n  return {\n    value: valueStr,\n    onChange: handleChange\n  };\n}", "map": {"version": 3, "names": ["React", "useEventCallback", "useFieldHiddenInputProps", "parameters", "manager", "internal_fieldValueManager", "field<PERSON><PERSON>ueManager", "stateResponse", "areAllSectionsEmpty", "state", "updateValueFromValueStr", "handleChange", "event", "target", "value", "valueStr", "useMemo", "getV7HiddenInputValueFromSections", "sections", "onChange"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useFieldHiddenInputProps.js"], "sourcesContent": ["import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\n/**\n * Generate the props to pass to the hidden input element of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldHiddenInputPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldHiddenInputPropsReturnValue} The props to forward to the hidden input element of the field.\n */\nexport function useFieldHiddenInputProps(parameters) {\n  const {\n    manager: {\n      internal_fieldValueManager: fieldValueManager\n    },\n    stateResponse: {\n      // States and derived states\n      areAllSectionsEmpty,\n      state,\n      // Methods to update the states\n      updateValueFromValueStr\n    }\n  } = parameters;\n  const handleChange = useEventCallback(event => {\n    updateValueFromValueStr(event.target.value);\n  });\n  const valueStr = React.useMemo(() => areAllSectionsEmpty ? '' : fieldValueManager.getV7HiddenInputValueFromSections(state.sections), [areAllSectionsEmpty, state.sections, fieldValueManager]);\n  return {\n    value: valueStr,\n    onChange: handleChange\n  };\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,UAAU,EAAE;EACnD,MAAM;IACJC,OAAO,EAAE;MACPC,0BAA0B,EAAEC;IAC9B,CAAC;IACDC,aAAa,EAAE;MACb;MACAC,mBAAmB;MACnBC,KAAK;MACL;MACAC;IACF;EACF,CAAC,GAAGP,UAAU;EACd,MAAMQ,YAAY,GAAGV,gBAAgB,CAACW,KAAK,IAAI;IAC7CF,uBAAuB,CAACE,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7C,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGf,KAAK,CAACgB,OAAO,CAAC,MAAMR,mBAAmB,GAAG,EAAE,GAAGF,iBAAiB,CAACW,iCAAiC,CAACR,KAAK,CAACS,QAAQ,CAAC,EAAE,CAACV,mBAAmB,EAAEC,KAAK,CAACS,QAAQ,EAAEZ,iBAAiB,CAAC,CAAC;EAC9L,OAAO;IACLQ,KAAK,EAAEC,QAAQ;IACfI,QAAQ,EAAER;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}