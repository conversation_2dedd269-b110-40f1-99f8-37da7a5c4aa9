#!/usr/bin/env python3
"""
Test script per verificare la funzionalità di aggiornamento bobine.
Questo script testa la correzione del problema 'BobinaUpdate' object has no attribute 'force_over'.
"""

import requests
import json
import sys

# Configurazione
API_URL = "http://localhost:8002/api"
USERNAME = "a"
PASSWORD = "a"

def login():
    """Effettua il login e restituisce il token."""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{API_URL}/auth/login", data=login_data)
    if response.status_code == 200:
        token = response.json()["access_token"]
        print(f"✅ Login effettuato con successo")
        return token
    else:
        print(f"❌ Errore durante il login: {response.status_code}")
        print(response.text)
        return None

def get_cavi_installati(token, cantiere_id=1):
    """Ottiene la lista dei cavi installati."""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{API_URL}/cavi/{cantiere_id}?tipo_cavo=0", headers=headers)
    if response.status_code == 200:
        cavi = response.json()
        # Filtra solo i cavi installati
        cavi_installati = [c for c in cavi if c.get('stato_installazione') == 'Installato' and c.get('metratura_reale', 0) > 0]
        print(f"✅ Trovati {len(cavi_installati)} cavi installati")
        return cavi_installati
    else:
        print(f"❌ Errore durante il recupero dei cavi: {response.status_code}")
        print(response.text)
        return []

def get_bobine_disponibili(token, cantiere_id=1):
    """Ottiene la lista delle bobine disponibili."""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{API_URL}/parco-cavi/{cantiere_id}", headers=headers)
    if response.status_code == 200:
        bobine = response.json()
        # Filtra solo le bobine disponibili
        bobine_disponibili = [b for b in bobine if b.get('stato_bobina') in ['Disponibile', 'In uso']]
        print(f"✅ Trovate {len(bobine_disponibili)} bobine disponibili")
        return bobine_disponibili
    else:
        print(f"❌ Errore durante il recupero delle bobine: {response.status_code}")
        print(response.text)
        return []

def test_update_bobina(token, cantiere_id, cavo_id, bobina_id, force_over=True):
    """Testa l'aggiornamento della bobina di un cavo."""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "id_bobina": bobina_id,
        "force_over": force_over
    }
    
    print(f"\n🔄 Test aggiornamento bobina per cavo {cavo_id}")
    print(f"   Payload: {json.dumps(payload, indent=2)}")
    
    response = requests.post(
        f"{API_URL}/cavi/{cantiere_id}/{cavo_id}/bobina",
        headers=headers,
        json=payload
    )
    
    if response.status_code == 200:
        print(f"✅ Aggiornamento bobina riuscito!")
        print(f"   Risposta: {json.dumps(response.json(), indent=2)}")
        return True
    else:
        print(f"❌ Errore durante l'aggiornamento bobina: {response.status_code}")
        print(f"   Errore: {response.text}")
        return False

def main():
    """Funzione principale del test."""
    print("🧪 Test funzionalità aggiornamento bobine")
    print("=" * 50)
    
    # 1. Login
    token = login()
    if not token:
        sys.exit(1)
    
    # 2. Ottieni cavi installati
    cavi_installati = get_cavi_installati(token)
    if not cavi_installati:
        print("❌ Nessun cavo installato trovato per il test")
        sys.exit(1)
    
    # 3. Ottieni bobine disponibili
    bobine_disponibili = get_bobine_disponibili(token)
    if not bobine_disponibili:
        print("❌ Nessuna bobina disponibile trovata per il test")
        sys.exit(1)
    
    # 4. Seleziona un cavo e una bobina per il test
    cavo_test = cavi_installati[0]
    bobina_test = bobine_disponibili[0] if bobine_disponibili else None
    
    print(f"\n📋 Cavo selezionato per il test:")
    print(f"   ID: {cavo_test['id_cavo']}")
    print(f"   Stato: {cavo_test['stato_installazione']}")
    print(f"   Metri posati: {cavo_test['metratura_reale']}")
    print(f"   Bobina attuale: {cavo_test.get('id_bobina', 'Nessuna')}")
    
    if bobina_test:
        print(f"\n📦 Bobina selezionata per il test:")
        print(f"   ID: {bobina_test['id_bobina']}")
        print(f"   Stato: {bobina_test['stato_bobina']}")
        print(f"   Metri residui: {bobina_test['metri_residui']}")
    
    # 5. Test 1: Aggiornamento con bobina normale
    if bobina_test:
        print(f"\n🧪 TEST 1: Aggiornamento con bobina {bobina_test['id_bobina']}")
        success = test_update_bobina(token, 1, cavo_test['id_cavo'], bobina_test['id_bobina'], force_over=True)
        if not success:
            print("❌ Test 1 fallito")
            return
    
    # 6. Test 2: Aggiornamento con BOBINA_VUOTA
    print(f"\n🧪 TEST 2: Aggiornamento con BOBINA_VUOTA")
    success = test_update_bobina(token, 1, cavo_test['id_cavo'], "BOBINA_VUOTA", force_over=False)
    if not success:
        print("❌ Test 2 fallito")
        return
    
    print(f"\n✅ Tutti i test completati con successo!")
    print("🎉 La correzione del problema 'force_over' funziona correttamente!")

if __name__ == "__main__":
    main()
